{"_from": "@hapi/bourne@1.x.x", "_id": "@hapi/bourne@1.3.2", "_inBundle": false, "_integrity": "sha512-1dVNHT76Uu5N3eJNTYcvxee+jzX4Z9lfciqRRHCU27ihbUcYi+iSc2iml5Ke1LXe1SyJCLA0+14Jh4tXJgOppA==", "_location": "/@hapi/bourne", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/bourne@1.x.x", "name": "@hapi/bourne", "escapedName": "@hapi%2fbourne", "scope": "@hapi", "rawSpec": "1.x.x", "saveSpec": null, "fetchSpec": "1.x.x"}, "_requiredBy": ["/@hapi/joi"], "_resolved": "https://registry.npmmirror.com/@hapi/bourne/-/bourne-1.3.2.tgz", "_shasum": "0a7095adea067243ce3283e1b56b8a8f453b242a", "_spec": "@hapi/bourne@1.x.x", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@hapi/joi", "bugs": {"url": "https://github.com/hapijs/bourne/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "description": "JSON parse with prototype poisoning protection", "devDependencies": {"@hapi/code": "5.x.x", "@hapi/lab": "18.x.x", "benchmark": "^2.1.4"}, "homepage": "https://github.com/hapijs/bourne#readme", "keywords": ["JSON", "parse", "safe", "prototype"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/bourne", "repository": {"type": "git", "url": "git://github.com/hapijs/bourne.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "version": "1.3.2"}