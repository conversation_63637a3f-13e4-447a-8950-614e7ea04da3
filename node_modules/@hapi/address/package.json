{"_from": "@hapi/address@2.x.x", "_id": "@hapi/address@2.1.4", "_inBundle": false, "_integrity": "sha512-QD1PhQk+s31P1ixsX0H0Suoupp3VMXzIVMSwobR3F3MSUO2YCV0B7xqLcUw/Bh8yuvd3LhpyqLQWTNcRmp6IdQ==", "_location": "/@hapi/address", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/address@2.x.x", "name": "@hapi/address", "escapedName": "@hapi%2faddress", "scope": "@hapi", "rawSpec": "2.x.x", "saveSpec": null, "fetchSpec": "2.x.x"}, "_requiredBy": ["/@hapi/joi"], "_resolved": "https://registry.npmmirror.com/@hapi/address/-/address-2.1.4.tgz", "_shasum": "5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5", "_spec": "@hapi/address@2.x.x", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@hapi/joi", "bugs": {"url": "https://github.com/hapijs/address/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": "Moved to 'npm install @sideway/address'", "description": "Email address and domain validation", "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "homepage": "https://github.com/hapijs/address#readme", "keywords": ["email", "domain", "address", "validation"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/address", "repository": {"type": "git", "url": "git://github.com/hapijs/address.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "version": "2.1.4"}