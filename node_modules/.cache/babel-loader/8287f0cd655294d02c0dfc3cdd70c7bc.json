{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue?vue&type=template&id=13877386&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"app-wrapper\",\n    class: _vm.classObj\n  }, [_vm.classObj.mobile && _vm.sidebar.opened ? _c(\"div\", {\n    staticClass: \"drawer-bg\",\n    on: {\n      click: _vm.handleClickOutside\n    }\n  }) : _vm._e(), _c(\"sidebar\", {\n    staticClass: \"sidebar-container\"\n  }), _c(\"div\", {\n    staticClass: \"main-container\"\n  }, [_c(\"navbar\"), _c(\"app-main\")], 1)], 1);\n};\nvar staticRenderFns = exports.staticRenderFns = [];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "class", "classObj", "mobile", "sidebar", "opened", "on", "click", "handleClickOutside", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\n    \"div\",\n    { staticClass: \"app-wrapper\", class: _vm.classObj },\n    [\n      _vm.classObj.mobile && _vm.sidebar.opened\n        ? _c(\"div\", {\n            staticClass: \"drawer-bg\",\n            on: { click: _vm.handleClickOutside },\n          })\n        : _vm._e(),\n      _c(\"sidebar\", { staticClass: \"sidebar-container\" }),\n      _c(\n        \"div\",\n        { staticClass: \"main-container\" },\n        [_c(\"navbar\"), _c(\"app-main\")],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CACP,KAAK,EACL;IAAEI,WAAW,EAAE,aAAa;IAAEC,KAAK,EAAEN,GAAG,CAACO;EAAS,CAAC,EACnD,CACEP,GAAG,CAACO,QAAQ,CAACC,MAAM,IAAIR,GAAG,CAACS,OAAO,CAACC,MAAM,GACrCT,EAAE,CAAC,KAAK,EAAE;IACRI,WAAW,EAAE,WAAW;IACxBM,EAAE,EAAE;MAAEC,KAAK,EAAEZ,GAAG,CAACa;IAAmB;EACtC,CAAC,CAAC,GACFb,GAAG,CAACc,EAAE,CAAC,CAAC,EACZb,EAAE,CAAC,SAAS,EAAE;IAAEI,WAAW,EAAE;EAAoB,CAAC,CAAC,EACnDJ,EAAE,CACA,KAAK,EACL;IAAEI,WAAW,EAAE;EAAiB,CAAC,EACjC,CAACJ,EAAE,CAAC,QAAQ,CAAC,EAAEA,EAAE,CAAC,UAAU,CAAC,CAAC,EAC9B,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIc,eAAe,GAAAhB,OAAA,CAAAgB,eAAA,GAAG,EAAE;AACxBjB,MAAM,CAACkB,aAAa,GAAG,IAAI", "ignoreList": []}]}