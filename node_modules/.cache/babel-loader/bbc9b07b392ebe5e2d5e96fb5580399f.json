{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue?vue&type=template&id=33ec43fc&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", [_c(\"div\", {\n    staticClass: \"logo\"\n  }, [!_vm.isCollapse ? _c(\"div\", {\n    staticClass: \"sidebar-logo\"\n  }, [_c(\"img\", {\n    staticStyle: {\n      width: \"120px\",\n      height: \"31px\"\n    },\n    attrs: {\n      src: require(\"@/assets/login/logo.png\")\n    }\n  })]) : _c(\"div\", {\n    staticClass: \"sidebar-logo-mini\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/login/mini-logo.png\")\n    }\n  })])]), _c(\"el-scrollbar\", {\n    attrs: {\n      \"wrap-class\": \"scrollbar-wrapper\"\n    }\n  }, [_c(\"el-menu\", {\n    attrs: {\n      \"default-openeds\": _vm.defOpen,\n      \"default-active\": _vm.defAct,\n      collapse: _vm.isCollapse,\n      \"background-color\": _vm.variables.menuBg,\n      \"text-color\": _vm.variables.menuText,\n      \"active-text-color\": _vm.variables.menuActiveText,\n      \"unique-opened\": false,\n      \"collapse-transition\": false,\n      mode: \"vertical\"\n    }\n  }, _vm._l(_vm.routes, function (route) {\n    return _c(\"sidebar-item\", {\n      key: route.path,\n      attrs: {\n        item: route,\n        \"base-path\": route.path,\n        \"is-collapse\": _vm.isCollapse\n      }\n    });\n  }), 1)], 1)], 1);\n};\nvar staticRenderFns = exports.staticRenderFns = [];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "isCollapse", "staticStyle", "width", "height", "attrs", "src", "require", "defOpen", "defAct", "collapse", "variables", "menuBg", "menuText", "menuActiveText", "mode", "_l", "routes", "route", "key", "path", "item", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\n    \"div\",\n    [\n      _c(\"div\", { staticClass: \"logo\" }, [\n        !_vm.isCollapse\n          ? _c(\"div\", { staticClass: \"sidebar-logo\" }, [\n              _c(\"img\", {\n                staticStyle: { width: \"120px\", height: \"31px\" },\n                attrs: { src: require(\"@/assets/login/logo.png\") },\n              }),\n            ])\n          : _c(\"div\", { staticClass: \"sidebar-logo-mini\" }, [\n              _c(\"img\", {\n                attrs: { src: require(\"@/assets/login/mini-logo.png\") },\n              }),\n            ]),\n      ]),\n      _c(\n        \"el-scrollbar\",\n        { attrs: { \"wrap-class\": \"scrollbar-wrapper\" } },\n        [\n          _c(\n            \"el-menu\",\n            {\n              attrs: {\n                \"default-openeds\": _vm.defOpen,\n                \"default-active\": _vm.defAct,\n                collapse: _vm.isCollapse,\n                \"background-color\": _vm.variables.menuBg,\n                \"text-color\": _vm.variables.menuText,\n                \"active-text-color\": _vm.variables.menuActiveText,\n                \"unique-opened\": false,\n                \"collapse-transition\": false,\n                mode: \"vertical\",\n              },\n            },\n            _vm._l(_vm.routes, function (route) {\n              return _c(\"sidebar-item\", {\n                key: route.path,\n                attrs: {\n                  item: route,\n                  \"base-path\": route.path,\n                  \"is-collapse\": _vm.isCollapse,\n                },\n              })\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjC,CAACL,GAAG,CAACM,UAAU,GACXL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCJ,EAAE,CAAC,KAAK,EAAE;IACRM,WAAW,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAO,CAAC;IAC/CC,KAAK,EAAE;MAAEC,GAAG,EAAEC,OAAO,CAAC,yBAAyB;IAAE;EACnD,CAAC,CAAC,CACH,CAAC,GACFX,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CJ,EAAE,CAAC,KAAK,EAAE;IACRS,KAAK,EAAE;MAAEC,GAAG,EAAEC,OAAO,CAAC,8BAA8B;IAAE;EACxD,CAAC,CAAC,CACH,CAAC,CACP,CAAC,EACFX,EAAE,CACA,cAAc,EACd;IAAES,KAAK,EAAE;MAAE,YAAY,EAAE;IAAoB;EAAE,CAAC,EAChD,CACET,EAAE,CACA,SAAS,EACT;IACES,KAAK,EAAE;MACL,iBAAiB,EAAEV,GAAG,CAACa,OAAO;MAC9B,gBAAgB,EAAEb,GAAG,CAACc,MAAM;MAC5BC,QAAQ,EAAEf,GAAG,CAACM,UAAU;MACxB,kBAAkB,EAAEN,GAAG,CAACgB,SAAS,CAACC,MAAM;MACxC,YAAY,EAAEjB,GAAG,CAACgB,SAAS,CAACE,QAAQ;MACpC,mBAAmB,EAAElB,GAAG,CAACgB,SAAS,CAACG,cAAc;MACjD,eAAe,EAAE,KAAK;MACtB,qBAAqB,EAAE,KAAK;MAC5BC,IAAI,EAAE;IACR;EACF,CAAC,EACDpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACsB,MAAM,EAAE,UAAUC,KAAK,EAAE;IAClC,OAAOtB,EAAE,CAAC,cAAc,EAAE;MACxBuB,GAAG,EAAED,KAAK,CAACE,IAAI;MACff,KAAK,EAAE;QACLgB,IAAI,EAAEH,KAAK;QACX,WAAW,EAAEA,KAAK,CAACE,IAAI;QACvB,aAAa,EAAEzB,GAAG,CAACM;MACrB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIqB,eAAe,GAAA5B,OAAA,CAAA4B,eAAA,GAAG,EAAE;AACxB7B,MAAM,CAAC8B,aAAa,GAAG,IAAI", "ignoreList": []}]}