{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"el-breadcrumb\", {\n    staticClass: \"app-breadcrumb\",\n    attrs: {\n      separator: \"/\"\n    }\n  }, [_c(\"transition-group\", {\n    attrs: {\n      name: \"breadcrumb\"\n    }\n  }, _vm._l(_vm.breadcrumbs, function (item, index) {\n    return _c(\"el-breadcrumb-item\", {\n      key: item.path\n    }, [item.redirect === \"noredirect\" || index === _vm.breadcrumbs.length - 1 ? _c(\"span\", {\n      staticClass: \"no-redirect\"\n    }, [_vm._v(_vm._s(item.meta.title))]) : _c(\"a\", {\n      on: {\n        click: function click($event) {\n          $event.preventDefault();\n          return _vm.handleLink(item);\n        }\n      }\n    }, [_vm._v(_vm._s(item.meta.title))])]);\n  }), 1)], 1);\n};\nvar staticRenderFns = exports.staticRenderFns = [];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "attrs", "separator", "name", "_l", "breadcrumbs", "item", "index", "key", "path", "redirect", "length", "_v", "_s", "meta", "title", "on", "click", "$event", "preventDefault", "handleLink", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\n    \"el-breadcrumb\",\n    { staticClass: \"app-breadcrumb\", attrs: { separator: \"/\" } },\n    [\n      _c(\n        \"transition-group\",\n        { attrs: { name: \"breadcrumb\" } },\n        _vm._l(_vm.breadcrumbs, function (item, index) {\n          return _c(\"el-breadcrumb-item\", { key: item.path }, [\n            item.redirect === \"noredirect\" ||\n            index === _vm.breadcrumbs.length - 1\n              ? _c(\"span\", { staticClass: \"no-redirect\" }, [\n                  _vm._v(_vm._s(item.meta.title)),\n                ])\n              : _c(\n                  \"a\",\n                  {\n                    on: {\n                      click: function ($event) {\n                        $event.preventDefault()\n                        return _vm.handleLink(item)\n                      },\n                    },\n                  },\n                  [_vm._v(_vm._s(item.meta.title))]\n                ),\n          ])\n        }),\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CACP,eAAe,EACf;IAAEI,WAAW,EAAE,gBAAgB;IAAEC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAI;EAAE,CAAC,EAC5D,CACEN,EAAE,CACA,kBAAkB,EAClB;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAa;EAAE,CAAC,EACjCR,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,WAAW,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC7C,OAAOX,EAAE,CAAC,oBAAoB,EAAE;MAAEY,GAAG,EAAEF,IAAI,CAACG;IAAK,CAAC,EAAE,CAClDH,IAAI,CAACI,QAAQ,KAAK,YAAY,IAC9BH,KAAK,KAAKZ,GAAG,CAACU,WAAW,CAACM,MAAM,GAAG,CAAC,GAChCf,EAAE,CAAC,MAAM,EAAE;MAAEI,WAAW,EAAE;IAAc,CAAC,EAAE,CACzCL,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACkB,EAAE,CAACP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAAC,CAAC,CAChC,CAAC,GACFnB,EAAE,CACA,GAAG,EACH;MACEoB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,CAAC,CAAC;UACvB,OAAOxB,GAAG,CAACyB,UAAU,CAACd,IAAI,CAAC;QAC7B;MACF;IACF,CAAC,EACD,CAACX,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACkB,EAAE,CAACP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAAC,CAAC,CAClC,CAAC,CACN,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIM,eAAe,GAAA3B,OAAA,CAAA2B,eAAA,GAAG,EAAE;AACxB5B,MAAM,CAAC6B,aAAa,GAAG,IAAI", "ignoreList": []}]}