{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItemLink.vue?vue&type=template&id=68520cb9", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItemLink.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _vm.isExternal(_vm.to) ? _c(\"a\", {\n    attrs: {\n      href: _vm.to,\n      target: \"_blank\",\n      rel: \"noopener\"\n    }\n  }, [_vm._t(\"default\")], 2) : _c(\"router-link\", {\n    attrs: {\n      to: _vm.to\n    }\n  }, [_vm._t(\"default\")], 2);\n};\nvar staticRenderFns = exports.staticRenderFns = [];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "isExternal", "to", "attrs", "href", "target", "rel", "_t", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItemLink.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _vm.isExternal(_vm.to)\n    ? _c(\n        \"a\",\n        { attrs: { href: _vm.to, target: \"_blank\", rel: \"noopener\" } },\n        [_vm._t(\"default\")],\n        2\n      )\n    : _c(\"router-link\", { attrs: { to: _vm.to } }, [_vm._t(\"default\")], 2)\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOJ,GAAG,CAACK,UAAU,CAACL,GAAG,CAACM,EAAE,CAAC,GACzBL,EAAE,CACA,GAAG,EACH;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAER,GAAG,CAACM,EAAE;MAAEG,MAAM,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAW;EAAE,CAAC,EAC9D,CAACV,GAAG,CAACW,EAAE,CAAC,SAAS,CAAC,CAAC,EACnB,CACF,CAAC,GACDV,EAAE,CAAC,aAAa,EAAE;IAAEM,KAAK,EAAE;MAAED,EAAE,EAAEN,GAAG,CAACM;IAAG;EAAE,CAAC,EAAE,CAACN,GAAG,CAACW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1E,CAAC;AACD,IAAIC,eAAe,GAAAb,OAAA,CAAAa,eAAA,GAAG,EAAE;AACxBd,MAAM,CAACe,aAAa,GAAG,IAAI", "ignoreList": []}]}