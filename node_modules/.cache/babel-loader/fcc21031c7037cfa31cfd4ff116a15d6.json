{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/404.vue?vue&type=template&id=6b3aeb0e&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/404.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"wscn-http404-container\"\n  }, [_c(\"div\", {\n    staticClass: \"wscn-http404\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"text-404\"\n  }, [_c(\"div\", {\n    staticClass: \"text-404__oops\"\n  }, [_vm._v(\"\\n        OOPS!\\n      \")]), _vm._m(1), _c(\"div\", {\n    staticClass: \"text-404__headline\"\n  }, [_vm._v(\"\\n        \" + _vm._s(_vm.message) + \"\\n      \")]), _c(\"div\", {\n    staticClass: \"text-404__info\"\n  }, [_vm._v(\"\\n        Please check that the URL you entered is correct, or click the button below to return to the homepage.\\n      \")]), _c(\"a\", {\n    staticClass: \"text-404__return-home\",\n    attrs: {\n      href: \"\"\n    }\n  }, [_vm._v(\"Back to home\")])])])]);\n};\nvar staticRenderFns = exports.staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"pic-404\"\n  }, [_c(\"img\", {\n    staticClass: \"pic-404__parent\",\n    attrs: {\n      src: require(\"@/assets/404-images/404.png\"),\n      alt: \"404\"\n    }\n  }), _c(\"img\", {\n    staticClass: \"pic-404__child left\",\n    attrs: {\n      src: require(\"@/assets/404-images/404-cloud.png\"),\n      alt: \"404\"\n    }\n  }), _c(\"img\", {\n    staticClass: \"pic-404__child mid\",\n    attrs: {\n      src: require(\"@/assets/404-images/404-cloud.png\"),\n      alt: \"404\"\n    }\n  }), _c(\"img\", {\n    staticClass: \"pic-404__child right\",\n    attrs: {\n      src: require(\"@/assets/404-images/404-cloud.png\"),\n      alt: \"404\"\n    }\n  })]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"text-404__info\"\n  }, [_vm._v(\"\\n        All rights reserved\\n        \"), _c(\"a\", {\n    staticStyle: {\n      color: \"#20a0ff\"\n    },\n    attrs: {\n      href: \"https://wallstreetcn.com\",\n      target: \"_blank\"\n    }\n  }, [_vm._v(\"wallstreetcn\")])]);\n}];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "_m", "_v", "_s", "message", "attrs", "href", "staticRenderFns", "src", "require", "alt", "staticStyle", "color", "target", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/404.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\"div\", { staticClass: \"wscn-http404-container\" }, [\n    _c(\"div\", { staticClass: \"wscn-http404\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"text-404\" }, [\n        _c(\"div\", { staticClass: \"text-404__oops\" }, [\n          _vm._v(\"\\n        OOPS!\\n      \"),\n        ]),\n        _vm._m(1),\n        _c(\"div\", { staticClass: \"text-404__headline\" }, [\n          _vm._v(\"\\n        \" + _vm._s(_vm.message) + \"\\n      \"),\n        ]),\n        _c(\"div\", { staticClass: \"text-404__info\" }, [\n          _vm._v(\n            \"\\n        Please check that the URL you entered is correct, or click the button below to return to the homepage.\\n      \"\n          ),\n        ]),\n        _c(\"a\", { staticClass: \"text-404__return-home\", attrs: { href: \"\" } }, [\n          _vm._v(\"Back to home\"),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c,\n      _setup = _vm._self._setupProxy\n    return _c(\"div\", { staticClass: \"pic-404\" }, [\n      _c(\"img\", {\n        staticClass: \"pic-404__parent\",\n        attrs: { src: require(\"@/assets/404-images/404.png\"), alt: \"404\" },\n      }),\n      _c(\"img\", {\n        staticClass: \"pic-404__child left\",\n        attrs: {\n          src: require(\"@/assets/404-images/404-cloud.png\"),\n          alt: \"404\",\n        },\n      }),\n      _c(\"img\", {\n        staticClass: \"pic-404__child mid\",\n        attrs: {\n          src: require(\"@/assets/404-images/404-cloud.png\"),\n          alt: \"404\",\n        },\n      }),\n      _c(\"img\", {\n        staticClass: \"pic-404__child right\",\n        attrs: {\n          src: require(\"@/assets/404-images/404-cloud.png\"),\n          alt: \"404\",\n        },\n      }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c,\n      _setup = _vm._self._setupProxy\n    return _c(\"div\", { staticClass: \"text-404__info\" }, [\n      _vm._v(\"\\n        All rights reserved\\n        \"),\n      _c(\n        \"a\",\n        {\n          staticStyle: { color: \"#20a0ff\" },\n          attrs: { href: \"https://wallstreetcn.com\", target: \"_blank\" },\n        },\n        [_vm._v(\"wallstreetcn\")]\n      ),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAyB,CAAC,EAAE,CAC1DJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCL,GAAG,CAACM,EAAE,CAAC,CAAC,CAAC,EACTL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CL,GAAG,CAACO,EAAE,CAAC,yBAAyB,CAAC,CAClC,CAAC,EACFP,GAAG,CAACM,EAAE,CAAC,CAAC,CAAC,EACTL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CL,GAAG,CAACO,EAAE,CAAC,YAAY,GAAGP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,OAAO,CAAC,GAAG,UAAU,CAAC,CACxD,CAAC,EACFR,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CL,GAAG,CAACO,EAAE,CACJ,0HACF,CAAC,CACF,CAAC,EACFN,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE,uBAAuB;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAG;EAAE,CAAC,EAAE,CACrEX,GAAG,CAACO,EAAE,CAAC,cAAc,CAAC,CACvB,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIK,eAAe,GAAAb,OAAA,CAAAa,eAAA,GAAG,CACpB,YAAY;EACV,IAAIZ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CAC3CJ,EAAE,CAAC,KAAK,EAAE;IACRI,WAAW,EAAE,iBAAiB;IAC9BK,KAAK,EAAE;MAAEG,GAAG,EAAEC,OAAO,CAAC,6BAA6B,CAAC;MAAEC,GAAG,EAAE;IAAM;EACnE,CAAC,CAAC,EACFd,EAAE,CAAC,KAAK,EAAE;IACRI,WAAW,EAAE,qBAAqB;IAClCK,KAAK,EAAE;MACLG,GAAG,EAAEC,OAAO,CAAC,mCAAmC,CAAC;MACjDC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFd,EAAE,CAAC,KAAK,EAAE;IACRI,WAAW,EAAE,oBAAoB;IACjCK,KAAK,EAAE;MACLG,GAAG,EAAEC,OAAO,CAAC,mCAAmC,CAAC;MACjDC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFd,EAAE,CAAC,KAAK,EAAE;IACRI,WAAW,EAAE,sBAAsB;IACnCK,KAAK,EAAE;MACLG,GAAG,EAAEC,OAAO,CAAC,mCAAmC,CAAC;MACjDC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIf,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDL,GAAG,CAACO,EAAE,CAAC,yCAAyC,CAAC,EACjDN,EAAE,CACA,GAAG,EACH;IACEe,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAU,CAAC;IACjCP,KAAK,EAAE;MAAEC,IAAI,EAAE,0BAA0B;MAAEO,MAAM,EAAE;IAAS;EAC9D,CAAC,EACD,CAAClB,GAAG,CAACO,EAAE,CAAC,cAAc,CAAC,CACzB,CAAC,CACF,CAAC;AACJ,CAAC,CACF;AACDT,MAAM,CAACqB,aAAa,GAAG,IAAI", "ignoreList": []}]}