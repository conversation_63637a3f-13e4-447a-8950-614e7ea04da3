{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/icons/components/index.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/icons/components/index.ts", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nrequire(\"./main\");\nrequire(\"./employee\");\nrequire(\"./pay\");\nrequire(\"./shop\");\nrequire(\"./vip\");\nrequire(\"./hamburger\");\nrequire(\"./dashboard\");\nrequire(\"./inform\");", {"version": 3, "names": ["require"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/icons/components/index.ts"], "sourcesContent": ["/* tslint:disable */\r\nimport './main'\r\nimport './employee'\r\nimport './pay'\r\nimport './shop'\r\nimport './vip'\r\nimport './hamburger'\r\nimport './dashboard'\r\nimport './inform'\r\n"], "mappings": ";;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA", "ignoreList": []}]}