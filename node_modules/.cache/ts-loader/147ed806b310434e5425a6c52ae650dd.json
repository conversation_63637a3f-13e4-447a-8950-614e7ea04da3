{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/inform.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/inform.ts", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setStatus = exports.getInformData = exports.getCountUnread = exports.batchMsg = void 0;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 获取列表数据\nvar getInformData = exports.getInformData = function getInformData(params) {\n  return (0, _request.default)({\n    url: '/messages/page',\n    method: 'get',\n    params: params\n  });\n};\n// 获取未读\nvar getCountUnread = exports.getCountUnread = function getCountUnread() {\n  return (0, _request.default)({\n    url: '/messages/countUnread',\n    method: 'get'\n  });\n};\n// 全部已读\nvar batchMsg = exports.batchMsg = function batchMsg(data) {\n  return (0, _request.default)({\n    url: '/messages/batch',\n    method: 'put',\n    data: data\n  });\n};\n// 标记已读\nvar setStatus = exports.setStatus = function setStatus(params) {\n  return (0, _request.default)({\n    url: \"/messages/\".concat(params),\n    method: 'PUT'\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getInformData", "exports", "params", "request", "url", "method", "getCountUnread", "batchMsg", "data", "setStatus", "concat"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/inform.ts"], "sourcesContent": ["import request from '@/utils/request'\r\n  // 获取列表数据\r\n  export const getInformData = (params: any) => {\r\n    return request({\r\n      url: '/messages/page',\r\n      method: 'get',\r\n      params,\r\n    },)\r\n  }\r\n  // 获取未读\r\n  export const getCountUnread = () => {\r\n    return request({\r\n      url: '/messages/countUnread',\r\n      method: 'get'\r\n    },)\r\n  }\r\n  // 全部已读\r\n  export const batchMsg = (data: any) => {\r\n    return request({\r\n      url: '/messages/batch',\r\n      method: 'put',\r\n      data\r\n    })\r\n  }\r\n    // 标记已读\r\n    export const setStatus = (params: any) => {\r\n      return request({\r\n        url: `/messages/${params}`,\r\n        method: 'PUT'\r\n      })\r\n    }"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACE;AACO,IAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,SAAhBA,aAAaA,CAAIE,MAAW,EAAI;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAE;AACL,CAAC;AACD;AACO,IAAMI,cAAc,GAAAL,OAAA,CAAAK,cAAA,GAAG,SAAjBA,cAAcA,CAAA,EAAQ;EACjC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;GACT,CAAE;AACL,CAAC;AACD;AACO,IAAME,QAAQ,GAAAN,OAAA,CAAAM,QAAA,GAAG,SAAXA,QAAQA,CAAIC,IAAS,EAAI;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbG,IAAI,EAAJA;GACD,CAAC;AACJ,CAAC;AACC;AACO,IAAMC,SAAS,GAAAR,OAAA,CAAAQ,SAAA,GAAG,SAAZA,SAASA,CAAIP,MAAW,EAAI;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,eAAAM,MAAA,CAAeR,MAAM,CAAE;IAC1BG,MAAM,EAAE;GACT,CAAC;AACJ,CAAC", "ignoreList": []}]}