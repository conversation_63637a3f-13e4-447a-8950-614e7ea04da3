{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/request.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/request.ts", "mtime": 1691719399000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.replace\");\nvar _axios = _interopRequireDefault(require(\"axios\"));\nvar _user = require(\"@/store/modules/user\");\nvar _requestOptimize = require(\"./requestOptimize\");\nvar _router = _interopRequireDefault(require(\"@/router\"));\nvar service = _axios.default.create({\n  baseURL: process.env.VUE_APP_BASE_API,\n  'timeout': 600000\n});\n// Request interceptors\nservice.interceptors.request.use(function (config) {\n  // Add X-Access-Token header to every request, you can add other custom headers here\n  if (_user.UserModule.token) {\n    config.headers['token'] = _user.UserModule.token;\n  } else if (_user.UserModule.token && config.url != '/login') {\n    window.location.href = '/login';\n    return false;\n  }\n  return config;\n}, function (error) {\n  Promise.reject(error);\n});\n// Response interceptors\nservice.interceptors.response.use(function (response) {\n  // console.log(response, 'response')\n  if (response.data.status === 401) {\n    _router.default.push('/login');\n  }\n  //请求响应中的config的url会带上代理的api需要去掉\n  response.config.url = response.config.url.replace('/api', '');\n  // 请求完成，删除请求中状态\n  var key = (0, _requestOptimize.getRequestKey)(response.config);\n  (0, _requestOptimize.removePending)(key);\n  if (response.data.code === 1) {\n    return response;\n  }\n  return response;\n}, function (error) {\n  // console.log(error.config, pending, 'error')\n  if (error && error.response) {\n    switch (error.response.status) {\n      case 401:\n        _router.default.push('/login');\n        break;\n      case 405:\n        error.message = '请求错误';\n    }\n  }\n  //请求响应中的config的url会带上代理的api需要去掉\n  error.config.url = error.config.url.replace('/api', '');\n  // 请求完成，删除请求中状态\n  var key = (0, _requestOptimize.getRequestKey)(error.config);\n  (0, _requestOptimize.removePending)(key);\n  return Promise.reject(error);\n});\nvar _default = exports.default = service;", {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "_user", "_requestOptimize", "_router", "service", "axios", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "interceptors", "request", "use", "config", "UserModule", "token", "headers", "url", "window", "location", "href", "error", "Promise", "reject", "response", "data", "status", "router", "push", "replace", "key", "getRequestKey", "removePending", "code", "message", "_default", "exports", "default"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/request.ts"], "sourcesContent": ["import axios from 'axios'\nimport { UserModule } from '@/store/modules/user'\nimport {getRequestKey,removePending} from './requestOptimize'\nimport router from '@/router'\n\nconst service = axios.create({\n  baseURL: process.env.VUE_APP_BASE_API,\n  'timeout': 600000\n})\n\n// Request interceptors\nservice.interceptors.request.use(\n  (config: any) => {\n    // Add X-Access-Token header to every request, you can add other custom headers here\n    if (UserModule.token) {\n      config.headers['token'] = UserModule.token\n    } else if (UserModule.token && config.url != '/login') {\n      window.location.href = '/login'\n      return false\n    }\n    return config\n  },\n  (error: any) => {\n    Promise.reject(error)\n  }\n)\n\n// Response interceptors\nservice.interceptors.response.use(\n  (response: any) => {\n    // console.log(response, 'response')\n    if (response.data.status === 401) {\n      router.push('/login')\n    }\n    //请求响应中的config的url会带上代理的api需要去掉\n    response.config.url = response.config.url.replace('/api', '')\n    // 请求完成，删除请求中状态\n    const key = getRequestKey(response.config);\n    removePending(key);\n    if (response.data.code === 1) {\n      return response\n    }\n    return response\n  },\n  (error: any) => {\n    // console.log(error.config, pending, 'error')\n    if (error && error.response) {\n      switch (error.response.status) {\n        case 401:\n          router.push('/login')\n          break;\n        case 405:\n          error.message = '请求错误'\n      }\n    }\n    //请求响应中的config的url会带上代理的api需要去掉\n    error.config.url = error.config.url.replace('/api', '')\n    // 请求完成，删除请求中状态\n    const key = getRequestKey(error.config);\n    removePending(key);\n    return Promise.reject(error)\n  }\n)\n\nexport default service\n"], "mappings": ";;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAMI,OAAO,GAAGC,cAAK,CAACC,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC,SAAS,EAAE;CACZ,CAAC;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAACC,MAAW,EAAI;EACd;EACA,IAAIC,gBAAU,CAACC,KAAK,EAAE;IACpBF,MAAM,CAACG,OAAO,CAAC,OAAO,CAAC,GAAGF,gBAAU,CAACC,KAAK;GAC3C,MAAM,IAAID,gBAAU,CAACC,KAAK,IAAIF,MAAM,CAACI,GAAG,IAAI,QAAQ,EAAE;IACrDC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IAC/B,OAAO,KAAK;;EAEd,OAAOP,MAAM;AACf,CAAC,EACD,UAACQ,KAAU,EAAI;EACbC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AACvB,CAAC,CACF;AAED;AACAlB,OAAO,CAACO,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC/B,UAACY,QAAa,EAAI;EAChB;EACA,IAAIA,QAAQ,CAACC,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;IAChCC,eAAM,CAACC,IAAI,CAAC,QAAQ,CAAC;;EAEvB;EACAJ,QAAQ,CAACX,MAAM,CAACI,GAAG,GAAGO,QAAQ,CAACX,MAAM,CAACI,GAAG,CAACY,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC7D;EACA,IAAMC,GAAG,GAAG,IAAAC,8BAAa,EAACP,QAAQ,CAACX,MAAM,CAAC;EAC1C,IAAAmB,8BAAa,EAACF,GAAG,CAAC;EAClB,IAAIN,QAAQ,CAACC,IAAI,CAACQ,IAAI,KAAK,CAAC,EAAE;IAC5B,OAAOT,QAAQ;;EAEjB,OAAOA,QAAQ;AACjB,CAAC,EACD,UAACH,KAAU,EAAI;EACb;EACA,IAAIA,KAAK,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAC3B,QAAQH,KAAK,CAACG,QAAQ,CAACE,MAAM;MAC3B,KAAK,GAAG;QACNC,eAAM,CAACC,IAAI,CAAC,QAAQ,CAAC;QACrB;MACF,KAAK,GAAG;QACNP,KAAK,CAACa,OAAO,GAAG,MAAM;;;EAG5B;EACAb,KAAK,CAACR,MAAM,CAACI,GAAG,GAAGI,KAAK,CAACR,MAAM,CAACI,GAAG,CAACY,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EACvD;EACA,IAAMC,GAAG,GAAG,IAAAC,8BAAa,EAACV,KAAK,CAACR,MAAM,CAAC;EACvC,IAAAmB,8BAAa,EAACF,GAAG,CAAC;EAClB,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CACF;AAAA,IAAAc,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEclC,OAAO", "ignoreList": []}]}