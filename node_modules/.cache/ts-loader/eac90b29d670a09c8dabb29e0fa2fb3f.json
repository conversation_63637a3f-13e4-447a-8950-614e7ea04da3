{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/employee.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/employee.ts", "mtime": 1756349952450}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.userLogout = exports.login = exports.getEmployeeList = exports.enableOrDisableEmployee = void 0;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n/**\n *\n * 员工管理\n *\n **/\n// 登录\nvar login = exports.login = function login(data) {\n  return (0, _request.default)({\n    'url': '/employee/login',\n    'method': 'post',\n    data: data\n  });\n};\n// 退出\nvar userLogout = exports.userLogout = function userLogout(params) {\n  return (0, _request.default)({\n    'url': \"/employee/logout\",\n    'method': 'post',\n    params: params\n  });\n};\n//分页查询\nvar getEmployeeList = exports.getEmployeeList = function getEmployeeList(params) {\n  return (0, _request.default)({\n    'url': \"/employee/page\",\n    'method': 'get',\n    'params': params\n  });\n};\n//启用禁用\nvar enableOrDisableEmployee = exports.enableOrDisableEmployee = function enableOrDisableEmployee(params) {\n  return (0, _request.default)({\n    'url': \"/employee/status/\".concat(params.status),\n    'method': 'post',\n    'params': {\n      id: params.id\n    }\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "login", "exports", "data", "request", "userLogout", "params", "getEmployeeList", "enableOrDisableEmployee", "concat", "status", "id"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/employee.ts"], "sourcesContent": ["import request from '@/utils/request'\r\n/**\r\n *\r\n * 员工管理\r\n *\r\n **/\r\n// 登录\r\nexport const login = (data: any) =>\r\n  request({\r\n    'url': '/employee/login',\r\n    'method': 'post',\r\n    data: data\r\n  })\r\n\r\n  // 退出\r\n export const userLogout = (params: any) =>\r\n request({\r\n   'url': `/employee/logout`,\r\n   'method': 'post',\r\n   params\r\n })\r\n\r\n //分页查询\r\n export const getEmployeeList = (params: any) =>\r\n  request({\r\n    'url': `/employee/page`,\r\n    'method': 'get',\r\n    'params': params\r\n  })\r\n  //启用禁用\r\n  export const enableOrDisableEmployee = (params: any) =>\r\n  request({\r\n    'url': `/employee/status/${params.status}`,\r\n    'method': 'post',\r\n    'params': {id:params.id}\r\n  })"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA;;;;;AAKA;AACO,IAAMC,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAG,SAARA,KAAKA,CAAIE,IAAS;EAAA,OAC7B,IAAAC,gBAAO,EAAC;IACN,KAAK,EAAE,iBAAiB;IACxB,QAAQ,EAAE,MAAM;IAChBD,IAAI,EAAEA;GACP,CAAC;AAAA;AAEF;AACM,IAAME,UAAU,GAAAH,OAAA,CAAAG,UAAA,GAAG,SAAbA,UAAUA,CAAIC,MAAW;EAAA,OACtC,IAAAF,gBAAO,EAAC;IACN,KAAK,oBAAoB;IACzB,QAAQ,EAAE,MAAM;IAChBE,MAAM,EAANA;GACD,CAAC;AAAA;AAEF;AACO,IAAMC,eAAe,GAAAL,OAAA,CAAAK,eAAA,GAAG,SAAlBA,eAAeA,CAAID,MAAW;EAAA,OAC1C,IAAAF,gBAAO,EAAC;IACN,KAAK,kBAAkB;IACvB,QAAQ,EAAE,KAAK;IACf,QAAQ,EAAEE;GACX,CAAC;AAAA;AACF;AACO,IAAME,uBAAuB,GAAAN,OAAA,CAAAM,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAIF,MAAW;EAAA,OACnD,IAAAF,gBAAO,EAAC;IACN,KAAK,sBAAAK,MAAA,CAAsBH,MAAM,CAACI,MAAM,CAAE;IAC1C,QAAQ,EAAE,MAAM;IAChB,QAAQ,EAAE;MAACC,EAAE,EAACL,MAAM,CAACK;IAAE;GACxB,CAAC;AAAA", "ignoreList": []}]}