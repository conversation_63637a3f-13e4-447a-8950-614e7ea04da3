{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es7.object.get-own-property-descriptors\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.object.keys\");\nrequire(\"core-js/modules/es7.symbol.async-iterator\");\nrequire(\"core-js/modules/es6.symbol\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.array.from\");\nrequire(\"core-js/modules/es6.function.name\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/defineProperty.js\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _path = _interopRequireDefault(require(\"path\"));\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _user = require(\"@/store/modules/user\");\nvar _validate = require(\"@/utils/validate\");\nvar _SidebarItemLink = _interopRequireDefault(require(\"./SidebarItemLink.vue\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    (0, _classCallCheck2.default)(this, default_1);\n    return _callSuper(this, default_1, arguments);\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"showingChildNumber\",\n    get: function get() {\n      if (this.item.children) {\n        var showingChildren = this.item.children.filter(function (item) {\n          if (item.meta && item.meta.hidden) {\n            return false;\n          }\n          return true;\n        });\n        return showingChildren.length;\n      }\n      return 0;\n    }\n  }, {\n    key: \"roles\",\n    get: function get() {\n      return _user.UserModule.roles;\n    }\n  }, {\n    key: \"theOnlyOneChild\",\n    get: function get() {\n      if (this.showingChildNumber > 0) {\n        return null;\n      }\n      if (this.item.children) {\n        var _iterator = _createForOfIteratorHelper(this.item.children),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var child = _step.value;\n            if (!child.meta || !child.meta.hidden) {\n              return child;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      }\n      // If there is no children, return itself with path removed,\n      // because this.basePath already conatins item's path information\n      return _objectSpread(_objectSpread({}, this.item), {}, {\n        path: ''\n      });\n    }\n  }, {\n    key: \"resolvePath\",\n    value: function resolvePath(routePath) {\n      if ((0, _validate.isExternal)(routePath)) {\n        return routePath;\n      }\n      if ((0, _validate.isExternal)(this.basePath)) {\n        return this.basePath;\n      }\n      return _path.default.resolve(this.basePath, routePath);\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  required: true\n})], default_1.prototype, \"item\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: false\n})], default_1.prototype, \"isCollapse\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: true\n})], default_1.prototype, \"isFirstLevel\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: ''\n})], default_1.prototype, \"basePath\", void 0);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'SidebarItem',\n  components: {\n    SidebarItemLink: _SidebarItemLink.default\n  }\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_path", "_interopRequireDefault", "require", "_vuePropertyDecorator", "_user", "_validate", "_SidebarItemLink", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty2", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_createForOfIteratorHelper", "Symbol", "iterator", "Array", "isArray", "_unsupportedIterableToArray", "_n", "F", "s", "n", "done", "value", "f", "TypeError", "a", "u", "call", "next", "return", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "Boolean", "prototype", "valueOf", "default_1", "_Vue", "_classCallCheck2", "_inherits2", "_createClass2", "key", "get", "item", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meta", "hidden", "UserModule", "roles", "showingChildNumber", "_iterator", "_step", "child", "err", "path", "<PERSON><PERSON><PERSON>", "routePath", "isExternal", "basePath", "resolve", "<PERSON><PERSON>", "__decorate", "Prop", "required", "Component", "components", "SidebarItemLink", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport path from 'path'\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport { Route, RouteConfig } from 'vue-router'\r\nimport { isExternal } from '@/utils/validate'\r\nimport SidebarItemLink from './SidebarItemLink.vue'\r\n\r\n@Component({\r\n  name: 'SidebarItem',\r\n  components: {\r\n    SidebarItemLink,\r\n  },\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ required: true }) private item!: RouteConfig\r\n  @Prop({ default: false }) private isCollapse!: boolean\r\n  @Prop({ default: true }) private isFirstLevel!: boolean\r\n  @Prop({ default: '' }) private basePath!: string\r\n\r\n  get showingChildNumber() {\r\n    if (this.item.children) {\r\n      const showingChildren = this.item.children.filter((item) => {\r\n        if (item.meta && item.meta.hidden) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n      return showingChildren.length\r\n    }\r\n    return 0\r\n  }\r\n\r\n  get roles() {\r\n    return UserModule.roles\r\n  }\r\n\r\n  get theOnlyOneChild() {\r\n    if (this.showingChildNumber > 0) {\r\n      return null\r\n    }\r\n    if (this.item.children) {\r\n      for (let child of this.item.children) {\r\n        if (!child.meta || !child.meta.hidden) {\r\n          return child\r\n        }\r\n      }\r\n    }\r\n    // If there is no children, return itself with path removed,\r\n    // because this.basePath already conatins item's path information\r\n    return { ...this.item, path: '' }\r\n  }\r\n\r\n  private resolvePath(routePath: string) {\r\n    if (isExternal(routePath)) {\r\n      return routePath\r\n    }\r\n    if (isExternal(this.basePath)) {\r\n      return this.basePath\r\n    }\r\n    return path.resolve(this.basePath, routePath)\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAL,sBAAA,CAAAC,OAAA;AAAmD,SAAAK,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAe,gBAAA,CAAAC,OAAA,EAAAjB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAe,yBAAA,GAAAf,MAAA,CAAAgB,gBAAA,CAAAnB,CAAA,EAAAG,MAAA,CAAAe,yBAAA,CAAAhB,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAiB,cAAA,CAAApB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAqB,2BAAApB,CAAA,EAAAD,CAAA,QAAAE,CAAA,yBAAAoB,MAAA,IAAArB,CAAA,CAAAqB,MAAA,CAAAC,QAAA,KAAAtB,CAAA,qBAAAC,CAAA,QAAAsB,KAAA,CAAAC,OAAA,CAAAxB,CAAA,MAAAC,CAAA,GAAAwB,2BAAA,CAAAzB,CAAA,MAAAD,CAAA,IAAAC,CAAA,uBAAAA,CAAA,CAAAa,MAAA,IAAAZ,CAAA,KAAAD,CAAA,GAAAC,CAAA,OAAAyB,EAAA,MAAAC,CAAA,YAAAA,EAAA,eAAAC,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAA,EAAA,WAAAH,EAAA,IAAA1B,CAAA,CAAAa,MAAA,KAAAiB,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAA/B,CAAA,CAAA0B,EAAA,UAAA3B,CAAA,WAAAA,EAAAC,CAAA,UAAAA,CAAA,KAAAgC,CAAA,EAAAL,CAAA,gBAAAM,SAAA,iJAAA5B,CAAA,EAAA6B,CAAA,OAAAC,CAAA,gBAAAP,CAAA,WAAAA,EAAA,IAAA3B,CAAA,GAAAA,CAAA,CAAAmC,IAAA,CAAApC,CAAA,MAAA6B,CAAA,WAAAA,EAAA,QAAA7B,CAAA,GAAAC,CAAA,CAAAoC,IAAA,WAAAH,CAAA,GAAAlC,CAAA,CAAA8B,IAAA,EAAA9B,CAAA,KAAAD,CAAA,WAAAA,EAAAC,CAAA,IAAAmC,CAAA,OAAA9B,CAAA,GAAAL,CAAA,KAAAgC,CAAA,WAAAA,EAAA,UAAAE,CAAA,YAAAjC,CAAA,CAAAqC,MAAA,IAAArC,CAAA,CAAAqC,MAAA,oBAAAH,CAAA,QAAA9B,CAAA;AAAA,SAAAoB,4BAAAzB,CAAA,EAAAkC,CAAA,QAAAlC,CAAA,2BAAAA,CAAA,SAAAuC,iBAAA,CAAAvC,CAAA,EAAAkC,CAAA,OAAAjC,CAAA,MAAAuC,QAAA,CAAAJ,IAAA,CAAApC,CAAA,EAAAyC,KAAA,6BAAAxC,CAAA,IAAAD,CAAA,CAAA0C,WAAA,KAAAzC,CAAA,GAAAD,CAAA,CAAA0C,WAAA,CAAAC,IAAA,aAAA1C,CAAA,cAAAA,CAAA,GAAAsB,KAAA,CAAAqB,IAAA,CAAA5C,CAAA,oBAAAC,CAAA,+CAAA4C,IAAA,CAAA5C,CAAA,IAAAsC,iBAAA,CAAAvC,CAAA,EAAAkC,CAAA;AAAA,SAAAK,kBAAAvC,CAAA,EAAAkC,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAlC,CAAA,CAAAa,MAAA,MAAAqB,CAAA,GAAAlC,CAAA,CAAAa,MAAA,YAAAd,CAAA,MAAA8B,CAAA,GAAAN,KAAA,CAAAW,CAAA,GAAAnC,CAAA,GAAAmC,CAAA,EAAAnC,CAAA,IAAA8B,CAAA,CAAA9B,CAAA,IAAAC,CAAA,CAAAD,CAAA,UAAA8B,CAAA;AAAA,SAAAiB,WAAA7C,CAAA,EAAAI,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAA0C,gBAAA,CAAA/B,OAAA,EAAAX,CAAA,OAAA2C,2BAAA,CAAAhC,OAAA,EAAAf,CAAA,EAAAgD,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAA9C,CAAA,EAAAN,CAAA,YAAAgD,gBAAA,CAAA/B,OAAA,EAAAf,CAAA,EAAAyC,WAAA,IAAArC,CAAA,CAAAK,KAAA,CAAAT,CAAA,EAAAF,CAAA;AAAA,SAAAkD,0BAAA,cAAAhD,CAAA,IAAAmD,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAlB,IAAA,CAAAc,OAAA,CAAAC,SAAA,CAAAC,OAAA,iCAAAnD,CAAA,aAAAgD,yBAAA,YAAAA,0BAAA,aAAAhD,CAAA;AAQnD,IAAAsD,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAzC,OAAA,QAAAuC,SAAA;IAAA,OAAAT,UAAA,OAAAS,SAAA,EAAA3C,SAAA;EAAA;EAAA,IAAA8C,UAAA,CAAA1C,OAAA,EAAAuC,SAAA,EAAAC,IAAA;EAAA,WAAAG,aAAA,CAAA3C,OAAA,EAAAuC,SAAA;IAAAK,GAAA;IAAAC,GAAA,EAME,SAAAA,IAAA,EAAsB;MACpB,IAAI,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;QACtB,IAAMC,eAAe,GAAG,IAAI,CAACF,IAAI,CAACC,QAAQ,CAACzD,MAAM,CAAC,UAACwD,IAAI,EAAI;UACzD,IAAIA,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACG,IAAI,CAACC,MAAM,EAAE;YACjC,OAAO,KAAK;;UAEd,OAAO,IAAI;QACb,CAAC,CAAC;QACF,OAAOF,eAAe,CAACnD,MAAM;;MAE/B,OAAO,CAAC;IACV;EAAC;IAAA+C,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAS;MACP,OAAOM,gBAAU,CAACC,KAAK;IACzB;EAAC;IAAAR,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAmB;MACjB,IAAI,IAAI,CAACQ,kBAAkB,GAAG,CAAC,EAAE;QAC/B,OAAO,IAAI;;MAEb,IAAI,IAAI,CAACP,IAAI,CAACC,QAAQ,EAAE;QAAA,IAAAO,SAAA,GAAAlD,0BAAA,CACJ,IAAI,CAAC0C,IAAI,CAACC,QAAQ;UAAAQ,KAAA;QAAA;UAApC,KAAAD,SAAA,CAAA1C,CAAA,MAAA2C,KAAA,GAAAD,SAAA,CAAAzC,CAAA,IAAAC,IAAA,GAAsC;YAAA,IAA7B0C,KAAK,GAAAD,KAAA,CAAAxC,KAAA;YACZ,IAAI,CAACyC,KAAK,CAACP,IAAI,IAAI,CAACO,KAAK,CAACP,IAAI,CAACC,MAAM,EAAE;cACrC,OAAOM,KAAK;;;QAEf,SAAAC,GAAA;UAAAH,SAAA,CAAAvE,CAAA,CAAA0E,GAAA;QAAA;UAAAH,SAAA,CAAAtC,CAAA;QAAA;;MAEH;MACA;MACA,OAAArB,aAAA,CAAAA,aAAA,KAAY,IAAI,CAACmD,IAAI;QAAEY,IAAI,EAAE;MAAE;IACjC;EAAC;IAAAd,GAAA;IAAA7B,KAAA,EAEO,SAAA4C,WAAWA,CAACC,SAAiB;MACnC,IAAI,IAAAC,oBAAU,EAACD,SAAS,CAAC,EAAE;QACzB,OAAOA,SAAS;;MAElB,IAAI,IAAAC,oBAAU,EAAC,IAAI,CAACC,QAAQ,CAAC,EAAE;QAC7B,OAAO,IAAI,CAACA,QAAQ;;MAEtB,OAAOJ,aAAI,CAACK,OAAO,CAAC,IAAI,CAACD,QAAQ,EAAEF,SAAS,CAAC;IAC/C;EAAC;AAAA,EA/C0BI,yBAAG,CAgD/B;AA/C2B,IAAAC,iBAAA,GAAzB,IAAAC,0BAAI,EAAC;EAAEC,QAAQ,EAAE;AAAI,CAAE,CAAC,C,sCAA2B;AAC1B,IAAAF,iBAAA,GAAzB,IAAAC,0BAAI,EAAC;EAAElE,OAAO,EAAE;AAAK,CAAE,CAAC,C,4CAA6B;AAC7B,IAAAiE,iBAAA,GAAxB,IAAAC,0BAAI,EAAC;EAAElE,OAAO,EAAE;AAAI,CAAE,CAAC,C,8CAA+B;AAChC,IAAAiE,iBAAA,GAAtB,IAAAC,0BAAI,EAAC;EAAElE,OAAO,EAAE;AAAE,CAAE,CAAC,C,0CAA0B;AAJlDuC,SAAA,OAAA0B,iBAAA,GANC,IAAAG,+BAAS,EAAC;EACTzC,IAAI,EAAE,aAAa;EACnB0C,UAAU,EAAE;IACVC,eAAe,EAAfA;;CAEH,CAAC,C,YAiDD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAxE,OAAA,G", "ignoreList": []}]}