{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/common.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/common.ts", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throttle = exports.strIncrease = exports.debounce = exports.checkProcessEnv = void 0;\nvar checkProcessEnv = exports.checkProcessEnv = function checkProcessEnv() {\n  return process.env.VUE_APP_DELETE_PERMISSIONS === 'true';\n};\nvar debounce = exports.debounce = function debounce(fn, time) {\n  time = time || 200;\n  // 定时器\n  var timer = null;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var _this = this;\n    if (timer) {\n      clearTimeout(timer);\n    }\n    timer = setTimeout(function () {\n      timer = null;\n      fn.apply(_this, args);\n    }, time);\n  };\n};\n//节流\nvar throttle = exports.throttle = function throttle(fn, time) {\n  var timer = null;\n  time = time || 1000;\n  return function () {\n    if (timer) {\n      return;\n    }\n    var _this = this;\n    timer = setTimeout(function () {\n      timer = null;\n    }, time);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    fn.apply(_this, args);\n  };\n};\n// 判断正、负\nvar strIncrease = exports.strIncrease = function strIncrease(str) {\n  if (str.slice(0, 1) === '-') {\n    return true;\n  }\n};", {"version": 3, "names": ["checkProcessEnv", "exports", "process", "env", "VUE_APP_DELETE_PERMISSIONS", "debounce", "fn", "time", "timer", "_len", "arguments", "length", "args", "Array", "_key", "_this", "clearTimeout", "setTimeout", "apply", "throttle", "_len2", "_key2", "strIncrease", "str", "slice"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/common.ts"], "sourcesContent": ["export const checkProcessEnv =() => {\r\n  return process.env.VUE_APP_DELETE_PERMISSIONS==='true'\r\n}\r\nexport const debounce=(fn, time)=> {\r\n  time = time || 200\r\n  // 定时器\r\n  let timer = null\r\n  return function(...args) {\r\n    var _this = this\r\n    if (timer) {\r\n      clearTimeout(timer)\r\n    }\r\n    timer = setTimeout(function() {\r\n      timer = null\r\n      fn.apply(_this, args)\r\n    }, time)\r\n  }\r\n  \r\n};\r\n//节流\r\nexport const throttle = (fn, time)=> {\r\n  let timer = null\r\n  time = time || 1000\r\n  return function(...args) {\r\n    if (timer) {\r\n      return\r\n    }\r\n    const _this = this\r\n    timer = setTimeout(() => {\r\n      timer = null\r\n    }, time)\r\n    fn.apply(_this, args)\r\n  }\r\n}\r\n// 判断正、负\r\nexport const strIncrease = (str)=>{\r\n  if(str.slice(0,1) ==='-'){\r\n    return true\r\n    }\r\n}\r\n"], "mappings": ";;;;;;AAAO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAE,SAAjBA,eAAeA,CAAA,EAAO;EACjC,OAAOE,OAAO,CAACC,GAAG,CAACC,0BAA0B,KAAG,MAAM;AACxD,CAAC;AACM,IAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAAC,SAATA,QAAQA,CAAEC,EAAE,EAAEC,IAAI,EAAG;EAChCA,IAAI,GAAGA,IAAI,IAAI,GAAG;EAClB;EACA,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,YAAgB;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAJC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACrB,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIP,KAAK,EAAE;MACTQ,YAAY,CAACR,KAAK,CAAC;;IAErBA,KAAK,GAAGS,UAAU,CAAC;MACjBT,KAAK,GAAG,IAAI;MACZF,EAAE,CAACY,KAAK,CAACH,KAAK,EAAEH,IAAI,CAAC;IACvB,CAAC,EAAEL,IAAI,CAAC;EACV,CAAC;AAEH,CAAC;AACD;AACO,IAAMY,QAAQ,GAAAlB,OAAA,CAAAkB,QAAA,GAAG,SAAXA,QAAQA,CAAIb,EAAE,EAAEC,IAAI,EAAG;EAClC,IAAIC,KAAK,GAAG,IAAI;EAChBD,IAAI,GAAGA,IAAI,IAAI,IAAI;EACnB,OAAO,YAAgB;IACrB,IAAIC,KAAK,EAAE;MACT;;IAEF,IAAMO,KAAK,GAAG,IAAI;IAClBP,KAAK,GAAGS,UAAU,CAAC,YAAK;MACtBT,KAAK,GAAG,IAAI;IACd,CAAC,EAAED,IAAI,CAAC;IAAA,SAAAa,KAAA,GAAAV,SAAA,CAAAC,MAAA,EAPSC,IAAI,OAAAC,KAAA,CAAAO,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJT,IAAI,CAAAS,KAAA,IAAAX,SAAA,CAAAW,KAAA;IAAA;IAQrBf,EAAE,CAACY,KAAK,CAACH,KAAK,EAAEH,IAAI,CAAC;EACvB,CAAC;AACH,CAAC;AACD;AACO,IAAMU,WAAW,GAAArB,OAAA,CAAAqB,WAAA,GAAG,SAAdA,WAAWA,CAAIC,GAAG,EAAG;EAChC,IAAGA,GAAG,CAACC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,KAAI,GAAG,EAAC;IACvB,OAAO,IAAI;;AAEf,CAAC", "ignoreList": []}]}