{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/store/modules/app.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/store/modules/app.ts", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DeviceType = exports.AppModule = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuexModuleDecorators = require(\"vuex-module-decorators\");\nvar _cookies = require(\"@/utils/cookies\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar DeviceType;\n(function (DeviceType) {\n  DeviceType[DeviceType[\"Mobile\"] = 0] = \"Mobile\";\n  DeviceType[DeviceType[\"Desktop\"] = 1] = \"Desktop\";\n})(DeviceType || (exports.DeviceType = DeviceType = {}));\nvar App = /*#__PURE__*/function (_VuexModule) {\n  function App() {\n    var _this;\n    (0, _classCallCheck2.default)(this, App);\n    _this = _callSuper(this, App, arguments);\n    _this.sidebar = {\n      'opened': true,\n      'withoutAnimation': false\n    };\n    _this.device = DeviceType.Desktop;\n    _this.statusNumber = 0;\n    return _this;\n  }\n  (0, _inherits2.default)(App, _VuexModule);\n  return (0, _createClass2.default)(App, [{\n    key: \"TOGGLE_SIDEBAR\",\n    value: function TOGGLE_SIDEBAR(withoutAnimation) {\n      this.sidebar.opened = !this.sidebar.opened;\n      this.sidebar.withoutAnimation = withoutAnimation;\n      if (this.sidebar.opened) {\n        (0, _cookies.setSidebarStatus)('opened');\n      } else {\n        (0, _cookies.setSidebarStatus)('closed');\n      }\n    }\n  }, {\n    key: \"CLOSE_SIDEBAR\",\n    value: function CLOSE_SIDEBAR(withoutAnimation) {\n      this.sidebar.opened = false;\n      this.sidebar.withoutAnimation = withoutAnimation;\n      (0, _cookies.setSidebarStatus)('closed');\n    }\n  }, {\n    key: \"STATUS_NUMBER\",\n    value: function STATUS_NUMBER(device) {\n      this.statusNumber = device;\n    }\n  }, {\n    key: \"TOGGLE_DEVICE\",\n    value: function TOGGLE_DEVICE(device) {\n      this.device = device;\n    }\n  }, {\n    key: \"ToggleSideBar\",\n    value: function ToggleSideBar(withoutAnimation) {\n      this.TOGGLE_SIDEBAR(withoutAnimation);\n    }\n  }, {\n    key: \"CloseSideBar\",\n    value: function CloseSideBar(withoutAnimation) {\n      this.CLOSE_SIDEBAR(withoutAnimation);\n    }\n  }, {\n    key: \"ToggleDevice\",\n    value: function ToggleDevice(device) {\n      this.TOGGLE_DEVICE(device);\n    }\n  }, {\n    key: \"StatusNumber\",\n    value: function StatusNumber(device) {\n      this.STATUS_NUMBER(device);\n    }\n  }]);\n}(_vuexModuleDecorators.VuexModule);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Mutation], App.prototype, \"TOGGLE_SIDEBAR\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Mutation], App.prototype, \"CLOSE_SIDEBAR\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Mutation], App.prototype, \"STATUS_NUMBER\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Mutation], App.prototype, \"TOGGLE_DEVICE\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Action], App.prototype, \"ToggleSideBar\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Action], App.prototype, \"CloseSideBar\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Action], App.prototype, \"ToggleDevice\", null);\n(0, _tslib.__decorate)([_vuexModuleDecorators.Action], App.prototype, \"StatusNumber\", null);\nApp = (0, _tslib.__decorate)([(0, _vuexModuleDecorators.Module)({\n  'dynamic': true,\n  store: _store.default,\n  'name': 'app'\n})], App);\nvar AppModule = exports.AppModule = (0, _vuexModuleDecorators.getModule)(App);", {"version": 3, "names": ["_vuexModuleDecorators", "require", "_cookies", "_store", "_interopRequireDefault", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "DeviceType", "exports", "App", "_VuexModule", "_this", "_classCallCheck2", "sidebar", "device", "Desktop", "statusNumber", "_inherits2", "_createClass2", "key", "value", "TOGGLE_SIDEBAR", "withoutAnimation", "opened", "setSidebarStatus", "CLOSE_SIDEBAR", "STATUS_NUMBER", "TOGGLE_DEVICE", "ToggleSideBar", "CloseSideBar", "ToggleDevice", "StatusNumber", "VuexModule", "__decorate", "Mutation", "Action", "<PERSON><PERSON><PERSON>", "store", "AppModule", "getModule"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/store/modules/app.ts"], "sourcesContent": ["import { VuexModule, Module, Mutation, Action, getModule } from 'vuex-module-decorators'\r\nimport { getSidebarStatus, setSidebarStatus } from '@/utils/cookies'\r\nimport store from '@/store'\r\n\r\nexport enum DeviceType {\r\n  Mobile,\r\n  Desktop\r\n}\r\n\r\nexport interface IAppState {\r\n  device: DeviceType\r\n  sidebar: {\r\n    opened: boolean\r\n    withoutAnimation: boolean\r\n    \r\n  }\r\n  statusNumber:Number\r\n}\r\n\r\n@Module({ 'dynamic': true, store, 'name': 'app' })\r\nclass App extends VuexModule implements IAppState {\r\n  public sidebar = {\r\n    'opened': true, //getSidebarStatus() !== 'closed',\r\n    'withoutAnimation': false\r\n  }\r\n  public device = DeviceType.Desktop\r\n  public statusNumber = 0\r\n  @Mutation\r\n  private TOGGLE_SIDEBAR(withoutAnimation: boolean) {\r\n    this.sidebar.opened = !this.sidebar.opened\r\n    this.sidebar.withoutAnimation = withoutAnimation\r\n    if (this.sidebar.opened) {\r\n      setSidebarStatus('opened')\r\n    } else {\r\n      setSidebarStatus('closed')\r\n    }\r\n  }\r\n\r\n  @Mutation\r\n  private CLOSE_SIDEBAR(withoutAnimation: boolean) {\r\n    this.sidebar.opened = false\r\n    this.sidebar.withoutAnimation = withoutAnimation\r\n    setSidebarStatus('closed')\r\n  }\r\n\r\n  @Mutation\r\n  private STATUS_NUMBER(device: DeviceType) {\r\n    this.statusNumber = device\r\n  }\r\n\r\n  @Mutation\r\n  private TOGGLE_DEVICE(device: DeviceType) {\r\n    this.device = device\r\n  }\r\n\r\n  @Action\r\n  public ToggleSideBar(withoutAnimation: boolean) {\r\n    this.TOGGLE_SIDEBAR(withoutAnimation)\r\n  }\r\n\r\n  @Action\r\n  public CloseSideBar(withoutAnimation: boolean) {\r\n    this.CLOSE_SIDEBAR(withoutAnimation)\r\n  }\r\n\r\n  @Action\r\n  public ToggleDevice(device: DeviceType) {\r\n    this.TOGGLE_DEVICE(device)\r\n  }\r\n\r\n  @Action\r\n  public StatusNumber(device: any) {\r\n    this.STATUS_NUMBER(device)\r\n  }\r\n}\r\n\r\nexport const AppModule = getModule(App)\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAA2B,SAAAI,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAE3B,IAAYe,UAGX;AAHD,WAAYA,UAAU;EACpBA,UAAA,CAAAA,UAAA,0BAAM;EACNA,UAAA,CAAAA,UAAA,4BAAO;AACT,CAAC,EAHWA,UAAU,KAAAC,OAAA,CAAAD,UAAA,GAAVA,UAAU;AAgBtB,IAAME,GAAG,0BAAAC,WAAA;EAAT,SAAAD,IAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAhB,OAAA,QAAAa,GAAA;;IACSE,KAAA,CAAAE,OAAO,GAAG;MACf,QAAQ,EAAE,IAAI;MACd,kBAAkB,EAAE;KACrB;IACMF,KAAA,CAAAG,MAAM,GAAGP,UAAU,CAACQ,OAAO;IAC3BJ,KAAA,CAAAK,YAAY,GAAG,CAAC;IAAA,OAAAL,KAAA;EAgDzB;EAAC,IAAAM,UAAA,CAAArB,OAAA,EAAAa,GAAA,EAAAC,WAAA;EAAA,WAAAQ,aAAA,CAAAtB,OAAA,EAAAa,GAAA;IAAAU,GAAA;IAAAC,KAAA,EA9CS,SAAAC,cAAcA,CAACC,gBAAyB;MAC9C,IAAI,CAACT,OAAO,CAACU,MAAM,GAAG,CAAC,IAAI,CAACV,OAAO,CAACU,MAAM;MAC1C,IAAI,CAACV,OAAO,CAACS,gBAAgB,GAAGA,gBAAgB;MAChD,IAAI,IAAI,CAACT,OAAO,CAACU,MAAM,EAAE;QACvB,IAAAC,yBAAgB,EAAC,QAAQ,CAAC;OAC3B,MAAM;QACL,IAAAA,yBAAgB,EAAC,QAAQ,CAAC;;IAE9B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAGO,SAAAK,aAAaA,CAACH,gBAAyB;MAC7C,IAAI,CAACT,OAAO,CAACU,MAAM,GAAG,KAAK;MAC3B,IAAI,CAACV,OAAO,CAACS,gBAAgB,GAAGA,gBAAgB;MAChD,IAAAE,yBAAgB,EAAC,QAAQ,CAAC;IAC5B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAGO,SAAAM,aAAaA,CAACZ,MAAkB;MACtC,IAAI,CAACE,YAAY,GAAGF,MAAM;IAC5B;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAGO,SAAAO,aAAaA,CAACb,MAAkB;MACtC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAGM,SAAAQ,aAAaA,CAACN,gBAAyB;MAC5C,IAAI,CAACD,cAAc,CAACC,gBAAgB,CAAC;IACvC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAGM,SAAAS,YAAYA,CAACP,gBAAyB;MAC3C,IAAI,CAACG,aAAa,CAACH,gBAAgB,CAAC;IACtC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAGM,SAAAU,YAAYA,CAAChB,MAAkB;MACpC,IAAI,CAACa,aAAa,CAACb,MAAM,CAAC;IAC5B;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAGM,SAAAW,YAAYA,CAACjB,MAAW;MAC7B,IAAI,CAACY,aAAa,CAACZ,MAAM,CAAC;IAC5B;EAAC;AAAA,EArDekB,gCAAU,CAsD3B;AA9CC,IAAAC,iBAAA,GADCC,8BAAQ,C,wCASR;AAGD,IAAAD,iBAAA,GADCC,8BAAQ,C,uCAKR;AAGD,IAAAD,iBAAA,GADCC,8BAAQ,C,uCAGR;AAGD,IAAAD,iBAAA,GADCC,8BAAQ,C,uCAGR;AAGD,IAAAD,iBAAA,GADCE,4BAAM,C,uCAGN;AAGD,IAAAF,iBAAA,GADCE,4BAAM,C,sCAGN;AAGD,IAAAF,iBAAA,GADCE,4BAAM,C,sCAGN;AAGD,IAAAF,iBAAA,GADCE,4BAAM,C,sCAGN;AArDG1B,GAAG,OAAAwB,iBAAA,GADR,IAAAG,4BAAM,EAAC;EAAE,SAAS,EAAE,IAAI;EAAEC,KAAK,EAALA,cAAK;EAAE,MAAM,EAAE;AAAK,CAAE,CAAC,C,EAC5C5B,GAAG,CAsDR;AAEM,IAAM6B,SAAS,GAAA9B,OAAA,CAAA8B,SAAA,GAAG,IAAAC,+BAAS,EAAC9B,GAAG,CAAC", "ignoreList": []}]}