{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/top10.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/top10.vue", "mtime": 1657266895000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/typeof.js\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.weak-map\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar echarts = _interopRequireWildcard(require(\"echarts\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    (0, _classCallCheck2.default)(this, default_1);\n    return _callSuper(this, default_1, arguments);\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"getData\",\n    value: function getData() {\n      var _this = this;\n      this.$nextTick(function () {\n        _this.initChart();\n      });\n    }\n  }, {\n    key: \"initChart\",\n    value: function initChart() {\n      var chartDom = document.getElementById('top');\n      var myChart = echarts.init(chartDom);\n      var option;\n      option = {\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: '#fff',\n          borderRadius: 2,\n          textStyle: {\n            color: '#333',\n            fontSize: 12,\n            fontWeight: 300\n          }\n        },\n        grid: {\n          top: '-10px',\n          left: '0',\n          right: '0',\n          bottom: '0',\n          containLabel: true\n        },\n        xAxis: {\n          show: false\n        },\n        yAxis: {\n          //   隐藏y轴坐标轴\n          axisLine: {\n            show: false\n          },\n          // 隐藏y轴刻度线\n          axisTick: {\n            show: false,\n            alignWithLabel: true\n          },\n          type: 'category',\n          // interval: 100,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px'\n            }\n          },\n          data: this.top10data.nameList\n        },\n        series: [{\n          data: this.top10data.numberList,\n          type: 'bar',\n          showBackground: true,\n          backgroundStyle: {\n            color: '#F3F4F7'\n          },\n          barWidth: 20,\n          barGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n          barCategoryGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n          itemStyle: {\n            emphasis: {\n              barBorderRadius: 30\n            },\n            normal: {\n              barBorderRadius: [0, 10, 10, 0],\n              color: new echarts.graphic.LinearGradient(\n              // 渐变色\n              1, 0, 0, 0,\n              // 渐变色的起止位置, 右/下/左/上\n              [\n              // offset 位置\n              {\n                offset: 0,\n                color: '#FFBD00'\n              }, {\n                offset: 1,\n                color: '#FFD000'\n              }]),\n              label: {\n                //内容样式\n                show: true,\n                formatter: '{@score}',\n                color: '#333',\n                // position: \"insideLeft\", //内部左对齐\n                position: ['8', '5']\n              }\n            }\n          }\n        }]\n      };\n      option && myChart.setOption(option);\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)()], default_1.prototype, \"top10data\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('top10data')], default_1.prototype, \"getData\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'Top'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "echarts", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "_typeof", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "default_1", "_Vue", "_classCallCheck2", "arguments", "_inherits2", "_createClass2", "key", "value", "getData", "_this", "$nextTick", "initChart", "chartDom", "document", "getElementById", "myChart", "init", "option", "tooltip", "trigger", "backgroundColor", "borderRadius", "textStyle", "color", "fontSize", "fontWeight", "grid", "top", "left", "right", "bottom", "containLabel", "xAxis", "show", "yAxis", "axisLine", "axisTick", "alignWithLabel", "type", "axisLabel", "data", "top10data", "nameList", "series", "numberList", "showBackground", "backgroundStyle", "<PERSON><PERSON><PERSON><PERSON>", "barGap", "barCategoryGap", "itemStyle", "emphasis", "barBorderRadius", "normal", "graphic", "LinearGradient", "offset", "label", "formatter", "position", "setOption", "<PERSON><PERSON>", "__decorate", "Prop", "Watch", "Component", "name", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/top10.vue?vue&type=script&lang=ts"], "sourcesContent": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'Top',\n})\nexport default class extends Vue {\n  @Prop() private top10data!: any\n  @Watch('top10data')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('top') as any\n    const myChart = echarts.init(chartDom)\n    var option: any\n    option = {\n      tooltip: {\n        trigger: 'axis',\n        backgroundColor: '#fff', //背景颜色（此时为默认色）\n        borderRadius: 2, //边框圆角\n        textStyle: {\n          color: '#333', //字体颜色\n          fontSize: 12, //字体大小\n          fontWeight: 300,\n        },\n      },\n      grid: {\n        top: '-10px',\n        left: '0',\n        right: '0',\n        bottom: '0',\n        containLabel: true,\n      },\n      xAxis: {\n        show: false,\n      },\n      yAxis: {\n        //   隐藏y轴坐标轴\n        axisLine: {\n          show: false,\n        },\n        // 隐藏y轴刻度线\n        axisTick: {\n          show: false,\n          alignWithLabel: true,\n        },\n        type: 'category',\n        // interval: 100,\n        axisLabel: {\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n          // formatter: \"{value} ml\",//单位\n        },\n        data: this.top10data.nameList,\n      },\n      series: [\n        {\n          data: this.top10data.numberList,\n          type: 'bar',\n          showBackground: true,\n          backgroundStyle: {\n            color: '#F3F4F7',\n          },\n          barWidth: 20,\n          barGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n          barCategoryGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n\n          itemStyle: {\n            emphasis: {\n              barBorderRadius: 30,\n            },\n            normal: {\n              barBorderRadius: [0, 10, 10, 0], // 圆角\n              color: new echarts.graphic.LinearGradient( // 渐变色\n                1,\n                0,\n                0,\n                0, // 渐变色的起止位置, 右/下/左/上\n                [\n                  // offset 位置\n                  { offset: 0, color: '#FFBD00' },\n                  { offset: 1, color: '#FFD000' },\n                ]\n              ),\n              label: {\n                //内容样式\n                show: true,\n                formatter: '{@score}',\n                color: '#333',\n                // position: \"insideLeft\", //内部左对齐\n                position: ['8', '5'], //自定义位置第一个参数为x轴方向，第二个参数为y轴方向，左上角为起点，向右向下为正数，向上向左为负数\n              },\n            },\n          },\n          // label: {\n          //   show: true,\n          //   position: \"left\",\n          //   valueAnimation: true,\n          // },\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAkC,SAAAE,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,wBAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,gBAAAW,OAAA,CAAAX,CAAA,0BAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAM,GAAA,CAAAZ,CAAA,UAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,GAAAM,CAAA,CAAAQ,GAAA,CAAAd,CAAA,EAAAQ,CAAA,cAAAO,EAAA,IAAAf,CAAA,gBAAAe,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,EAAA,OAAAR,CAAA,IAAAD,CAAA,GAAAY,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAApB,CAAA,EAAAe,EAAA,OAAAR,CAAA,CAAAM,GAAA,IAAAN,CAAA,CAAAO,GAAA,IAAAR,CAAA,CAAAE,CAAA,EAAAO,EAAA,EAAAR,CAAA,IAAAC,CAAA,CAAAO,EAAA,IAAAf,CAAA,CAAAe,EAAA,WAAAP,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAoB,WAAApB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAgB,gBAAA,CAAAZ,OAAA,EAAAJ,CAAA,OAAAiB,2BAAA,CAAAb,OAAA,EAAAT,CAAA,EAAAuB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAApB,CAAA,EAAAN,CAAA,YAAAsB,gBAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAA0B,WAAA,IAAArB,CAAA,CAAAsB,KAAA,CAAA3B,CAAA,EAAAD,CAAA;AAAA,SAAAwB,0BAAA,cAAAvB,CAAA,IAAA4B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA5B,CAAA,aAAAuB,yBAAA,YAAAA,0BAAA,aAAAvB,CAAA;AAIlC,IAAA+B,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAxB,OAAA,QAAAsB,SAAA;IAAA,OAAAX,UAAA,OAAAW,SAAA,EAAAG,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAA1B,OAAA,EAAAsB,SAAA,EAAAC,IAAA;EAAA,WAAAI,aAAA,CAAA3B,OAAA,EAAAsB,SAAA;IAAAM,GAAA;IAAAC,KAAA,EAGE,SAAAC,OAAOA,CAAA;MAAA,IAAAC,KAAA;MACL,IAAI,CAACC,SAAS,CAAC,YAAK;QAClBD,KAAI,CAACE,SAAS,EAAE;MAClB,CAAC,CAAC;IACJ;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAI,SAASA,CAAA;MAEP,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,KAAK,CAAQ;MACtD,IAAMC,OAAO,GAAGjD,OAAO,CAACkD,IAAI,CAACJ,QAAQ,CAAC;MACtC,IAAIK,MAAW;MACfA,MAAM,GAAG;QACPC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,MAAM;UACvBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;YACTC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;;SAEf;QACDC,IAAI,EAAE;UACJC,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,YAAY,EAAE;SACf;QACDC,KAAK,EAAE;UACLC,IAAI,EAAE;SACP;QACDC,KAAK,EAAE;UACL;UACAC,QAAQ,EAAE;YACRF,IAAI,EAAE;WACP;UACD;UACAG,QAAQ,EAAE;YACRH,IAAI,EAAE,KAAK;YACXI,cAAc,EAAE;WACjB;UACDC,IAAI,EAAE,UAAU;UAChB;UACAC,SAAS,EAAE;YACTjB,SAAS,EAAE;cACTC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;;WAGb;UACDgB,IAAI,EAAE,IAAI,CAACC,SAAS,CAACC;SACtB;QACDC,MAAM,EAAE,CACN;UACEH,IAAI,EAAE,IAAI,CAACC,SAAS,CAACG,UAAU;UAC/BN,IAAI,EAAE,KAAK;UACXO,cAAc,EAAE,IAAI;UACpBC,eAAe,EAAE;YACfvB,KAAK,EAAE;WACR;UACDwB,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAE,KAAK,CAAC;UACdC,cAAc,EAAE,KAAK,CAAC;UAEtBC,SAAS,EAAE;YACTC,QAAQ,EAAE;cACRC,eAAe,EAAE;aAClB;YACDC,MAAM,EAAE;cACND,eAAe,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;cAC/B7B,KAAK,EAAE,IAAIzD,OAAO,CAACwF,OAAO,CAACC,cAAc;cAAE;cACzC,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;cAAE;cACH;cACE;cACA;gBAAEC,MAAM,EAAE,CAAC;gBAAEjC,KAAK,EAAE;cAAS,CAAE,EAC/B;gBAAEiC,MAAM,EAAE,CAAC;gBAAEjC,KAAK,EAAE;cAAS,CAAE,CAChC,CACF;cACDkC,KAAK,EAAE;gBACL;gBACAxB,IAAI,EAAE,IAAI;gBACVyB,SAAS,EAAE,UAAU;gBACrBnC,KAAK,EAAE,MAAM;gBACb;gBACAoC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG;;;;SAS1B;OAEJ;MACD1C,MAAM,IAAIF,OAAO,CAAC6C,SAAS,CAAC3C,MAAM,CAAC;IACrC;EAAC;AAAA,EAvG0B4C,yBAAG,CAwG/B;AAvGS,IAAAC,iBAAA,GAAP,IAAAC,0BAAI,GAAE,C,2CAAwB;AAE/B,IAAAD,iBAAA,GADC,IAAAE,2BAAK,EAAC,WAAW,CAAC,C,uCAKlB;AAPHhE,SAAA,OAAA8D,iBAAA,GAHC,IAAAG,+BAAS,EAAC;EACTC,IAAI,EAAE;CACP,CAAC,C,YAyGD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA1F,OAAA,G", "ignoreList": []}]}