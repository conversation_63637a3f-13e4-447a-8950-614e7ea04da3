{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/index.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/index.ts", "mtime": 1656301720000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.exportInfor = exportInfor;\nexports.getUserStatistics = exports.getTurnoverStatistics = exports.getTop = exports.getSetMealStatistics = exports.getOverviewDishes = exports.getOrderStatistics = exports.getOrderData = exports.getDataOverView = exports.getBusinessData = void 0;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 营业额数据\n// export const getTurnoverDataes = (data) =>\n//   request({\n//     'url': `/report/turnoverStatistics`,\n//     'method': 'get',\n//     data\n//   })\n// 首页数据\n// // 今日数据\n// export const getTodayDataes = () =>\n//   request({\n//     'url': `/workspace/todaydate`,\n//     'method': 'get'\n//   })\n// 订单管理\nvar getOrderData = exports.getOrderData = function getOrderData() {\n  return (0, _request.default)({\n    'url': \"/workspace/overviewOrders\",\n    'method': 'get'\n  });\n};\n// 菜品总览\nvar getOverviewDishes = exports.getOverviewDishes = function getOverviewDishes() {\n  return (0, _request.default)({\n    'url': \"/workspace/overviewDishes\",\n    'method': 'get'\n  });\n};\n// 套餐总览\nvar getSetMealStatistics = exports.getSetMealStatistics = function getSetMealStatistics() {\n  return (0, _request.default)({\n    'url': \"/workspace/overviewSetmeals\",\n    'method': 'get'\n  });\n};\n// 营业数据\nvar getBusinessData = exports.getBusinessData = function getBusinessData() {\n  return (0, _request.default)({\n    'url': \"/workspace/businessData\",\n    'method': 'get'\n  });\n};\n/**\n *\n * 报表数据\n *\n **/\n// 统计\n// 获取当日销售数据 -> 顶部数据\n// export const getDataes = (params: any) =>\n//   request({\n//     'url': `/report/amountCollect/${params.date}`,\n//     'method': 'get'\n//   })\n// 营业额统计\nvar getTurnoverStatistics = exports.getTurnoverStatistics = function getTurnoverStatistics(params) {\n  return (0, _request.default)({\n    'url': \"/report/turnoverStatistics\",\n    'method': 'get',\n    params: params\n  });\n};\n// 用户统计\nvar getUserStatistics = exports.getUserStatistics = function getUserStatistics(params) {\n  return (0, _request.default)({\n    'url': \"/report/userStatistics\",\n    'method': 'get',\n    params: params\n  });\n};\n// 订单统计\nvar getOrderStatistics = exports.getOrderStatistics = function getOrderStatistics(params) {\n  return (0, _request.default)({\n    'url': \"/report/ordersStatistics\",\n    'method': 'get',\n    params: params\n  });\n};\n// 销量排名TOP10\nvar getTop = exports.getTop = function getTop(params) {\n  return (0, _request.default)({\n    'url': \"/report/top10\",\n    'method': 'get',\n    params: params\n  });\n};\n// 数据概览\nvar getDataOverView = exports.getDataOverView = function getDataOverView(params) {\n  return (0, _request.default)({\n    'url': \"/report/dataOverView\",\n    'method': 'get',\n    params: params\n  });\n};\n// 导出\nfunction exportInfor() {\n  return (0, _request.default)({\n    url: '/report/export',\n    method: 'get',\n    responseType: \"blob\"\n  });\n}", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getOrderData", "exports", "request", "getOverviewDishes", "getSetMealStatistics", "getBusinessData", "getTurnoverStatistics", "params", "getUserStatistics", "getOrderStatistics", "getTop", "getDataOverView", "exportInfor", "url", "method", "responseType"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/index.ts"], "sourcesContent": ["import request from '@/utils/request'\n// 营业额数据\n// export const getTurnoverDataes = (data) =>\n//   request({\n//     'url': `/report/turnoverStatistics`,\n//     'method': 'get',\n//     data\n//   })\n// 首页数据\n// // 今日数据\n// export const getTodayDataes = () =>\n//   request({\n//     'url': `/workspace/todaydate`,\n//     'method': 'get'\n//   })\n// 订单管理\n  export const getOrderData = () =>\n  request({\n    'url': `/workspace/overviewOrders`,\n    'method': 'get'\n  })\n// 菜品总览\nexport const getOverviewDishes = () =>\nrequest({\n  'url': `/workspace/overviewDishes`,\n  'method': 'get'\n})\n// 套餐总览\nexport const getSetMealStatistics = () =>\nrequest({\n  'url': `/workspace/overviewSetmeals`,\n  'method': 'get'\n})\n// 营业数据\nexport const getBusinessData= () =>\nrequest({\n  'url': `/workspace/businessData`,\n  'method': 'get'\n})\n/**\n *\n * 报表数据\n *\n **/\n// 统计\n// 获取当日销售数据 -> 顶部数据\n// export const getDataes = (params: any) =>\n//   request({\n//     'url': `/report/amountCollect/${params.date}`,\n//     'method': 'get'\n//   })\n\n\n// 营业额统计\nexport const getTurnoverStatistics= (params: any) =>\n  request({\n    'url': `/report/turnoverStatistics`,\n    'method': 'get',\n    params\n  })\n\n// 用户统计\nexport const getUserStatistics= (params: any) =>\n  request({\n    'url': `/report/userStatistics`,\n    'method': 'get',\n    params\n  })\n  // 订单统计\nexport const getOrderStatistics= (params: any) =>\nrequest({\n  'url': `/report/ordersStatistics`,\n  'method': 'get',\n  params\n})\n  // 销量排名TOP10\n  export const getTop= (params: any) =>\n  request({\n    'url': `/report/top10`,\n    'method': 'get',\n    params\n  })\n  // 数据概览\n  export const getDataOverView= (params: any) =>\n  request({\n    'url': `/report/dataOverView`,\n    'method': 'get',\n    params\n  })\n  // 导出\n  export function exportInfor() {\n    return request({\n      url: '/report/export',\n      method: 'get',\n      responseType: \"blob\"\n    })\n  }\n"], "mappings": ";;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACS,IAAMC,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG,SAAfA,YAAYA,CAAA;EAAA,OACzB,IAAAE,gBAAO,EAAC;IACN,KAAK,6BAA6B;IAClC,QAAQ,EAAE;GACX,CAAC;AAAA;AACJ;AACO,IAAMC,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAC9B,IAAAD,gBAAO,EAAC;IACN,KAAK,6BAA6B;IAClC,QAAQ,EAAE;GACX,CAAC;AAAA;AACF;AACO,IAAME,oBAAoB,GAAAH,OAAA,CAAAG,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OACjC,IAAAF,gBAAO,EAAC;IACN,KAAK,+BAA+B;IACpC,QAAQ,EAAE;GACX,CAAC;AAAA;AACF;AACO,IAAMG,eAAe,GAAAJ,OAAA,CAAAI,eAAA,GAAE,SAAjBA,eAAeA,CAAA;EAAA,OAC5B,IAAAH,gBAAO,EAAC;IACN,KAAK,2BAA2B;IAChC,QAAQ,EAAE;GACX,CAAC;AAAA;AACF;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACO,IAAMI,qBAAqB,GAAAL,OAAA,CAAAK,qBAAA,GAAE,SAAvBA,qBAAqBA,CAAGC,MAAW;EAAA,OAC9C,IAAAL,gBAAO,EAAC;IACN,KAAK,8BAA8B;IACnC,QAAQ,EAAE,KAAK;IACfK,MAAM,EAANA;GACD,CAAC;AAAA;AAEJ;AACO,IAAMC,iBAAiB,GAAAP,OAAA,CAAAO,iBAAA,GAAE,SAAnBA,iBAAiBA,CAAGD,MAAW;EAAA,OAC1C,IAAAL,gBAAO,EAAC;IACN,KAAK,0BAA0B;IAC/B,QAAQ,EAAE,KAAK;IACfK,MAAM,EAANA;GACD,CAAC;AAAA;AACF;AACK,IAAME,kBAAkB,GAAAR,OAAA,CAAAQ,kBAAA,GAAE,SAApBA,kBAAkBA,CAAGF,MAAW;EAAA,OAC7C,IAAAL,gBAAO,EAAC;IACN,KAAK,4BAA4B;IACjC,QAAQ,EAAE,KAAK;IACfK,MAAM,EAANA;GACD,CAAC;AAAA;AACA;AACO,IAAMG,MAAM,GAAAT,OAAA,CAAAS,MAAA,GAAE,SAARA,MAAMA,CAAGH,MAAW;EAAA,OACjC,IAAAL,gBAAO,EAAC;IACN,KAAK,iBAAiB;IACtB,QAAQ,EAAE,KAAK;IACfK,MAAM,EAANA;GACD,CAAC;AAAA;AACF;AACO,IAAMI,eAAe,GAAAV,OAAA,CAAAU,eAAA,GAAE,SAAjBA,eAAeA,CAAGJ,MAAW;EAAA,OAC1C,IAAAL,gBAAO,EAAC;IACN,KAAK,wBAAwB;IAC7B,QAAQ,EAAE,KAAK;IACfK,MAAM,EAANA;GACD,CAAC;AAAA;AACF;AACM,SAAUK,WAAWA,CAAA;EACzB,OAAO,IAAAV,gBAAO,EAAC;IACbW,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE;GACf,CAAC;AACJ", "ignoreList": []}]}