{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/dish.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/dish.ts", "mtime": 1657782209000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es7.object.get-own-property-descriptors\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.object.keys\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.queryDishList = exports.queryDishById = exports.getDishPage = exports.getCategoryList = exports.editDish = exports.dishStatusByStatus = exports.dishCategoryList = exports.deleteDish = exports.commonDownload = exports.addDish = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/defineProperty.js\"));\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n *\n * 菜品管理\n *\n **/\n// 查询列表接口\nvar getDishPage = exports.getDishPage = function getDishPage(params) {\n  return (0, _request.default)({\n    url: '/dish/page',\n    method: 'get',\n    params: params\n  });\n};\n// 删除接口\nvar deleteDish = exports.deleteDish = function deleteDish(ids) {\n  return (0, _request.default)({\n    url: '/dish',\n    method: 'delete',\n    params: {\n      ids: ids\n    }\n  });\n};\n// 修改接口\nvar editDish = exports.editDish = function editDish(params) {\n  return (0, _request.default)({\n    url: '/dish',\n    method: 'put',\n    data: _objectSpread({}, params)\n  });\n};\n// 新增接口\nvar addDish = exports.addDish = function addDish(params) {\n  return (0, _request.default)({\n    url: '/dish',\n    method: 'post',\n    data: _objectSpread({}, params)\n  });\n};\n// 查询详情\nvar queryDishById = exports.queryDishById = function queryDishById(id) {\n  return (0, _request.default)({\n    url: \"/dish/\".concat(id),\n    method: 'get'\n  });\n};\n// 获取菜品分类列表\nvar getCategoryList = exports.getCategoryList = function getCategoryList(params) {\n  return (0, _request.default)({\n    url: '/category/list',\n    method: 'get',\n    params: params\n  });\n};\n// 查菜品列表的接口\nvar queryDishList = exports.queryDishList = function queryDishList(params) {\n  return (0, _request.default)({\n    url: '/dish/list',\n    method: 'get',\n    params: params\n  });\n};\n// 文件down预览\nvar commonDownload = exports.commonDownload = function commonDownload(params) {\n  return (0, _request.default)({\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'\n    },\n    url: '/common/download',\n    method: 'get',\n    params: params\n  });\n};\n// 起售停售---批量起售停售接口\nvar dishStatusByStatus = exports.dishStatusByStatus = function dishStatusByStatus(params) {\n  return (0, _request.default)({\n    url: \"/dish/status/\".concat(params.status),\n    method: 'post',\n    params: {\n      id: params.id\n    }\n  });\n};\n//菜品分类数据查询\nvar dishCategoryList = exports.dishCategoryList = function dishCategoryList(params) {\n  return (0, _request.default)({\n    url: \"/category/list\",\n    method: 'get',\n    params: _objectSpread({}, params)\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty2", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "getDishPage", "exports", "params", "request", "url", "method", "deleteDish", "ids", "editDish", "data", "addDish", "queryDishById", "id", "concat", "getCategoryList", "queryDishList", "commonDownload", "headers", "dishStatusByStatus", "status", "dishCategoryList"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/dish.ts"], "sourcesContent": ["import request from '@/utils/request'\n/**\n *\n * 菜品管理\n *\n **/\n// 查询列表接口\nexport const getDishPage = (params: any) => {\n  return request({\n    url: '/dish/page',\n    method: 'get',\n    params\n  })\n}\n\n// 删除接口\nexport const deleteDish = (ids: string) => {\n  return request({\n    url: '/dish',\n    method: 'delete',\n    params: { ids }\n  })\n}\n\n// 修改接口\nexport const editDish = (params: any) => {\n  return request({\n    url: '/dish',\n    method: 'put',\n    data: { ...params }\n  })\n}\n\n// 新增接口\nexport const addDish = (params: any) => {\n  return request({\n    url: '/dish',\n    method: 'post',\n    data: { ...params }\n  })\n}\n\n// 查询详情\nexport const queryDishById = (id: string | (string | null)[]) => {\n  return request({\n    url: `/dish/${id}`,\n    method: 'get'\n  })\n}\n\n// 获取菜品分类列表\nexport const getCategoryList = (params: any) => {\n  return request({\n    url: '/category/list',\n    method: 'get',\n    params\n  })\n}\n\n// 查菜品列表的接口\nexport const queryDishList = (params: any) => {\n  return request({\n    url: '/dish/list',\n    method: 'get',\n    params\n  })\n}\n\n// 文件down预览\nexport const commonDownload = (params: any) => {\n  return request({\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'\n    },\n    url: '/common/download',\n    method: 'get',\n    params\n  })\n}\n\n// 起售停售---批量起售停售接口\nexport const dishStatusByStatus = (params: any) => {\n  return request({\n    url: `/dish/status/${params.status}`,\n    method: 'post',\n    params: { id: params.id }\n  })\n}\n\n//菜品分类数据查询\nexport const dishCategoryList = (params: any) => {\n  return request({\n    url: `/category/list`,\n    method: 'get',\n    params: { ...params }\n  })\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,SAAAC,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAe,gBAAA,CAAAC,OAAA,EAAAjB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAe,yBAAA,GAAAf,MAAA,CAAAgB,gBAAA,CAAAnB,CAAA,EAAAG,MAAA,CAAAe,yBAAA,CAAAhB,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAiB,cAAA,CAAApB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AACrC;;;;;AAKA;AACO,IAAMqB,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAG,SAAdA,WAAWA,CAAIE,MAAW,EAAI;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAC;AACJ,CAAC;AAED;AACO,IAAMI,UAAU,GAAAL,OAAA,CAAAK,UAAA,GAAG,SAAbA,UAAUA,CAAIC,GAAW,EAAI;EACxC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAE,QAAQ;IAChBH,MAAM,EAAE;MAAEK,GAAG,EAAHA;IAAG;GACd,CAAC;AACJ,CAAC;AAED;AACO,IAAMC,QAAQ,GAAAP,OAAA,CAAAO,QAAA,GAAG,SAAXA,QAAQA,CAAIN,MAAW,EAAI;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAE,KAAK;IACbI,IAAI,EAAAlB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMQ,OAAO,GAAAT,OAAA,CAAAS,OAAA,GAAG,SAAVA,OAAOA,CAAIR,MAAW,EAAI;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAAlB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMS,aAAa,GAAAV,OAAA,CAAAU,aAAA,GAAG,SAAhBA,aAAaA,CAAIC,EAA8B,EAAI;EAC9D,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,WAAAS,MAAA,CAAWD,EAAE,CAAE;IAClBP,MAAM,EAAE;GACT,CAAC;AACJ,CAAC;AAED;AACO,IAAMS,eAAe,GAAAb,OAAA,CAAAa,eAAA,GAAG,SAAlBA,eAAeA,CAAIZ,MAAW,EAAI;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAC;AACJ,CAAC;AAED;AACO,IAAMa,aAAa,GAAAd,OAAA,CAAAc,aAAA,GAAG,SAAhBA,aAAaA,CAAIb,MAAW,EAAI;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAC;AACJ,CAAC;AAED;AACO,IAAMc,cAAc,GAAAf,OAAA,CAAAe,cAAA,GAAG,SAAjBA,cAAcA,CAAId,MAAW,EAAI;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbc,OAAO,EAAE;MACP,cAAc,EAAE;KACjB;IACDb,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAC;AACJ,CAAC;AAED;AACO,IAAMgB,kBAAkB,GAAAjB,OAAA,CAAAiB,kBAAA,GAAG,SAArBA,kBAAkBA,CAAIhB,MAAW,EAAI;EAChD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,kBAAAS,MAAA,CAAkBX,MAAM,CAACiB,MAAM,CAAE;IACpCd,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE;MAAEU,EAAE,EAAEV,MAAM,CAACU;IAAE;GACxB,CAAC;AACJ,CAAC;AAED;AACO,IAAMQ,gBAAgB,GAAAnB,OAAA,CAAAmB,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIlB,MAAW,EAAI;EAC9C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,kBAAkB;IACrBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAAX,aAAA,KAAOW,MAAM;GACpB,CAAC;AACJ,CAAC", "ignoreList": []}]}