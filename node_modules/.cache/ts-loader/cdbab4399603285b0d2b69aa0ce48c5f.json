{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/users.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/users.ts", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setStatus = exports.getStatus = exports.editPassword = void 0;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 修改密码\nvar editPassword = exports.editPassword = function editPassword(data) {\n  return (0, _request.default)({\n    'url': '/employee/editPassword',\n    'method': 'put',\n    data: data\n  });\n};\n// 获取营业状态\nvar getStatus = exports.getStatus = function getStatus() {\n  return (0, _request.default)({\n    'url': \"/shop/status\",\n    'method': 'get'\n  });\n};\n// 设置营业状态\nvar setStatus = exports.setStatus = function setStatus(data) {\n  return (0, _request.default)({\n    'url': \"/shop/\" + data,\n    'method': 'put',\n    'data': data\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "editPassword", "exports", "data", "request", "getStatus", "setStatus"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/users.ts"], "sourcesContent": ["import request from '@/utils/request'\r\n// 修改密码\r\nexport const editPassword = (data: any) =>\r\n  request({\r\n    'url': '/employee/editPassword',\r\n    'method': 'put',\r\n    data\r\n  })\r\n  // 获取营业状态\r\n  export const getStatus = () =>\r\n  request({\r\n    'url': `/shop/status`,\r\n    'method': 'get'\r\n  })\r\n    // 设置营业状态\r\n    export const setStatus = (data:any) =>\r\n    request({\r\n      'url': `/shop/`+data,\r\n      'method': 'put',\r\n      'data':data\r\n    })"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA;AACO,IAAMC,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG,SAAfA,YAAYA,CAAIE,IAAS;EAAA,OACpC,IAAAC,gBAAO,EAAC;IACN,KAAK,EAAE,wBAAwB;IAC/B,QAAQ,EAAE,KAAK;IACfD,IAAI,EAAJA;GACD,CAAC;AAAA;AACF;AACO,IAAME,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAG,SAAZA,SAASA,CAAA;EAAA,OACtB,IAAAD,gBAAO,EAAC;IACN,KAAK,gBAAgB;IACrB,QAAQ,EAAE;GACX,CAAC;AAAA;AACA;AACO,IAAME,SAAS,GAAAJ,OAAA,CAAAI,SAAA,GAAG,SAAZA,SAASA,CAAIH,IAAQ;EAAA,OAClC,IAAAC,gBAAO,EAAC;IACN,KAAK,EAAE,WAASD,IAAI;IACpB,QAAQ,EAAE,KAAK;IACf,MAAM,EAACA;GACR,CAAC;AAAA", "ignoreList": []}]}