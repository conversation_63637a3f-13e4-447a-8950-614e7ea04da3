{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue", "mtime": 1656301911000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/asyncToGenerator.js\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _index = require(\"@/api/index\");\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    var _this;\n    (0, _classCallCheck2.default)(this, default_1);\n    _this = _callSuper(this, default_1, arguments);\n    _this.nowIndex = 2 - 1;\n    _this.value = [];\n    _this.tabsParam = ['昨日', '近7日', '近30日', '本周', '本月'];\n    return _this;\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"getNowIndex\",\n    value: function getNowIndex(val) {\n      this.nowIndex = val;\n    }\n    // tab切换\n  }, {\n    key: \"toggleTabs\",\n    value: function toggleTabs(index) {\n      this.nowIndex = index;\n      this.value = [];\n      this.$emit('sendTitleInd', index + 1);\n    }\n    //  数据导出\n    /** 导出按钮操作 */\n  }, {\n    key: \"handleExport\",\n    value: function handleExport() {\n      this.$confirm('是否确认导出最近30天运营数据?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/(0, _asyncToGenerator2.default)(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {\n        var _yield$exportInfor, data, url, a;\n        return regeneratorRuntime.wrap(function (_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 1;\n              return (0, _index.exportInfor)();\n            case 1:\n              _yield$exportInfor = _context.sent;\n              data = _yield$exportInfor.data;\n              url = window.URL.createObjectURL(data);\n              a = document.createElement('a');\n              document.body.appendChild(a);\n              a.href = url;\n              a.download = '运营数据统计报表.xlsx';\n              a.click();\n              window.URL.revokeObjectURL(url);\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))).then(function (response) {});\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)()], default_1.prototype, \"flag\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)()], default_1.prototype, \"tateData\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)()], default_1.prototype, \"turnoverData\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('flag')], default_1.prototype, \"getNowIndex\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'TitleIndex'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_index", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_this", "_classCallCheck2", "nowIndex", "value", "tabsParam", "_inherits2", "_createClass2", "key", "getNowIndex", "val", "toggleTabs", "index", "$emit", "handleExport", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_asyncToGenerator2", "regeneratorRuntime", "mark", "_callee", "_yield$exportInfor", "data", "url", "a", "wrap", "_context", "prev", "next", "exportInfor", "sent", "window", "URL", "createObjectURL", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "href", "download", "click", "revokeObjectURL", "stop", "response", "<PERSON><PERSON>", "__decorate", "Prop", "Watch", "Component", "name", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue?vue&type=script&lang=ts"], "sourcesContent": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport { exportInfor } from '@/api/index'\n@Component({\n  name: 'TitleIndex',\n})\nexport default class extends Vue {\n  @Prop() private flag!: any\n  @Prop() private tateData!: any\n  @Prop() private turnoverData!: any\n\n  nowIndex = 2 - 1\n  value = []\n  tabsParam = ['昨日', '近7日', '近30日', '本周', '本月']\n  @Watch('flag')\n  getNowIndex(val) {\n    this.nowIndex = val\n  }\n  // tab切换\n  toggleTabs(index: number) {\n    this.nowIndex = index\n    this.value = []\n    this.$emit('sendTitleInd', index + 1)\n  }\n  //  数据导出\n  /** 导出按钮操作 */\n  handleExport() {\n    this.$confirm('是否确认导出最近30天运营数据?', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning',\n    })\n      .then(async function () {\n        const { data } = await exportInfor()\n        let url = window.URL.createObjectURL(data)\n        var a = document.createElement('a')\n        document.body.appendChild(a)\n        a.href = url\n        a.download = '运营数据统计报表.xlsx'\n        a.click()\n        window.URL.revokeObjectURL(url)\n      })\n      .then((response) => {})\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAAyC,SAAAE,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAIzC,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAd,OAAA,QAAAW,SAAA;;IAKEE,KAAA,CAAAE,QAAQ,GAAG,CAAC,GAAG,CAAC;IAChBF,KAAA,CAAAG,KAAK,GAAG,EAAE;IACVH,KAAA,CAAAI,SAAS,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAAA,OAAAJ,KAAA;EA+B/C;EAAC,IAAAK,UAAA,CAAAlB,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAAO,aAAA,CAAAnB,OAAA,EAAAW,SAAA;IAAAS,GAAA;IAAAJ,KAAA,EA7BC,SAAAK,WAAWA,CAACC,GAAG;MACb,IAAI,CAACP,QAAQ,GAAGO,GAAG;IACrB;IACA;EAAA;IAAAF,GAAA;IAAAJ,KAAA,EACA,SAAAO,UAAUA,CAACC,KAAa;MACtB,IAAI,CAACT,QAAQ,GAAGS,KAAK;MACrB,IAAI,CAACR,KAAK,GAAG,EAAE;MACf,IAAI,CAACS,KAAK,CAAC,cAAc,EAAED,KAAK,GAAG,CAAC,CAAC;IACvC;IACA;IACA;EAAA;IAAAJ,GAAA;IAAAJ,KAAA,EACA,SAAAU,YAAYA,CAAA;MACV,IAAI,CAACC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,EAAE;QACtCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;OACP,CAAC,CACCC,IAAI,kBAAAC,kBAAA,CAAAhC,OAAA,eAAAiC,kBAAA,CAAAC,IAAA,CAAC,SAAAC,QAAA;QAAA,IAAAC,kBAAA,EAAAC,IAAA,EAAAC,GAAA,EAAAC,CAAA;QAAA,OAAAN,kBAAA,CAAAO,IAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACmB,IAAAC,kBAAW,GAAE;YAAA;cAAAR,kBAAA,GAAAK,QAAA,CAAAI,IAAA;cAA5BR,IAAI,GAAAD,kBAAA,CAAJC,IAAI;cACRC,GAAG,GAAGQ,MAAM,CAACC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC;cACtCE,CAAC,GAAGU,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;cACnCD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACb,CAAC,CAAC;cAC5BA,CAAC,CAACc,IAAI,GAAGf,GAAG;cACZC,CAAC,CAACe,QAAQ,GAAG,eAAe;cAC5Bf,CAAC,CAACgB,KAAK,EAAE;cACTT,MAAM,CAACC,GAAG,CAACS,eAAe,CAAClB,GAAG,CAAC;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAgB,IAAA;UAAA;QAAA,GAAAtB,OAAA;MAAA,CAChC,GAAC,CACDJ,IAAI,CAAC,UAAC2B,QAAQ,EAAI,CAAE,CAAC,CAAC;IAC3B;EAAC;AAAA,EArC0BC,yBAAG,CAsC/B;AArCS,IAAAC,iBAAA,GAAP,IAAAC,0BAAI,GAAE,C,sCAAmB;AAClB,IAAAD,iBAAA,GAAP,IAAAC,0BAAI,GAAE,C,0CAAuB;AACtB,IAAAD,iBAAA,GAAP,IAAAC,0BAAI,GAAE,C,8CAA2B;AAMlC,IAAAD,iBAAA,GADC,IAAAE,2BAAK,EAAC,MAAM,CAAC,C,2CAGb;AAXHnD,SAAA,OAAAiD,iBAAA,GAHC,IAAAG,+BAAS,EAAC;EACTC,IAAI,EAAE;CACP,CAAC,C,YAuCD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAlE,OAAA,G", "ignoreList": []}]}