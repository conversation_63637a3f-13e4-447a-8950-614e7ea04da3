{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/formValidate.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/formValidate.ts", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pastWeek = exports.pastMonth = exports.past7Day = exports.past30Day = exports.getday = exports.get1stAndToday = exports.formatDate = void 0;\nrequire(\"core-js/modules/es7.string.pad-start\");\nrequire(\"core-js/modules/es6.regexp.replace\");\nrequire(\"core-js/modules/es6.regexp.constructor\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nvar formatDate = exports.formatDate = function formatDate() {\n  var now = new Date();\n  var hour = now.getHours();\n  var minute = now.getMinutes();\n  var second = now.getSeconds();\n  if (hour < 10) hour = \"0\".concat(hour);\n  if (minute < 10) minute = \"0\".concat(minute);\n  if (second < 10) second = \"0\".concat(second);\n  return \"\".concat(hour, \":\").concat(minute, \":\").concat(second);\n};\nfunction dateFormat(fmt, time) {\n  var date = new Date(time);\n  var ret;\n  var opt = {\n    // 年\n    \"Y+\": date.getFullYear().toString(),\n    // 月\n    \"m+\": (date.getMonth() + 1).toString(),\n    // 日\n    \"d+\": date.getDate().toString()\n    // 有其他格式化字符需求可以继续添加，必须转化成字符串\n  };\n  for (var k in opt) {\n    ret = new RegExp(\"(\" + k + \")\").exec(fmt);\n    if (ret) {\n      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, \"0\"));\n    }\n  }\n  return fmt;\n}\n// js获取昨日的日期\nvar get1stAndToday = exports.get1stAndToday = function get1stAndToday() {\n  var toData = new Date(new Date().toLocaleDateString()).getTime();\n  var yesterdayStart = toData - 3600 * 24 * 1000;\n  var yesterdayEnd = yesterdayStart + 24 * 60 * 60 * 1000 - 1;\n  var startDay1 = dateFormat(\"YYYY-mm-dd\", yesterdayStart);\n  var endDay1 = dateFormat(\"YYYY-mm-dd\", yesterdayEnd);\n  return [startDay1, endDay1];\n};\n// 获取昨日、今日日期\nvar getday = exports.getday = function getday() {\n  var toData = new Date(new Date().toLocaleDateString()).getTime();\n  var yesterdays = toData - 3600 * 24 * 1000;\n  var yesterday = dateFormat(\"YYYY.mm.dd\", yesterdays);\n  var today = dateFormat(\"YYYY.mm.dd\", toData);\n  return [yesterday, today];\n};\n// 获取近7日\nvar past7Day = exports.past7Day = function past7Day() {\n  var toData = new Date(new Date().toLocaleDateString()).getTime();\n  var past7daysStart = toData - 7 * 3600 * 24 * 1000;\n  var past7daysEnd = toData - 1;\n  var days7Start = dateFormat(\"YYYY-mm-dd\", past7daysStart);\n  var days7End = dateFormat(\"YYYY-mm-dd\", past7daysEnd);\n  return [days7Start, days7End];\n};\n// 获取近30日\nvar past30Day = exports.past30Day = function past30Day() {\n  var toData = new Date(new Date().toLocaleDateString()).getTime();\n  var past30daysStart = toData - 30 * 3600 * 24 * 1000;\n  var past30daysEnd = toData - 1;\n  var days30Start = dateFormat(\"YYYY-mm-dd\", past30daysStart);\n  var days30End = dateFormat(\"YYYY-mm-dd\", past30daysEnd);\n  return [days30Start, days30End];\n};\n// 获取本周\nvar pastWeek = exports.pastWeek = function pastWeek() {\n  var toData = new Date(new Date().toLocaleDateString()).getTime();\n  var nowDayOfWeek = new Date().getDay();\n  var weekStartData = toData - (nowDayOfWeek - 1) * 24 * 60 * 60 * 1000;\n  var weekEndData = toData + (7 - nowDayOfWeek) * 24 * 60 * 60 * 1000;\n  var weekStart = dateFormat(\"YYYY-mm-dd\", weekStartData);\n  var weekEnd = dateFormat(\"YYYY-mm-dd\", weekEndData);\n  return [weekStart, weekEnd];\n};\n// 获取本月\nvar pastMonth = exports.pastMonth = function pastMonth() {\n  var year = new Date().getFullYear();\n  var month = new Date().getMonth();\n  var monthStartData = new Date(year, month, 1).getTime();\n  var monthEndData = new Date(year, month + 1, 0).getTime() + 24 * 60 * 60 * 1000 - 1;\n  var monthStart = dateFormat(\"YYYY-mm-dd\", monthStartData);\n  var monthEnd = dateFormat(\"YYYY-mm-dd\", monthEndData);\n  return [monthStart, monthEnd];\n};", {"version": 3, "names": ["formatDate", "exports", "now", "Date", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "concat", "dateFormat", "fmt", "time", "date", "ret", "opt", "getFullYear", "toString", "getMonth", "getDate", "k", "RegExp", "exec", "replace", "length", "padStart", "get1stAndToday", "toData", "toLocaleDateString", "getTime", "yesterdayStart", "yesterdayEnd", "startDay1", "endDay1", "getday", "yesterdays", "yesterday", "today", "past7Day", "past7daysStart", "past7daysEnd", "days7Start", "days7End", "past30Day", "past30daysStart", "past30daysEnd", "days30Start", "days30End", "pastWeek", "nowDayOfWeek", "getDay", "weekStartData", "weekEndData", "weekStart", "weekEnd", "pastMonth", "year", "month", "monthStartData", "monthEndData", "monthStart", "monthEnd"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/formValidate.ts"], "sourcesContent": ["export const formatDate = () => {\r\n  const now = new Date();\r\n  let hour: string | number = now.getHours();\r\n  let minute: string | number = now.getMinutes();\r\n  let second: string | number = now.getSeconds();\r\n  if (hour < 10) hour = `0${hour}`;\r\n  if (minute < 10) minute = `0${minute}`;\r\n  if (second < 10) second = `0${second}`;\r\n  return `${hour}:${minute}:${second}`;\r\n};\r\n\r\nfunction dateFormat(fmt: any, time: any) {\r\n  let date = new Date(time);\r\n  let ret;\r\n  const opt = {\r\n    // 年\r\n    \"Y+\": date.getFullYear().toString(),\r\n    // 月\r\n    \"m+\": (date.getMonth() + 1).toString(),\r\n    // 日\r\n    \"d+\": date.getDate().toString()\r\n    // 有其他格式化字符需求可以继续添加，必须转化成字符串\r\n  } as any;\r\n  for (const k in opt) {\r\n    ret = new RegExp(\"(\" + k + \")\").exec(fmt);\r\n    if (ret) {\r\n      fmt = fmt.replace(\r\n        ret[1],\r\n        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, \"0\")\r\n      );\r\n    }\r\n  }\r\n  return fmt;\r\n}\r\n\r\n// js获取昨日的日期\r\nexport const get1stAndToday = () => {\r\n  let toData = new Date(new Date().toLocaleDateString()).getTime();\r\n  let yesterdayStart = toData - 3600 * 24 * 1000;\r\n  let yesterdayEnd = yesterdayStart + 24 * 60 * 60 * 1000 - 1;\r\n  let startDay1 = dateFormat(\"YYYY-mm-dd\", yesterdayStart);\r\n  let endDay1 = dateFormat(\"YYYY-mm-dd\", yesterdayEnd);\r\n  return [startDay1, endDay1];\r\n};\r\n// 获取昨日、今日日期\r\nexport const getday = () => {\r\n  let toData = new Date(new Date().toLocaleDateString()).getTime();\r\n  let yesterdays= toData - 3600 * 24 * 1000;\r\n  let yesterday = dateFormat(\"YYYY.mm.dd\", yesterdays);\r\n  let today = dateFormat(\"YYYY.mm.dd\", toData);\r\n  return [yesterday,today];\r\n};\r\n\r\n// 获取近7日\r\nexport const past7Day = () => {\r\n  let toData = new Date(new Date().toLocaleDateString()).getTime();\r\n  let past7daysStart = toData - 7 * 3600 * 24 * 1000;\r\n  let past7daysEnd = toData - 1;\r\n  let days7Start = dateFormat(\"YYYY-mm-dd\", past7daysStart);\r\n  let days7End = dateFormat(\"YYYY-mm-dd\", past7daysEnd);\r\n  return [days7Start, days7End];\r\n};\r\n\r\n// 获取近30日\r\nexport const past30Day = () => {\r\n  let toData = new Date(new Date().toLocaleDateString()).getTime();\r\n  let past30daysStart = toData - 30 * 3600 * 24 * 1000;\r\n  let past30daysEnd = toData - 1;\r\n  let days30Start = dateFormat(\"YYYY-mm-dd\", past30daysStart);\r\n  let days30End = dateFormat(\"YYYY-mm-dd\", past30daysEnd);\r\n  return [days30Start, days30End];\r\n};\r\n// 获取本周\r\nexport const pastWeek = () => {\r\n  let toData = new Date(new Date().toLocaleDateString()).getTime();\r\n  var nowDayOfWeek = new Date().getDay();\r\n  const weekStartData = toData - (nowDayOfWeek - 1) * 24 * 60 * 60 * 1000;\r\n  const weekEndData = toData + (7 - nowDayOfWeek) * 24 * 60 * 60 * 1000;\r\n  let weekStart = dateFormat(\"YYYY-mm-dd\", weekStartData);\r\n  let weekEnd = dateFormat(\"YYYY-mm-dd\", weekEndData);\r\n  return [weekStart, weekEnd];\r\n};\r\n// 获取本月\r\nexport const pastMonth = () => {\r\n  let year = new Date().getFullYear()\r\n  let month =new Date().getMonth()\r\n  const monthStartData = new Date(year, month, 1).getTime()\r\n  const monthEndData = new Date(year, month + 1, 0).getTime() + 24 * 60 * 60 * 1000 - 1\r\n  let monthStart = dateFormat(\"YYYY-mm-dd\", monthStartData);\r\n  let monthEnd = dateFormat(\"YYYY-mm-dd\", monthEndData);\r\n  return [monthStart, monthEnd];\r\n};\r\n"], "mappings": ";;;;;;;;;;AAAO,IAAMA,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAbA,UAAUA,CAAA,EAAQ;EAC7B,IAAME,GAAG,GAAG,IAAIC,IAAI,EAAE;EACtB,IAAIC,IAAI,GAAoBF,GAAG,CAACG,QAAQ,EAAE;EAC1C,IAAIC,MAAM,GAAoBJ,GAAG,CAACK,UAAU,EAAE;EAC9C,IAAIC,MAAM,GAAoBN,GAAG,CAACO,UAAU,EAAE;EAC9C,IAAIL,IAAI,GAAG,EAAE,EAAEA,IAAI,OAAAM,MAAA,CAAON,IAAI,CAAE;EAChC,IAAIE,MAAM,GAAG,EAAE,EAAEA,MAAM,OAAAI,MAAA,CAAOJ,MAAM,CAAE;EACtC,IAAIE,MAAM,GAAG,EAAE,EAAEA,MAAM,OAAAE,MAAA,CAAOF,MAAM,CAAE;EACtC,UAAAE,MAAA,CAAUN,IAAI,OAAAM,MAAA,CAAIJ,MAAM,OAAAI,MAAA,CAAIF,MAAM;AACpC,CAAC;AAED,SAASG,UAAUA,CAACC,GAAQ,EAAEC,IAAS;EACrC,IAAIC,IAAI,GAAG,IAAIX,IAAI,CAACU,IAAI,CAAC;EACzB,IAAIE,GAAG;EACP,IAAMC,GAAG,GAAG;IACV;IACA,IAAI,EAAEF,IAAI,CAACG,WAAW,EAAE,CAACC,QAAQ,EAAE;IACnC;IACA,IAAI,EAAE,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,EAAED,QAAQ,EAAE;IACtC;IACA,IAAI,EAAEJ,IAAI,CAACM,OAAO,EAAE,CAACF,QAAQ;IAC7B;GACM;EACR,KAAK,IAAMG,CAAC,IAAIL,GAAG,EAAE;IACnBD,GAAG,GAAG,IAAIO,MAAM,CAAC,GAAG,GAAGD,CAAC,GAAG,GAAG,CAAC,CAACE,IAAI,CAACX,GAAG,CAAC;IACzC,IAAIG,GAAG,EAAE;MACPH,GAAG,GAAGA,GAAG,CAACY,OAAO,CACfT,GAAG,CAAC,CAAC,CAAC,EACNA,GAAG,CAAC,CAAC,CAAC,CAACU,MAAM,IAAI,CAAC,GAAGT,GAAG,CAACK,CAAC,CAAC,GAAGL,GAAG,CAACK,CAAC,CAAC,CAACK,QAAQ,CAACX,GAAG,CAAC,CAAC,CAAC,CAACU,MAAM,EAAE,GAAG,CAAC,CAClE;;;EAGL,OAAOb,GAAG;AACZ;AAEA;AACO,IAAMe,cAAc,GAAA1B,OAAA,CAAA0B,cAAA,GAAG,SAAjBA,cAAcA,CAAA,EAAQ;EACjC,IAAIC,MAAM,GAAG,IAAIzB,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC0B,kBAAkB,EAAE,CAAC,CAACC,OAAO,EAAE;EAChE,IAAIC,cAAc,GAAGH,MAAM,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI;EAC9C,IAAII,YAAY,GAAGD,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC;EAC3D,IAAIE,SAAS,GAAGtB,UAAU,CAAC,YAAY,EAAEoB,cAAc,CAAC;EACxD,IAAIG,OAAO,GAAGvB,UAAU,CAAC,YAAY,EAAEqB,YAAY,CAAC;EACpD,OAAO,CAACC,SAAS,EAAEC,OAAO,CAAC;AAC7B,CAAC;AACD;AACO,IAAMC,MAAM,GAAAlC,OAAA,CAAAkC,MAAA,GAAG,SAATA,MAAMA,CAAA,EAAQ;EACzB,IAAIP,MAAM,GAAG,IAAIzB,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC0B,kBAAkB,EAAE,CAAC,CAACC,OAAO,EAAE;EAChE,IAAIM,UAAU,GAAER,MAAM,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI;EACzC,IAAIS,SAAS,GAAG1B,UAAU,CAAC,YAAY,EAAEyB,UAAU,CAAC;EACpD,IAAIE,KAAK,GAAG3B,UAAU,CAAC,YAAY,EAAEiB,MAAM,CAAC;EAC5C,OAAO,CAACS,SAAS,EAACC,KAAK,CAAC;AAC1B,CAAC;AAED;AACO,IAAMC,QAAQ,GAAAtC,OAAA,CAAAsC,QAAA,GAAG,SAAXA,QAAQA,CAAA,EAAQ;EAC3B,IAAIX,MAAM,GAAG,IAAIzB,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC0B,kBAAkB,EAAE,CAAC,CAACC,OAAO,EAAE;EAChE,IAAIU,cAAc,GAAGZ,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI;EAClD,IAAIa,YAAY,GAAGb,MAAM,GAAG,CAAC;EAC7B,IAAIc,UAAU,GAAG/B,UAAU,CAAC,YAAY,EAAE6B,cAAc,CAAC;EACzD,IAAIG,QAAQ,GAAGhC,UAAU,CAAC,YAAY,EAAE8B,YAAY,CAAC;EACrD,OAAO,CAACC,UAAU,EAAEC,QAAQ,CAAC;AAC/B,CAAC;AAED;AACO,IAAMC,SAAS,GAAA3C,OAAA,CAAA2C,SAAA,GAAG,SAAZA,SAASA,CAAA,EAAQ;EAC5B,IAAIhB,MAAM,GAAG,IAAIzB,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC0B,kBAAkB,EAAE,CAAC,CAACC,OAAO,EAAE;EAChE,IAAIe,eAAe,GAAGjB,MAAM,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI;EACpD,IAAIkB,aAAa,GAAGlB,MAAM,GAAG,CAAC;EAC9B,IAAImB,WAAW,GAAGpC,UAAU,CAAC,YAAY,EAAEkC,eAAe,CAAC;EAC3D,IAAIG,SAAS,GAAGrC,UAAU,CAAC,YAAY,EAAEmC,aAAa,CAAC;EACvD,OAAO,CAACC,WAAW,EAAEC,SAAS,CAAC;AACjC,CAAC;AACD;AACO,IAAMC,QAAQ,GAAAhD,OAAA,CAAAgD,QAAA,GAAG,SAAXA,QAAQA,CAAA,EAAQ;EAC3B,IAAIrB,MAAM,GAAG,IAAIzB,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC0B,kBAAkB,EAAE,CAAC,CAACC,OAAO,EAAE;EAChE,IAAIoB,YAAY,GAAG,IAAI/C,IAAI,EAAE,CAACgD,MAAM,EAAE;EACtC,IAAMC,aAAa,GAAGxB,MAAM,GAAG,CAACsB,YAAY,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACvE,IAAMG,WAAW,GAAGzB,MAAM,GAAG,CAAC,CAAC,GAAGsB,YAAY,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACrE,IAAII,SAAS,GAAG3C,UAAU,CAAC,YAAY,EAAEyC,aAAa,CAAC;EACvD,IAAIG,OAAO,GAAG5C,UAAU,CAAC,YAAY,EAAE0C,WAAW,CAAC;EACnD,OAAO,CAACC,SAAS,EAAEC,OAAO,CAAC;AAC7B,CAAC;AACD;AACO,IAAMC,SAAS,GAAAvD,OAAA,CAAAuD,SAAA,GAAG,SAAZA,SAASA,CAAA,EAAQ;EAC5B,IAAIC,IAAI,GAAG,IAAItD,IAAI,EAAE,CAACc,WAAW,EAAE;EACnC,IAAIyC,KAAK,GAAE,IAAIvD,IAAI,EAAE,CAACgB,QAAQ,EAAE;EAChC,IAAMwC,cAAc,GAAG,IAAIxD,IAAI,CAACsD,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC,CAAC5B,OAAO,EAAE;EACzD,IAAM8B,YAAY,GAAG,IAAIzD,IAAI,CAACsD,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC5B,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC;EACrF,IAAI+B,UAAU,GAAGlD,UAAU,CAAC,YAAY,EAAEgD,cAAc,CAAC;EACzD,IAAIG,QAAQ,GAAGnD,UAAU,CAAC,YAAY,EAAEiD,YAAY,CAAC;EACrD,OAAO,CAACC,UAAU,EAAEC,QAAQ,CAAC;AAC/B,CAAC", "ignoreList": []}]}