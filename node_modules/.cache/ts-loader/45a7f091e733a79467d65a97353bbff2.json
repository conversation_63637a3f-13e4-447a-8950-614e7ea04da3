{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/cookies.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/cookies.ts", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setUserInfo = exports.setToken = exports.setStoreId = exports.setSidebarStatus = exports.setPrint = exports.setNewData = exports.removeUserInfo = exports.removeToken = exports.removeStoreId = exports.removePrint = exports.getUserInfo = exports.getToken = exports.getStoreId = exports.getSidebarStatus = exports.getPrint = exports.getNewData = void 0;\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\n// App\nvar sidebarStatusKey = 'sidebar_status';\nvar getSidebarStatus = exports.getSidebarStatus = function getSidebarStatus() {\n  return _jsCookie.default.get(sidebarStatusKey);\n};\nvar setSidebarStatus = exports.setSidebarStatus = function setSidebarStatus(sidebarStatus) {\n  return _jsCookie.default.set(sidebarStatusKey, sidebarStatus);\n};\n// User\nvar storeId = 'storeId';\nvar getStoreId = exports.getStoreId = function getStoreId() {\n  return _jsCookie.default.get(storeId);\n};\nvar setStoreId = exports.setStoreId = function setStoreId(id) {\n  return _jsCookie.default.set(storeId, id);\n};\nvar removeStoreId = exports.removeStoreId = function removeStoreId() {\n  return _jsCookie.default.remove(storeId);\n};\n// User\nvar tokenKey = 'token';\nvar getToken = exports.getToken = function getToken() {\n  return _jsCookie.default.get(tokenKey);\n};\nvar setToken = exports.setToken = function setToken(token) {\n  return _jsCookie.default.set(tokenKey, token);\n};\nvar removeToken = exports.removeToken = function removeToken() {\n  return _jsCookie.default.remove(tokenKey);\n};\n// userInfo\nvar userInfoKey = 'userInfo';\nvar getUserInfo = exports.getUserInfo = function getUserInfo() {\n  return _jsCookie.default.get(userInfoKey);\n};\nvar setUserInfo = exports.setUserInfo = function setUserInfo(useInfor) {\n  return _jsCookie.default.set(userInfoKey, useInfor);\n};\nvar removeUserInfo = exports.removeUserInfo = function removeUserInfo() {\n  return _jsCookie.default.remove(userInfoKey);\n};\n// printinfo\nvar printKey = 'print';\nvar getPrint = exports.getPrint = function getPrint() {\n  return _jsCookie.default.get(printKey);\n};\nvar setPrint = exports.setPrint = function setPrint(useInfor) {\n  return _jsCookie.default.set(printKey, useInfor);\n};\nvar removePrint = exports.removePrint = function removePrint() {\n  return _jsCookie.default.remove(printKey);\n};\n// 获取消息\nvar newData = 'new';\nvar getNewData = exports.getNewData = function getNewData() {\n  return _jsCookie.default.get(newData);\n};\nvar setNewData = exports.setNewData = function setNewData(val) {\n  return _jsCookie.default.set(newData, val);\n};", {"version": 3, "names": ["_js<PERSON><PERSON>ie", "_interopRequireDefault", "require", "sidebarStatusKey", "getSidebarStatus", "exports", "Cookies", "get", "setSidebarStatus", "sidebarStatus", "set", "storeId", "getStoreId", "setStoreId", "id", "removeStoreId", "remove", "<PERSON><PERSON><PERSON>", "getToken", "setToken", "token", "removeToken", "userInfoKey", "getUserInfo", "setUserInfo", "useInfor", "removeUserInfo", "printKey", "getPrint", "setPrint", "removePrint", "newData", "getNewData", "setNewData", "val"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/cookies.ts"], "sourcesContent": ["import Cookies from 'js-cookie';\r\n\r\n// App\r\nconst sidebarStatusKey = 'sidebar_status';\r\nexport const getSidebarStatus = () => Cookies.get(sidebarStatusKey);\r\nexport const setSidebarStatus = (sidebarStatus: string) => Cookies.set(sidebarStatusKey, sidebarStatus);\r\n\r\n// User\r\nconst storeId = 'storeId';\r\nexport const getStoreId = () => Cookies.get(storeId);\r\nexport const setStoreId = (id: string) => Cookies.set(storeId, id);\r\nexport const removeStoreId = () => Cookies.remove(storeId);\r\n\r\n// User\r\nconst tokenKey = 'token';\r\nexport const getToken = () => Cookies.get(tokenKey);\r\nexport const setToken = (token: string) => Cookies.set(tokenKey, token);\r\nexport const removeToken = () => Cookies.remove(tokenKey);\r\n\r\n// userInfo\r\n\r\nconst userInfoKey = 'userInfo';\r\nexport const getUserInfo = () => Cookies.get(userInfoKey);\r\nexport const setUserInfo = (useInfor: Object) => Cookies.set(userInfoKey, useInfor);\r\nexport const removeUserInfo = () => Cookies.remove(userInfoKey);\r\n\r\n// printinfo\r\n\r\nconst printKey = 'print';\r\nexport const getPrint = () => Cookies.get(printKey);\r\nexport const setPrint = (useInfor: Object) => Cookies.set(printKey, useInfor);\r\nexport const removePrint = () => Cookies.remove(printKey);\r\n\r\n// 获取消息\r\nconst newData = 'new';\r\nexport const getNewData = () => Cookies.get(newData);\r\nexport const setNewData = (val: Object) => Cookies.set(newData, val);\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACA,IAAMC,gBAAgB,GAAG,gBAAgB;AAClC,IAAMC,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAASE,iBAAO,CAACC,GAAG,CAACJ,gBAAgB,CAAC;AAAA;AAC5D,IAAMK,gBAAgB,GAAAH,OAAA,CAAAG,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIC,aAAqB;EAAA,OAAKH,iBAAO,CAACI,GAAG,CAACP,gBAAgB,EAAEM,aAAa,CAAC;AAAA;AAEvG;AACA,IAAME,OAAO,GAAG,SAAS;AAClB,IAAMC,UAAU,GAAAP,OAAA,CAAAO,UAAA,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAASN,iBAAO,CAACC,GAAG,CAACI,OAAO,CAAC;AAAA;AAC7C,IAAME,UAAU,GAAAR,OAAA,CAAAQ,UAAA,GAAG,SAAbA,UAAUA,CAAIC,EAAU;EAAA,OAAKR,iBAAO,CAACI,GAAG,CAACC,OAAO,EAAEG,EAAE,CAAC;AAAA;AAC3D,IAAMC,aAAa,GAAAV,OAAA,CAAAU,aAAA,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAST,iBAAO,CAACU,MAAM,CAACL,OAAO,CAAC;AAAA;AAE1D;AACA,IAAMM,QAAQ,GAAG,OAAO;AACjB,IAAMC,QAAQ,GAAAb,OAAA,CAAAa,QAAA,GAAG,SAAXA,QAAQA,CAAA;EAAA,OAASZ,iBAAO,CAACC,GAAG,CAACU,QAAQ,CAAC;AAAA;AAC5C,IAAME,QAAQ,GAAAd,OAAA,CAAAc,QAAA,GAAG,SAAXA,QAAQA,CAAIC,KAAa;EAAA,OAAKd,iBAAO,CAACI,GAAG,CAACO,QAAQ,EAAEG,KAAK,CAAC;AAAA;AAChE,IAAMC,WAAW,GAAAhB,OAAA,CAAAgB,WAAA,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAASf,iBAAO,CAACU,MAAM,CAACC,QAAQ,CAAC;AAAA;AAEzD;AAEA,IAAMK,WAAW,GAAG,UAAU;AACvB,IAAMC,WAAW,GAAAlB,OAAA,CAAAkB,WAAA,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAASjB,iBAAO,CAACC,GAAG,CAACe,WAAW,CAAC;AAAA;AAClD,IAAME,WAAW,GAAAnB,OAAA,CAAAmB,WAAA,GAAG,SAAdA,WAAWA,CAAIC,QAAgB;EAAA,OAAKnB,iBAAO,CAACI,GAAG,CAACY,WAAW,EAAEG,QAAQ,CAAC;AAAA;AAC5E,IAAMC,cAAc,GAAArB,OAAA,CAAAqB,cAAA,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAASpB,iBAAO,CAACU,MAAM,CAACM,WAAW,CAAC;AAAA;AAE/D;AAEA,IAAMK,QAAQ,GAAG,OAAO;AACjB,IAAMC,QAAQ,GAAAvB,OAAA,CAAAuB,QAAA,GAAG,SAAXA,QAAQA,CAAA;EAAA,OAAStB,iBAAO,CAACC,GAAG,CAACoB,QAAQ,CAAC;AAAA;AAC5C,IAAME,QAAQ,GAAAxB,OAAA,CAAAwB,QAAA,GAAG,SAAXA,QAAQA,CAAIJ,QAAgB;EAAA,OAAKnB,iBAAO,CAACI,GAAG,CAACiB,QAAQ,EAAEF,QAAQ,CAAC;AAAA;AACtE,IAAMK,WAAW,GAAAzB,OAAA,CAAAyB,WAAA,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAASxB,iBAAO,CAACU,MAAM,CAACW,QAAQ,CAAC;AAAA;AAEzD;AACA,IAAMI,OAAO,GAAG,KAAK;AACd,IAAMC,UAAU,GAAA3B,OAAA,CAAA2B,UAAA,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS1B,iBAAO,CAACC,GAAG,CAACwB,OAAO,CAAC;AAAA;AAC7C,IAAME,UAAU,GAAA5B,OAAA,CAAA4B,UAAA,GAAG,SAAbA,UAAUA,CAAIC,GAAW;EAAA,OAAK5B,iBAAO,CAACI,GAAG,CAACqB,OAAO,EAAEG,GAAG,CAAC;AAAA", "ignoreList": []}]}