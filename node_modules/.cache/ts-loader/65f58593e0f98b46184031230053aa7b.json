{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/store/index.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/store/index.ts", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _vuex = _interopRequireDefault(require(\"vuex\"));\n_vue.default.use(_vuex.default);\nvar _default = exports.default = new _vuex.default.Store({});", {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vuex", "<PERSON><PERSON>", "use", "Vuex", "_default", "exports", "default", "Store"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/store/index.ts"], "sourcesContent": ["import Vue from 'vue'\r\nimport Vuex from 'vuex'\r\nimport { IAppState } from './modules/app'\r\nimport { IUserState } from './modules/user'\r\n\r\nVue.use(Vuex)\r\n\r\nexport interface IRootState {\r\n  app: IAppState\r\n  user: IUserState\r\n}\r\n\r\nexport default new Vuex.Store<IRootState>({})\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AAIAE,YAAG,CAACC,GAAG,CAACC,aAAI,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOE,IAAIH,aAAI,CAACI,KAAK,CAAa,EAAE,CAAC", "ignoreList": []}]}