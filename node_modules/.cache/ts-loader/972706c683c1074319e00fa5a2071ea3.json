{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _orderList = _interopRequireDefault(require(\"./orderList.vue\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    (0, _classCallCheck2.default)(this, default_1);\n    return _callSuper(this, default_1, arguments);\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)()], default_1.prototype, \"dishesData\", void 0);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  components: {\n    orderList: _orderList.default\n  },\n  name: 'cuisineview'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_orderList", "_interopRequireDefault", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_classCallCheck2", "arguments", "_inherits2", "_createClass2", "<PERSON><PERSON>", "__decorate", "Prop", "Component", "components", "orderList", "name", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { Component, Vue, Prop } from 'vue-property-decorator'\r\nimport orderList from './orderList.vue'\r\n@Component({\r\n  components: { orderList },\r\n  name: 'cuisineview',\r\n})\r\nexport default class extends Vue {\r\n  @Prop() private dishesData!: any\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAuC,SAAAG,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAKvC,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAb,OAAA,QAAAW,SAAA;IAAA,OAAAhB,UAAA,OAAAgB,SAAA,EAAAG,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAf,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAAI,aAAA,CAAAhB,OAAA,EAAAW,SAAA;AAAA,EAA6BM,yBAAG,CAE/B;AADS,IAAAC,iBAAA,GAAP,IAAAC,0BAAI,GAAE,C,4CAAyB;AADlCR,SAAA,OAAAO,iBAAA,GAJC,IAAAE,+BAAS,EAAC;EACTC,UAAU,EAAE;IAAEC,SAAS,EAATA;EAAS,CAAE;EACzBC,IAAI,EAAE;CACP,CAAC,C,YAGD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAzB,OAAA,G", "ignoreList": []}]}