{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.string.starts-with\");\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _pathToRegexp = _interopRequireDefault(require(\"path-to-regexp\"));\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    var _this;\n    (0, _classCallCheck2.default)(this, default_1);\n    _this = _callSuper(this, default_1, arguments);\n    _this.breadcrumbs = [];\n    return _this;\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"onRouteChange\",\n    value: function onRouteChange(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return;\n      }\n      this.getBreadcrumb();\n    }\n  }, {\n    key: \"created\",\n    value: function created() {\n      this.getBreadcrumb();\n    }\n  }, {\n    key: \"getBreadcrumb\",\n    value: function getBreadcrumb() {\n      var matched = this.$route.matched.filter(function (item) {\n        return item.meta && item.meta.title;\n      });\n      var first = matched[0];\n      // if (!this.isDashboard(first)) {\n      //   matched = [\n      //     { path: '/', meta: { title: '集团管理' } } as RouteRecord\n      //   ].concat(matched)\n      // }\n      this.breadcrumbs = matched.filter(function (item) {\n        return item.meta && item.meta.title && item.meta.breadcrumb !== false;\n      });\n    }\n  }, {\n    key: \"isDashboard\",\n    value: function isDashboard(route) {\n      var name = route && route.meta && route.meta.title;\n      return name === '集团管理';\n    }\n  }, {\n    key: \"pathCompile\",\n    value: function pathCompile(path) {\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\n      var params = this.$route.params;\n      var toPath = _pathToRegexp.default.compile(path);\n      return toPath(params);\n    }\n  }, {\n    key: \"handleLink\",\n    value: function handleLink(item) {\n      var redirect = item.redirect,\n        path = item.path;\n      if (redirect) {\n        this.$router.push(redirect);\n        return;\n      }\n      this.$router.push(this.pathCompile(path));\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('$route')], default_1.prototype, \"onRouteChange\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  'name': 'Breadcrumb'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_pathToRegexp", "_interopRequireDefault", "require", "_vuePropertyDecorator", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_this", "_classCallCheck2", "breadcrumbs", "_inherits2", "_createClass2", "key", "value", "onRouteChange", "route", "path", "startsWith", "getBreadcrumb", "created", "matched", "$route", "filter", "item", "meta", "title", "first", "breadcrumb", "isDashboard", "name", "pathCompile", "params", "to<PERSON><PERSON>", "pathToRegexp", "compile", "handleLink", "redirect", "$router", "push", "<PERSON><PERSON>", "__decorate", "Watch", "Component", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { RouteRecord, Route } from 'vue-router'\r\n\r\n@Component({\r\n  'name': 'Breadcrumb'\r\n})\r\n\r\nexport default class extends Vue {\r\n  private breadcrumbs: RouteRecord[] = []\r\n  @Watch('$route')\r\n  private onRouteChange(route: Route) {\r\n    // if you go to the redirect page, do not update the breadcrumbs\r\n    if (route.path.startsWith('/redirect/')) {\r\n      return\r\n    }\r\n\r\n    this.getBreadcrumb()\r\n  }\r\n\r\n  created () {\r\n    this.getBreadcrumb()\r\n  }\r\n\r\n  private getBreadcrumb () {\r\n    let matched = this.$route.matched.filter(\r\n      item => item.meta && item.meta.title\r\n    )\r\n    const first = matched[0]\r\n    // if (!this.isDashboard(first)) {\r\n    //   matched = [\r\n    //     { path: '/', meta: { title: '集团管理' } } as RouteRecord\r\n    //   ].concat(matched)\r\n    // }\r\n    this.breadcrumbs = matched.filter(item => {\r\n      return item.meta && item.meta.title && item.meta.breadcrumb !== false\r\n    })\r\n  }\r\n\r\n  private isDashboard (route: RouteRecord) {\r\n    const name = route && route.meta && route.meta.title\r\n    return name === '集团管理'\r\n  }\r\n\r\n  private pathCompile (path: string) {\r\n    // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n    const { params } = this.$route\r\n    const toPath = pathToRegexp.compile(path)\r\n    return toPath(params)\r\n  }\r\n\r\n  private handleLink (item: any) {\r\n    const { redirect, path } = item\r\n    if (redirect) {\r\n      this.$router.push(redirect)\r\n      return\r\n    }\r\n    this.$router.push(this.pathCompile(path))\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AACA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAA8D,SAAAE,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAO9D,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAd,OAAA,QAAAW,SAAA;;IACUE,KAAA,CAAAE,WAAW,GAAkB,EAAE;IAAA,OAAAF,KAAA;EAkDzC;EAAC,IAAAG,UAAA,CAAAhB,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAAK,aAAA,CAAAjB,OAAA,EAAAW,SAAA;IAAAO,GAAA;IAAAC,KAAA,EAhDS,SAAAC,aAAaA,CAACC,KAAY;MAChC;MACA,IAAIA,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QACvC;;MAGF,IAAI,CAACC,aAAa,EAAE;IACtB;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAM,OAAOA,CAAA;MACL,IAAI,CAACD,aAAa,EAAE;IACtB;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAEO,SAAAK,aAAaA,CAAA;MACnB,IAAIE,OAAO,GAAG,IAAI,CAACC,MAAM,CAACD,OAAO,CAACE,MAAM,CACtC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,IAAI,IAAID,IAAI,CAACC,IAAI,CAACC,KAAK;MAAA,EACrC;MACD,IAAMC,KAAK,GAAGN,OAAO,CAAC,CAAC,CAAC;MACxB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACX,WAAW,GAAGW,OAAO,CAACE,MAAM,CAAC,UAAAC,IAAI,EAAG;QACvC,OAAOA,IAAI,CAACC,IAAI,IAAID,IAAI,CAACC,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACC,IAAI,CAACG,UAAU,KAAK,KAAK;MACvE,CAAC,CAAC;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAEO,SAAAe,WAAWA,CAAEb,KAAkB;MACrC,IAAMc,IAAI,GAAGd,KAAK,IAAIA,KAAK,CAACS,IAAI,IAAIT,KAAK,CAACS,IAAI,CAACC,KAAK;MACpD,OAAOI,IAAI,KAAK,MAAM;IACxB;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAEO,SAAAiB,WAAWA,CAAEd,IAAY;MAC/B;MACA,IAAQe,MAAM,GAAK,IAAI,CAACV,MAAM,CAAtBU,MAAM;MACd,IAAMC,MAAM,GAAGC,qBAAY,CAACC,OAAO,CAAClB,IAAI,CAAC;MACzC,OAAOgB,MAAM,CAACD,MAAM,CAAC;IACvB;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAEO,SAAAsB,UAAUA,CAAEZ,IAAS;MAC3B,IAAQa,QAAQ,GAAWb,IAAI,CAAvBa,QAAQ;QAAEpB,IAAI,GAAKO,IAAI,CAAbP,IAAI;MACtB,IAAIoB,QAAQ,EAAE;QACZ,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,QAAQ,CAAC;QAC3B;;MAEF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAACR,WAAW,CAACd,IAAI,CAAC,CAAC;IAC3C;EAAC;AAAA,EAlD0BuB,yBAAG,CAmD/B;AAhDC,IAAAC,iBAAA,GADC,IAAAC,2BAAK,EAAC,QAAQ,CAAC,C,6CAQf;AAVHpC,SAAA,OAAAmC,iBAAA,GAJC,IAAAE,+BAAS,EAAC;EACT,MAAM,EAAE;CACT,CAAC,C,YAqDD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAlD,OAAA,G", "ignoreList": []}]}