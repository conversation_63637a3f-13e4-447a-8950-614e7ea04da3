{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/mixin/resize.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/mixin/resize.ts", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _app = require(\"@/store/modules/app\");\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar WIDTH = 992; // refer to Bootstrap's responsive design\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    (0, _classCallCheck2.default)(this, default_1);\n    return _callSuper(this, default_1, arguments);\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"device\",\n    get: function get() {\n      return _app.AppModule.device;\n    }\n  }, {\n    key: \"sidebar\",\n    get: function get() {\n      return _app.AppModule.sidebar;\n    }\n  }, {\n    key: \"onRouteChange\",\n    value: function onRouteChange() {\n      if (this.device === _app.DeviceType.Mobile && this.sidebar.opened) {\n        _app.AppModule.CloseSideBar(false);\n      }\n    }\n  }, {\n    key: \"beforeMount\",\n    value: function beforeMount() {\n      window.addEventListener('resize', this.resizeHandler);\n    }\n  }, {\n    key: \"mounted\",\n    value: function mounted() {\n      var isMobile = this.isMobile();\n      if (isMobile) {\n        _app.AppModule.ToggleDevice(_app.DeviceType.Mobile);\n        _app.AppModule.CloseSideBar(true);\n      }\n    }\n  }, {\n    key: \"beforeDestroy\",\n    value: function beforeDestroy() {\n      window.removeEventListener('resize', this.resizeHandler);\n    }\n  }, {\n    key: \"isMobile\",\n    value: function isMobile() {\n      var rect = document.body.getBoundingClientRect();\n      return rect.width - 1 < WIDTH;\n    }\n  }, {\n    key: \"resizeHandler\",\n    value: function resizeHandler() {\n      if (!document.hidden) {\n        var isMobile = this.isMobile();\n        _app.AppModule.ToggleDevice(isMobile ? _app.DeviceType.Mobile : _app.DeviceType.Desktop);\n        if (isMobile) {\n          _app.AppModule.CloseSideBar(true);\n        }\n      }\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('$route')], default_1.prototype, \"onRouteChange\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  'name': 'ResizeMixin'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_app", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "WIDTH", "default_1", "_Vue", "_classCallCheck2", "arguments", "_inherits2", "_createClass2", "key", "get", "AppModule", "device", "sidebar", "value", "onRouteChange", "DeviceType", "Mobile", "opened", "CloseSideBar", "beforeMount", "window", "addEventListener", "resize<PERSON><PERSON>ler", "mounted", "isMobile", "ToggleDevice", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "rect", "document", "body", "getBoundingClientRect", "width", "hidden", "Desktop", "<PERSON><PERSON>", "__decorate", "Watch", "Component", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/mixin/resize.ts"], "sourcesContent": ["import { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { AppModule, DeviceType } from '@/store/modules/app'\r\n\r\nconst WIDTH = 992; // refer to Bootstrap's responsive design\r\n\r\n@Component({\r\n    'name': 'ResizeMixin'\r\n})\r\nexport default class extends Vue {\r\n    get device () {\r\n      return AppModule.device\r\n    }\r\n\r\n    get sidebar () {\r\n      return AppModule.sidebar\r\n    }\r\n\r\n  @Watch('$route')\r\n    private onRouteChange() {\r\n      if (this.device === DeviceType.Mobile && this.sidebar.opened) {\r\n        AppModule.CloseSideBar(false)\r\n      }\r\n    }\r\n\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.resizeHandler)\r\n  }\r\n\r\n  mounted() {\r\n    const isMobile = this.isMobile()\r\n    if (isMobile) {\r\n      AppModule.ToggleDevice(DeviceType.Mobile)\r\n      AppModule.CloseSideBar(true)\r\n    }\r\n  }\r\n\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.resizeHandler)\r\n  }\r\n\r\n  private isMobile() {\r\n    const rect = document.body.getBoundingClientRect()\r\n    return rect.width - 1 < WIDTH\r\n  }\r\n\r\n  private resizeHandler() {\r\n    if (!document.hidden) {\r\n      const isMobile = this.isMobile()\r\n      AppModule.ToggleDevice(isMobile ? DeviceType.Mobile : DeviceType.Desktop)\r\n      if (isMobile) {\r\n        AppModule.CloseSideBar(true)\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AAA2D,SAAAE,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAE3D,IAAMe,KAAK,GAAG,GAAG,CAAC,CAAC;AAKnB,IAAAC,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAd,OAAA,QAAAY,SAAA;IAAA,OAAAjB,UAAA,OAAAiB,SAAA,EAAAG,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAhB,OAAA,EAAAY,SAAA,EAAAC,IAAA;EAAA,WAAAI,aAAA,CAAAjB,OAAA,EAAAY,SAAA;IAAAM,GAAA;IAAAC,GAAA,EACI,SAAAA,IAAA,EAAU;MACR,OAAOC,cAAS,CAACC,MAAM;IACzB;EAAC;IAAAH,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAW;MACT,OAAOC,cAAS,CAACE,OAAO;IAC1B;EAAC;IAAAJ,GAAA;IAAAK,KAAA,EAGO,SAAAC,aAAaA,CAAA;MACnB,IAAI,IAAI,CAACH,MAAM,KAAKI,eAAU,CAACC,MAAM,IAAI,IAAI,CAACJ,OAAO,CAACK,MAAM,EAAE;QAC5DP,cAAS,CAACQ,YAAY,CAAC,KAAK,CAAC;;IAEjC;EAAC;IAAAV,GAAA;IAAAK,KAAA,EAEH,SAAAM,WAAWA,CAAA;MACTC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,aAAa,CAAC;IACvD;EAAC;IAAAd,GAAA;IAAAK,KAAA,EAED,SAAAU,OAAOA,CAAA;MACL,IAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;MAChC,IAAIA,QAAQ,EAAE;QACZd,cAAS,CAACe,YAAY,CAACV,eAAU,CAACC,MAAM,CAAC;QACzCN,cAAS,CAACQ,YAAY,CAAC,IAAI,CAAC;;IAEhC;EAAC;IAAAV,GAAA;IAAAK,KAAA,EAED,SAAAa,aAAaA,CAAA;MACXN,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACL,aAAa,CAAC;IAC1D;EAAC;IAAAd,GAAA;IAAAK,KAAA,EAEO,SAAAW,QAAQA,CAAA;MACd,IAAMI,IAAI,GAAGC,QAAQ,CAACC,IAAI,CAACC,qBAAqB,EAAE;MAClD,OAAOH,IAAI,CAACI,KAAK,GAAG,CAAC,GAAG/B,KAAK;IAC/B;EAAC;IAAAO,GAAA;IAAAK,KAAA,EAEO,SAAAS,aAAaA,CAAA;MACnB,IAAI,CAACO,QAAQ,CAACI,MAAM,EAAE;QACpB,IAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;QAChCd,cAAS,CAACe,YAAY,CAACD,QAAQ,GAAGT,eAAU,CAACC,MAAM,GAAGD,eAAU,CAACmB,OAAO,CAAC;QACzE,IAAIV,QAAQ,EAAE;UACZd,cAAS,CAACQ,YAAY,CAAC,IAAI,CAAC;;;IAGlC;EAAC;AAAA,EA7C0BiB,yBAAG,CA8C/B;AApCG,IAAAC,iBAAA,GADD,IAAAC,2BAAK,EAAC,QAAQ,CAAC,C,6CAKb;AAdLnC,SAAA,OAAAkC,iBAAA,GAHC,IAAAE,+BAAS,EAAC;EACP,MAAM,EAAE;CACX,CAAC,C,YA+CD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAlD,OAAA,G", "ignoreList": []}]}