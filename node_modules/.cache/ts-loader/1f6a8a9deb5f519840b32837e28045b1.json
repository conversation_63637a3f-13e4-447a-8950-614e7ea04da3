{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue", "mtime": 1655712116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/defineProperty.js\"));\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/asyncToGenerator.js\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _index = _interopRequireDefault(require(\"@/components/Empty/index.vue\"));\nvar _order = require(\"@/api/order\");\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    var _this;\n    (0, _classCallCheck2.default)(this, default_1);\n    _this = _callSuper(this, default_1, arguments);\n    _this.orderId = ''; //订单号\n    _this.dialogOrderStatus = 0; //弹窗所需订单状态，用于详情展示字段\n    _this.activeIndex = 0;\n    _this.dialogVisible = false; //详情弹窗\n    _this.cancelDialogVisible = false; //取消，拒单弹窗\n    _this.cancelDialogTitle = ''; //取消，拒绝弹窗标题\n    _this.cancelReason = '';\n    _this.remark = ''; //自定义原因\n    _this.diaForm = [];\n    _this.row = {};\n    _this.isAutoNext = true;\n    _this.isSearch = false;\n    _this.counts = 0;\n    _this.page = 1;\n    _this.pageSize = 10;\n    _this.status = 2;\n    _this.orderData = [];\n    _this.isTableOperateBtn = true;\n    _this.cancelOrderReasonList = [{\n      value: 1,\n      label: '订单量较多，暂时无法接单'\n    }, {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单'\n    }, {\n      value: 3,\n      label: '餐厅已打烊，暂时无法接单'\n    }, {\n      value: 0,\n      label: '自定义原因'\n    }];\n    _this.cancelrReasonList = [{\n      value: 1,\n      label: '订单量较多，暂时无法接单'\n    }, {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单'\n    }, {\n      value: 3,\n      label: '骑手不足无法配送'\n    }, {\n      value: 4,\n      label: '客户电话取消'\n    }, {\n      value: 0,\n      label: '自定义原因'\n    }];\n    _this.orderList = [{\n      label: '全部订单',\n      value: 0\n    }, {\n      label: '待付款',\n      value: 1\n    }, {\n      label: '待接单',\n      value: 2\n    }, {\n      label: '待派送',\n      value: 3\n    }, {\n      label: '派送中',\n      value: 4\n    }, {\n      label: '已完成',\n      value: 5\n    }, {\n      label: '已取消',\n      value: 6\n    }];\n    return _this;\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"tabList\",\n    get: function get() {\n      return [{\n        label: '待接单',\n        value: 2,\n        num: this.orderStatics.toBeConfirmed\n      }, {\n        label: '待派送',\n        value: 3,\n        num: this.orderStatics.confirmed\n      }];\n    }\n  }, {\n    key: \"created\",\n    value: function created() {\n      this.getOrderListData(this.status);\n    }\n    // // 获取订单数据\n  }, {\n    key: \"getOrderListData\",\n    value: function () {\n      var _getOrderListData = (0, _asyncToGenerator2.default)(/*#__PURE__*/regeneratorRuntime.mark(function _callee(status) {\n        var params, data, row;\n        return regeneratorRuntime.wrap(function (_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              params = {\n                page: this.page,\n                pageSize: this.pageSize,\n                status: status\n              };\n              _context.next = 1;\n              return (0, _order.getOrderDetailPage)(params);\n            case 1:\n              data = _context.sent;\n              this.orderData = data.data.data.records;\n              this.counts = data.data.data.total;\n              this.$emit('getOrderListBy3Status');\n              if (!(this.dialogOrderStatus === 2 && this.status === 2 && this.isAutoNext && !this.isTableOperateBtn && data.data.records.length > 1)) {\n                _context.next = 2;\n                break;\n              }\n              row = data.data.records[0];\n              this.goDetail(row.id, row.status, row, row);\n              _context.next = 3;\n              break;\n            case 2:\n              return _context.abrupt(\"return\", null);\n            case 3:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function getOrderListData(_x) {\n        return _getOrderListData.apply(this, arguments);\n      }\n      return getOrderListData;\n    }() //接单\n  }, {\n    key: \"orderAccept\",\n    value: function orderAccept(row, event) {\n      var _this2 = this;\n      event.stopPropagation();\n      this.orderId = row.id;\n      this.dialogOrderStatus = row.status;\n      (0, _order.orderAccept)({\n        id: this.orderId\n      }).then(function (res) {\n        if (res.data.code === 1) {\n          _this2.$message.success('操作成功');\n          _this2.orderId = '';\n          // this.dialogOrderStatus = 0\n          _this2.dialogVisible = false;\n          _this2.getOrderListData(_this2.status);\n        } else {\n          _this2.$message.error(res.data.msg);\n        }\n      }).catch(function (err) {\n        _this2.$message.error('请求出错了：' + err.message);\n      });\n    }\n    //打开取消订单弹窗\n  }, {\n    key: \"cancelOrder\",\n    value: function cancelOrder(row, event) {\n      event.stopPropagation();\n      this.cancelDialogVisible = true;\n      this.orderId = row.id;\n      this.dialogOrderStatus = row.status;\n      this.cancelDialogTitle = '取消';\n      this.dialogVisible = false;\n      this.cancelReason = '';\n    }\n    //打开拒单弹窗\n  }, {\n    key: \"orderReject\",\n    value: function orderReject(row, event) {\n      event.stopPropagation();\n      this.cancelDialogVisible = true;\n      this.orderId = row.id;\n      this.dialogOrderStatus = row.status;\n      this.cancelDialogTitle = '拒绝';\n      this.dialogVisible = false;\n      this.cancelReason = '';\n    }\n    //确认取消或拒绝订单并填写原因\n  }, {\n    key: \"confirmCancel\",\n    value: function confirmCancel(type) {\n      var _this3 = this;\n      if (!this.cancelReason) {\n        return this.$message.error(\"\\u8BF7\\u9009\\u62E9\".concat(this.cancelDialogTitle, \"\\u539F\\u56E0\"));\n      } else if (this.cancelReason === '自定义原因' && !this.remark) {\n        return this.$message.error(\"\\u8BF7\\u8F93\\u5165\".concat(this.cancelDialogTitle, \"\\u539F\\u56E0\"));\n      }\n      ;\n      (this.cancelDialogTitle === '取消' ? _order.orderCancel : _order.orderReject)((0, _defineProperty2.default)({\n        id: this.orderId\n      }, this.cancelDialogTitle === '取消' ? 'cancelReason' : 'rejectionReason', this.cancelReason === '自定义原因' ? this.remark : this.cancelReason)).then(function (res) {\n        if (res.data.code === 1) {\n          _this3.$message.success('操作成功');\n          _this3.cancelDialogVisible = false;\n          _this3.orderId = '';\n          // this.dialogOrderStatus = 0\n          _this3.getOrderListData(_this3.status);\n        } else {\n          _this3.$message.error(res.data.msg);\n        }\n      }).catch(function (err) {\n        _this3.$message.error('请求出错了：' + err.message);\n      });\n    }\n    // 派送，完成\n  }, {\n    key: \"cancelOrDeliveryOrComplete\",\n    value: function cancelOrDeliveryOrComplete(status, id, event) {\n      var _this4 = this;\n      event.stopPropagation();\n      var params = {\n        status: status,\n        id: id\n      };\n      (status === 3 ? _order.deliveryOrder : _order.completeOrder)(params).then(function (res) {\n        if (res.data.code === 1) {\n          _this4.$message.success('操作成功');\n          _this4.orderId = '';\n          // this.dialogOrderStatus = 0\n          _this4.dialogVisible = false;\n          _this4.getOrderListData(_this4.status);\n        } else {\n          _this4.$message.error(res.data.msg);\n        }\n      }).catch(function (err) {\n        _this4.$message.error('请求出错了：' + err.message);\n      });\n    }\n    // 查看详情\n  }, {\n    key: \"goDetail\",\n    value: function () {\n      var _goDetail = (0, _asyncToGenerator2.default)(/*#__PURE__*/regeneratorRuntime.mark(function _callee2(id, status, row, event) {\n        var _yield$queryOrderDeta, data;\n        return regeneratorRuntime.wrap(function (_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              event.stopPropagation();\n              // console.log(111, index, row)\n              this.diaForm = [];\n              this.dialogVisible = true;\n              this.dialogOrderStatus = status;\n              _context2.next = 1;\n              return (0, _order.queryOrderDetailById)({\n                orderId: id\n              });\n            case 1:\n              _yield$queryOrderDeta = _context2.sent;\n              data = _yield$queryOrderDeta.data;\n              this.diaForm = data.data;\n              this.row = row;\n            case 2:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function goDetail(_x2, _x3, _x4, _x5) {\n        return _goDetail.apply(this, arguments);\n      }\n      return goDetail;\n    }() // 关闭弹层\n  }, {\n    key: \"handleClose\",\n    value: function handleClose() {\n      this.dialogVisible = false;\n    }\n    // tab切换\n  }, {\n    key: \"handleClass\",\n    value: function handleClass(index) {\n      this.activeIndex = index;\n      if (index === 0) {\n        this.status = 2;\n        this.getOrderListData(2);\n      } else {\n        this.status = 3;\n        this.getOrderListData(3);\n      }\n    }\n    // 触发table某一行\n  }, {\n    key: \"handleTable\",\n    value: function handleTable(row, column, event) {\n      event.stopPropagation();\n      this.goDetail(row.id, row.status, row, event);\n    }\n    // 分页\n  }, {\n    key: \"handleSizeChange\",\n    value: function handleSizeChange(val) {\n      this.pageSize = val;\n      this.getOrderListData(this.status);\n    }\n  }, {\n    key: \"handleCurrentChange\",\n    value: function handleCurrentChange(val) {\n      this.page = val;\n      this.getOrderListData(this.status);\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: ''\n})], default_1.prototype, \"orderStatics\", void 0);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'Orderview',\n  components: {\n    Empty: _index.default\n  }\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_index", "_interopRequireDefault", "_order", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_this", "_classCallCheck2", "orderId", "dialogOrderStatus", "activeIndex", "dialogVisible", "cancelDialogVisible", "cancelDialogTitle", "cancelReason", "remark", "diaForm", "row", "isAutoNext", "isSearch", "counts", "page", "pageSize", "status", "orderData", "isTableOperateBtn", "cancelOrderReasonList", "value", "label", "cancelrReasonList", "orderList", "_inherits2", "_createClass2", "key", "get", "num", "orderStatics", "toBeConfirmed", "confirmed", "created", "getOrderListData", "_getOrderListData", "_asyncToGenerator2", "regeneratorRuntime", "mark", "_callee", "params", "data", "wrap", "_context", "prev", "next", "getOrderDetailPage", "sent", "records", "total", "$emit", "length", "goDetail", "id", "abrupt", "stop", "_x", "arguments", "orderAccept", "event", "_this2", "stopPropagation", "then", "res", "code", "$message", "success", "error", "msg", "catch", "err", "message", "cancelOrder", "orderReject", "confirmCancel", "type", "_this3", "concat", "orderCancel", "_defineProperty2", "cancelOrDeliveryOrComplete", "_this4", "deliveryOrder", "completeOrder", "_goDetail", "_callee2", "_yield$queryOrderDeta", "_context2", "queryOrderDetailById", "_x2", "_x3", "_x4", "_x5", "handleClose", "handleClass", "index", "handleTable", "column", "handleSizeChange", "val", "handleCurrentChange", "<PERSON><PERSON>", "__decorate", "Prop", "Component", "name", "components", "Empty", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue?vue&type=script&lang=ts"], "sourcesContent": ["\nimport { Component, Vue, Prop } from 'vue-property-decorator'\nimport Empty from '@/components/Empty/index.vue'\nimport {\n  getOrderDetailPage,\n  queryOrderDetailById,\n  completeOrder,\n  deliveryOrder,\n  orderCancel,\n  orderReject,\n  orderAccept,\n  getOrderListBy,\n} from '@/api/order'\n@Component({\n  name: 'Orderview',\n  components: {\n    Empty,\n  },\n})\nexport default class extends Vue {\n  @Prop({ default: '' }) orderStatics!: any\n\n  private orderId = '' //订单号\n  private dialogOrderStatus = 0 //弹窗所需订单状态，用于详情展示字段\n  private activeIndex = 0\n\n  private dialogVisible = false //详情弹窗\n  private cancelDialogVisible = false //取消，拒单弹窗\n  private cancelDialogTitle = '' //取消，拒绝弹窗标题\n  private cancelReason = ''\n  private remark = '' //自定义原因\n  private diaForm = []\n  private row = {}\n  private isAutoNext = true\n  private isSearch: boolean = false\n  private counts = 0\n  private page: number = 1\n  private pageSize: number = 10\n  private status = 2\n  private orderData = []\n  private isTableOperateBtn = true\n  private cancelOrderReasonList = [\n    {\n      value: 1,\n      label: '订单量较多，暂时无法接单',\n    },\n    {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单',\n    },\n    {\n      value: 3,\n      label: '餐厅已打烊，暂时无法接单',\n    },\n    {\n      value: 0,\n      label: '自定义原因',\n    },\n  ]\n\n  private cancelrReasonList = [\n    {\n      value: 1,\n      label: '订单量较多，暂时无法接单',\n    },\n    {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单',\n    },\n    {\n      value: 3,\n      label: '骑手不足无法配送',\n    },\n    {\n      value: 4,\n      label: '客户电话取消',\n    },\n    {\n      value: 0,\n      label: '自定义原因',\n    },\n  ]\n  private orderList = [\n    {\n      label: '全部订单',\n      value: 0,\n    },\n    {\n      label: '待付款',\n      value: 1,\n    },\n    {\n      label: '待接单',\n      value: 2,\n    },\n    {\n      label: '待派送',\n      value: 3,\n    },\n    {\n      label: '派送中',\n      value: 4,\n    },\n    {\n      label: '已完成',\n      value: 5,\n    },\n    {\n      label: '已取消',\n      value: 6,\n    },\n  ]\n  get tabList() {\n    return [\n      {\n        label: '待接单',\n        value: 2,\n        num: this.orderStatics.toBeConfirmed,\n      },\n      {\n        label: '待派送',\n        value: 3,\n        num: this.orderStatics.confirmed,\n      },\n    ]\n  }\n  created() {\n    this.getOrderListData(this.status)\n  }\n  // // 获取订单数据\n  async getOrderListData(status) {\n    const params = {\n      page: this.page,\n      pageSize: this.pageSize,\n      status: status,\n    }\n    const data = await getOrderDetailPage(params)\n    this.orderData = data.data.data.records\n    this.counts = data.data.data.total\n    this.$emit('getOrderListBy3Status')\n    if (\n      this.dialogOrderStatus === 2 &&\n      this.status === 2 &&\n      this.isAutoNext &&\n      !this.isTableOperateBtn &&\n      data.data.records.length > 1\n    ) {\n      const row = data.data.records[0]\n      this.goDetail(row.id, row.status, row, row)\n    } else {\n      return null\n    }\n  }\n\n  //接单\n  orderAccept(row: any, event) {\n    event.stopPropagation()\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    orderAccept({ id: this.orderId })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.dialogVisible = false\n          this.getOrderListData(this.status)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n  //打开取消订单弹窗\n  cancelOrder(row: any, event) {\n    event.stopPropagation()\n    this.cancelDialogVisible = true\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    this.cancelDialogTitle = '取消'\n    this.dialogVisible = false\n    this.cancelReason = ''\n  }\n  //打开拒单弹窗\n  orderReject(row: any, event) {\n    event.stopPropagation()\n    this.cancelDialogVisible = true\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    this.cancelDialogTitle = '拒绝'\n    this.dialogVisible = false\n    this.cancelReason = ''\n  }\n  //确认取消或拒绝订单并填写原因\n  confirmCancel(type) {\n    if (!this.cancelReason) {\n      return this.$message.error(`请选择${this.cancelDialogTitle}原因`)\n    } else if (this.cancelReason === '自定义原因' && !this.remark) {\n      return this.$message.error(`请输入${this.cancelDialogTitle}原因`)\n    }\n\n    ;(this.cancelDialogTitle === '取消' ? orderCancel : orderReject)({\n      id: this.orderId,\n      // eslint-disable-next-line standard/computed-property-even-spacing\n      [this.cancelDialogTitle === '取消' ? 'cancelReason' : 'rejectionReason']:\n        this.cancelReason === '自定义原因' ? this.remark : this.cancelReason,\n    })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.cancelDialogVisible = false\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.getOrderListData(this.status)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  // 派送，完成\n  cancelOrDeliveryOrComplete(status: number, id: string, event) {\n    event.stopPropagation()\n    const params = {\n      status,\n      id,\n    }\n    ;(status === 3 ? deliveryOrder : completeOrder)(params)\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.dialogVisible = false\n          this.getOrderListData(this.status)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n  // 查看详情\n  async goDetail(id: any, status: number, row: any, event) {\n    event.stopPropagation()\n    // console.log(111, index, row)\n    this.diaForm = []\n    this.dialogVisible = true\n    this.dialogOrderStatus = status\n    const { data } = await queryOrderDetailById({ orderId: id })\n    this.diaForm = data.data\n    this.row = row\n  }\n  // 关闭弹层\n  handleClose() {\n    this.dialogVisible = false\n  }\n  // tab切换\n  handleClass(index) {\n    this.activeIndex = index\n    if (index === 0) {\n      this.status = 2\n      this.getOrderListData(2)\n    } else {\n      this.status = 3\n      this.getOrderListData(3)\n    }\n  }\n  // 触发table某一行\n  handleTable(row, column, event) {\n    event.stopPropagation()\n    this.goDetail(row.id, row.status, row, event)\n  }\n  // 分页\n  private handleSizeChange(val: any) {\n    this.pageSize = val\n    this.getOrderListData(this.status)\n  }\n\n  private handleCurrentChange(val: any) {\n    this.page = val\n    this.getOrderListData(this.status)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AASoB,SAAAI,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAOpB,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAd,OAAA,QAAAW,SAAA;;IAGUE,KAAA,CAAAE,OAAO,GAAG,EAAE,EAAC;IACbF,KAAA,CAAAG,iBAAiB,GAAG,CAAC,EAAC;IACtBH,KAAA,CAAAI,WAAW,GAAG,CAAC;IAEfJ,KAAA,CAAAK,aAAa,GAAG,KAAK,EAAC;IACtBL,KAAA,CAAAM,mBAAmB,GAAG,KAAK,EAAC;IAC5BN,KAAA,CAAAO,iBAAiB,GAAG,EAAE,EAAC;IACvBP,KAAA,CAAAQ,YAAY,GAAG,EAAE;IACjBR,KAAA,CAAAS,MAAM,GAAG,EAAE,EAAC;IACZT,KAAA,CAAAU,OAAO,GAAG,EAAE;IACZV,KAAA,CAAAW,GAAG,GAAG,EAAE;IACRX,KAAA,CAAAY,UAAU,GAAG,IAAI;IACjBZ,KAAA,CAAAa,QAAQ,GAAY,KAAK;IACzBb,KAAA,CAAAc,MAAM,GAAG,CAAC;IACVd,KAAA,CAAAe,IAAI,GAAW,CAAC;IAChBf,KAAA,CAAAgB,QAAQ,GAAW,EAAE;IACrBhB,KAAA,CAAAiB,MAAM,GAAG,CAAC;IACVjB,KAAA,CAAAkB,SAAS,GAAG,EAAE;IACdlB,KAAA,CAAAmB,iBAAiB,GAAG,IAAI;IACxBnB,KAAA,CAAAoB,qBAAqB,GAAG,CAC9B;MACEC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,CACF;IAEOtB,KAAA,CAAAuB,iBAAiB,GAAG,CAC1B;MACEF,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR,CACF;IACOtB,KAAA,CAAAwB,SAAS,GAAG,CAClB;MACEF,KAAK,EAAE,MAAM;MACbD,KAAK,EAAE;KACR,EACD;MACEC,KAAK,EAAE,KAAK;MACZD,KAAK,EAAE;KACR,EACD;MACEC,KAAK,EAAE,KAAK;MACZD,KAAK,EAAE;KACR,EACD;MACEC,KAAK,EAAE,KAAK;MACZD,KAAK,EAAE;KACR,EACD;MACEC,KAAK,EAAE,KAAK;MACZD,KAAK,EAAE;KACR,EACD;MACEC,KAAK,EAAE,KAAK;MACZD,KAAK,EAAE;KACR,EACD;MACEC,KAAK,EAAE,KAAK;MACZD,KAAK,EAAE;KACR,CACF;IAAA,OAAArB,KAAA;EAkLH;EAAC,IAAAyB,UAAA,CAAAtC,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAA2B,aAAA,CAAAvC,OAAA,EAAAW,SAAA;IAAA6B,GAAA;IAAAC,GAAA,EAjLC,SAAAA,IAAA,EAAW;MACT,OAAO,CACL;QACEN,KAAK,EAAE,KAAK;QACZD,KAAK,EAAE,CAAC;QACRQ,GAAG,EAAE,IAAI,CAACC,YAAY,CAACC;OACxB,EACD;QACET,KAAK,EAAE,KAAK;QACZD,KAAK,EAAE,CAAC;QACRQ,GAAG,EAAE,IAAI,CAACC,YAAY,CAACE;OACxB,CACF;IACH;EAAC;IAAAL,GAAA;IAAAN,KAAA,EACD,SAAAY,OAAOA,CAAA;MACL,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACjB,MAAM,CAAC;IACpC;IACA;EAAA;IAAAU,GAAA;IAAAN,KAAA;MAAA,IAAAc,iBAAA,OAAAC,kBAAA,CAAAjD,OAAA,eAAAkD,kBAAA,CAAAC,IAAA,CACA,SAAAC,QAAuBtB,MAAM;QAAA,IAAAuB,MAAA,EAAAC,IAAA,EAAA9B,GAAA;QAAA,OAAA0B,kBAAA,CAAAK,IAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACrBL,MAAM,GAAG;gBACbzB,IAAI,EAAE,IAAI,CAACA,IAAI;gBACfC,QAAQ,EAAE,IAAI,CAACA,QAAQ;gBACvBC,MAAM,EAAEA;eACT;cAAA0B,QAAA,CAAAE,IAAA;cAAA,OACkB,IAAAC,yBAAkB,EAACN,MAAM,CAAC;YAAA;cAAvCC,IAAI,GAAAE,QAAA,CAAAI,IAAA;cACV,IAAI,CAAC7B,SAAS,GAAGuB,IAAI,CAACA,IAAI,CAACA,IAAI,CAACO,OAAO;cACvC,IAAI,CAAClC,MAAM,GAAG2B,IAAI,CAACA,IAAI,CAACA,IAAI,CAACQ,KAAK;cAClC,IAAI,CAACC,KAAK,CAAC,uBAAuB,CAAC;cAAA,MAEjC,IAAI,CAAC/C,iBAAiB,KAAK,CAAC,IAC5B,IAAI,CAACc,MAAM,KAAK,CAAC,IACjB,IAAI,CAACL,UAAU,IACf,CAAC,IAAI,CAACO,iBAAiB,IACvBsB,IAAI,CAACA,IAAI,CAACO,OAAO,CAACG,MAAM,GAAG,CAAC;gBAAAR,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAEtBlC,GAAG,GAAG8B,IAAI,CAACA,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC;cAChC,IAAI,CAACI,QAAQ,CAACzC,GAAG,CAAC0C,EAAE,EAAE1C,GAAG,CAACM,MAAM,EAAEN,GAAG,EAAEA,GAAG,CAAC;cAAAgC,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,OAAAF,QAAA,CAAAW,MAAA,WAEpC,IAAI;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA,CAEd;MAAA,SAtBKL,gBAAgBA,CAAAsB,EAAA;QAAA,OAAArB,iBAAA,CAAA1C,KAAA,OAAAgE,SAAA;MAAA;MAAA,OAAhBvB,gBAAgB;IAAA,IAwBtB;EAAA;IAAAP,GAAA;IAAAN,KAAA,EACA,SAAAqC,WAAWA,CAAC/C,GAAQ,EAAEgD,KAAK;MAAA,IAAAC,MAAA;MACzBD,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI,CAAC3D,OAAO,GAAGS,GAAG,CAAC0C,EAAE;MACrB,IAAI,CAAClD,iBAAiB,GAAGQ,GAAG,CAACM,MAAM;MACnC,IAAAyC,kBAAW,EAAC;QAAEL,EAAE,EAAE,IAAI,CAACnD;MAAO,CAAE,CAAC,CAC9B4D,IAAI,CAAC,UAACC,GAAG,EAAI;QACZ,IAAIA,GAAG,CAACtB,IAAI,CAACuB,IAAI,KAAK,CAAC,EAAE;UACvBJ,MAAI,CAACK,QAAQ,CAACC,OAAO,CAAC,MAAM,CAAC;UAC7BN,MAAI,CAAC1D,OAAO,GAAG,EAAE;UACjB;UACA0D,MAAI,CAACvD,aAAa,GAAG,KAAK;UAC1BuD,MAAI,CAAC1B,gBAAgB,CAAC0B,MAAI,CAAC3C,MAAM,CAAC;SACnC,MAAM;UACL2C,MAAI,CAACK,QAAQ,CAACE,KAAK,CAACJ,GAAG,CAACtB,IAAI,CAAC2B,GAAG,CAAC;;MAErC,CAAC,CAAC,CACDC,KAAK,CAAC,UAACC,GAAG,EAAI;QACbV,MAAI,CAACK,QAAQ,CAACE,KAAK,CAAC,QAAQ,GAAGG,GAAG,CAACC,OAAO,CAAC;MAC7C,CAAC,CAAC;IACN;IACA;EAAA;IAAA5C,GAAA;IAAAN,KAAA,EACA,SAAAmD,WAAWA,CAAC7D,GAAQ,EAAEgD,KAAK;MACzBA,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI,CAACvD,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACJ,OAAO,GAAGS,GAAG,CAAC0C,EAAE;MACrB,IAAI,CAAClD,iBAAiB,GAAGQ,GAAG,CAACM,MAAM;MACnC,IAAI,CAACV,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACF,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACG,YAAY,GAAG,EAAE;IACxB;IACA;EAAA;IAAAmB,GAAA;IAAAN,KAAA,EACA,SAAAoD,WAAWA,CAAC9D,GAAQ,EAAEgD,KAAK;MACzBA,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI,CAACvD,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACJ,OAAO,GAAGS,GAAG,CAAC0C,EAAE;MACrB,IAAI,CAAClD,iBAAiB,GAAGQ,GAAG,CAACM,MAAM;MACnC,IAAI,CAACV,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACF,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACG,YAAY,GAAG,EAAE;IACxB;IACA;EAAA;IAAAmB,GAAA;IAAAN,KAAA,EACA,SAAAqD,aAAaA,CAACC,IAAI;MAAA,IAAAC,MAAA;MAChB,IAAI,CAAC,IAAI,CAACpE,YAAY,EAAE;QACtB,OAAO,IAAI,CAACyD,QAAQ,CAACE,KAAK,sBAAAU,MAAA,CAAO,IAAI,CAACtE,iBAAiB,iBAAI,CAAC;OAC7D,MAAM,IAAI,IAAI,CAACC,YAAY,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;QACxD,OAAO,IAAI,CAACwD,QAAQ,CAACE,KAAK,sBAAAU,MAAA,CAAO,IAAI,CAACtE,iBAAiB,iBAAI,CAAC;;MAG9D;MAAC,CAAC,IAAI,CAACA,iBAAiB,KAAK,IAAI,GAAGuE,kBAAW,GAAGL,kBAAW,MAAAM,gBAAA,CAAA5F,OAAA;QAC3DkE,EAAE,EAAE,IAAI,CAACnD;MAAO,GAEf,IAAI,CAACK,iBAAiB,KAAK,IAAI,GAAG,cAAc,GAAG,iBAAiB,EACnE,IAAI,CAACC,YAAY,KAAK,OAAO,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,YAAY,CAClE,CAAC,CACCsD,IAAI,CAAC,UAACC,GAAG,EAAI;QACZ,IAAIA,GAAG,CAACtB,IAAI,CAACuB,IAAI,KAAK,CAAC,EAAE;UACvBY,MAAI,CAACX,QAAQ,CAACC,OAAO,CAAC,MAAM,CAAC;UAC7BU,MAAI,CAACtE,mBAAmB,GAAG,KAAK;UAChCsE,MAAI,CAAC1E,OAAO,GAAG,EAAE;UACjB;UACA0E,MAAI,CAAC1C,gBAAgB,CAAC0C,MAAI,CAAC3D,MAAM,CAAC;SACnC,MAAM;UACL2D,MAAI,CAACX,QAAQ,CAACE,KAAK,CAACJ,GAAG,CAACtB,IAAI,CAAC2B,GAAG,CAAC;;MAErC,CAAC,CAAC,CACDC,KAAK,CAAC,UAACC,GAAG,EAAI;QACbM,MAAI,CAACX,QAAQ,CAACE,KAAK,CAAC,QAAQ,GAAGG,GAAG,CAACC,OAAO,CAAC;MAC7C,CAAC,CAAC;IACN;IAEA;EAAA;IAAA5C,GAAA;IAAAN,KAAA,EACA,SAAA2D,0BAA0BA,CAAC/D,MAAc,EAAEoC,EAAU,EAAEM,KAAK;MAAA,IAAAsB,MAAA;MAC1DtB,KAAK,CAACE,eAAe,EAAE;MACvB,IAAMrB,MAAM,GAAG;QACbvB,MAAM,EAANA,MAAM;QACNoC,EAAE,EAAFA;OACD;MACA,CAACpC,MAAM,KAAK,CAAC,GAAGiE,oBAAa,GAAGC,oBAAa,EAAE3C,MAAM,CAAC,CACpDsB,IAAI,CAAC,UAACC,GAAG,EAAI;QACZ,IAAIA,GAAG,CAACtB,IAAI,CAACuB,IAAI,KAAK,CAAC,EAAE;UACvBiB,MAAI,CAAChB,QAAQ,CAACC,OAAO,CAAC,MAAM,CAAC;UAC7Be,MAAI,CAAC/E,OAAO,GAAG,EAAE;UACjB;UACA+E,MAAI,CAAC5E,aAAa,GAAG,KAAK;UAC1B4E,MAAI,CAAC/C,gBAAgB,CAAC+C,MAAI,CAAChE,MAAM,CAAC;SACnC,MAAM;UACLgE,MAAI,CAAChB,QAAQ,CAACE,KAAK,CAACJ,GAAG,CAACtB,IAAI,CAAC2B,GAAG,CAAC;;MAErC,CAAC,CAAC,CACDC,KAAK,CAAC,UAACC,GAAG,EAAI;QACbW,MAAI,CAAChB,QAAQ,CAACE,KAAK,CAAC,QAAQ,GAAGG,GAAG,CAACC,OAAO,CAAC;MAC7C,CAAC,CAAC;IACN;IACA;EAAA;IAAA5C,GAAA;IAAAN,KAAA;MAAA,IAAA+D,SAAA,OAAAhD,kBAAA,CAAAjD,OAAA,eAAAkD,kBAAA,CAAAC,IAAA,CACA,SAAA+C,SAAehC,EAAO,EAAEpC,MAAc,EAAEN,GAAQ,EAAEgD,KAAK;QAAA,IAAA2B,qBAAA,EAAA7C,IAAA;QAAA,OAAAJ,kBAAA,CAAAK,IAAA,WAAA6C,SAAA;UAAA,kBAAAA,SAAA,CAAA3C,IAAA,GAAA2C,SAAA,CAAA1C,IAAA;YAAA;cACrDc,KAAK,CAACE,eAAe,EAAE;cACvB;cACA,IAAI,CAACnD,OAAO,GAAG,EAAE;cACjB,IAAI,CAACL,aAAa,GAAG,IAAI;cACzB,IAAI,CAACF,iBAAiB,GAAGc,MAAM;cAAAsE,SAAA,CAAA1C,IAAA;cAAA,OACR,IAAA2C,2BAAoB,EAAC;gBAAEtF,OAAO,EAAEmD;cAAE,CAAE,CAAC;YAAA;cAAAiC,qBAAA,GAAAC,SAAA,CAAAxC,IAAA;cAApDN,IAAI,GAAA6C,qBAAA,CAAJ7C,IAAI;cACZ,IAAI,CAAC/B,OAAO,GAAG+B,IAAI,CAACA,IAAI;cACxB,IAAI,CAAC9B,GAAG,GAAGA,GAAG;YAAA;YAAA;cAAA,OAAA4E,SAAA,CAAAhC,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA,CACf;MAAA,SATKjC,QAAQA,CAAAqC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAR,SAAA,CAAA3F,KAAA,OAAAgE,SAAA;MAAA;MAAA,OAARL,QAAQ;IAAA,IAUd;EAAA;IAAAzB,GAAA;IAAAN,KAAA,EACA,SAAAwE,WAAWA,CAAA;MACT,IAAI,CAACxF,aAAa,GAAG,KAAK;IAC5B;IACA;EAAA;IAAAsB,GAAA;IAAAN,KAAA,EACA,SAAAyE,WAAWA,CAACC,KAAK;MACf,IAAI,CAAC3F,WAAW,GAAG2F,KAAK;MACxB,IAAIA,KAAK,KAAK,CAAC,EAAE;QACf,IAAI,CAAC9E,MAAM,GAAG,CAAC;QACf,IAAI,CAACiB,gBAAgB,CAAC,CAAC,CAAC;OACzB,MAAM;QACL,IAAI,CAACjB,MAAM,GAAG,CAAC;QACf,IAAI,CAACiB,gBAAgB,CAAC,CAAC,CAAC;;IAE5B;IACA;EAAA;IAAAP,GAAA;IAAAN,KAAA,EACA,SAAA2E,WAAWA,CAACrF,GAAG,EAAEsF,MAAM,EAAEtC,KAAK;MAC5BA,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI,CAACT,QAAQ,CAACzC,GAAG,CAAC0C,EAAE,EAAE1C,GAAG,CAACM,MAAM,EAAEN,GAAG,EAAEgD,KAAK,CAAC;IAC/C;IACA;EAAA;IAAAhC,GAAA;IAAAN,KAAA,EACQ,SAAA6E,gBAAgBA,CAACC,GAAQ;MAC/B,IAAI,CAACnF,QAAQ,GAAGmF,GAAG;MACnB,IAAI,CAACjE,gBAAgB,CAAC,IAAI,CAACjB,MAAM,CAAC;IACpC;EAAC;IAAAU,GAAA;IAAAN,KAAA,EAEO,SAAA+E,mBAAmBA,CAACD,GAAQ;MAClC,IAAI,CAACpF,IAAI,GAAGoF,GAAG;MACf,IAAI,CAACjE,gBAAgB,CAAC,IAAI,CAACjB,MAAM,CAAC;IACpC;EAAC;AAAA,EA7Q0BoF,yBAAG,CA8Q/B;AA7QwB,IAAAC,iBAAA,GAAtB,IAAAC,0BAAI,EAAC;EAAEpH,OAAO,EAAE;AAAE,CAAE,CAAC,C,8CAAmB;AAD3CW,SAAA,OAAAwG,iBAAA,GANC,IAAAE,+BAAS,EAAC;EACTC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVC,KAAK,EAALA;;CAEH,CAAC,C,YA+QD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA1H,OAAA,G", "ignoreList": []}]}