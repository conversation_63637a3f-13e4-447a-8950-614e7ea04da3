{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/permission.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/permission.ts", "mtime": 1691639154000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/asyncToGenerator.js\"));\nvar _router = _interopRequireDefault(require(\"./router\"));\nvar _nprogress = _interopRequireDefault(require(\"nprogress\"));\nrequire(\"nprogress/nprogress.css\");\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\n_nprogress.default.configure({\n  'showSpinner': false\n});\n_router.default.beforeEach(/*#__PURE__*/function () {\n  var _ref = (0, _asyncToGenerator2.default)(/*#__PURE__*/regeneratorRuntime.mark(function _callee(to, _, next) {\n    return regeneratorRuntime.wrap(function (_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _nprogress.default.start();\n          if (_jsCookie.default.get('token')) {\n            next();\n          } else {\n            if (!to.meta.notNeedAuth) {\n              next('/login');\n            } else {\n              next();\n            }\n          }\n        case 1:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return function (_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}());\n_router.default.afterEach(function (to) {\n  _nprogress.default.done();\n  document.title = to.meta.title;\n});", {"version": 3, "names": ["_router", "_interopRequireDefault", "require", "_nprogress", "_js<PERSON><PERSON>ie", "NProgress", "configure", "router", "beforeEach", "_ref", "_asyncToGenerator2", "default", "regeneratorRuntime", "mark", "_callee", "to", "_", "next", "wrap", "_context", "prev", "start", "Cookies", "get", "meta", "notNeedAuth", "stop", "_x", "_x2", "_x3", "apply", "arguments", "after<PERSON>ach", "done", "document", "title"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/permission.ts"], "sourcesContent": ["import router from './router'\r\nimport NProgress from 'nprogress'\r\nimport 'nprogress/nprogress.css'\r\nimport { Message } from 'element-ui'\r\nimport { Route } from 'vue-router'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport Cookies from 'js-cookie'\r\n\r\nNProgress.configure({ 'showSpinner': false })\r\n\r\nrouter.beforeEach(async (to: Route, _: Route, next: any) => {\r\n  NProgress.start()\r\n  if (Cookies.get('token')) {\r\n    next()\r\n  } else {\r\n    if (!to.meta.notNeedAuth) {\r\n      next('/login')\r\n    } else {\r\n      next()\r\n    }\r\n  }\r\n})\r\n\r\nrouter.afterEach((to: Route) => {\r\n  NProgress.done()\r\n  document.title = to.meta.title\r\n})\r\n"], "mappings": ";;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AACAA,OAAA;AAIA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEAG,kBAAS,CAACC,SAAS,CAAC;EAAE,aAAa,EAAE;AAAK,CAAE,CAAC;AAE7CC,eAAM,CAACC,UAAU;EAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAC,OAAA,eAAAC,kBAAA,CAAAC,IAAA,CAAC,SAAAC,QAAOC,EAAS,EAAEC,CAAQ,EAAEC,IAAS;IAAA,OAAAL,kBAAA,CAAAM,IAAA,WAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAF,IAAA;QAAA;UACrDZ,kBAAS,CAACgB,KAAK,EAAE;UACjB,IAAIC,iBAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE;YACxBN,IAAI,EAAE;WACP,MAAM;YACL,IAAI,CAACF,EAAE,CAACS,IAAI,CAACC,WAAW,EAAE;cACxBR,IAAI,CAAC,QAAQ,CAAC;aACf,MAAM;cACLA,IAAI,EAAE;;;QAET;QAAA;UAAA,OAAAE,QAAA,CAAAO,IAAA;MAAA;IAAA,GAAAZ,OAAA;EAAA,CACF;EAAA,iBAAAa,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAApB,IAAA,CAAAqB,KAAA,OAAAC,SAAA;EAAA;AAAA,IAAC;AAEFxB,eAAM,CAACyB,SAAS,CAAC,UAACjB,EAAS,EAAI;EAC7BV,kBAAS,CAAC4B,IAAI,EAAE;EAChBC,QAAQ,CAACC,KAAK,GAAGpB,EAAE,CAACS,IAAI,CAACW,KAAK;AAChC,CAAC,CAAC", "ignoreList": []}]}