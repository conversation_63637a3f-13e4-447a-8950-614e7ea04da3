{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue", "mtime": 1756361565147}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.function.name\");\nvar _employee = require(\"@/api/employee\");\nvar _default = exports.default = {\n  //模型数据\n  data: function data() {\n    return {\n      name: '',\n      page: 1,\n      pageSize: 10,\n      total: 0,\n      records: []\n    };\n  },\n  created: function created() {\n    this.pageQuery();\n  },\n  methods: {\n    //分页查询\n    pageQuery: function pageQuery() {\n      var _this = this;\n      //准备参数\n      var params = {\n        name: this.name,\n        page: this.page,\n        pageSize: this.pageSize\n      };\n      //发送ajax请求访问后端\n      (0, _employee.getEmployeeList)(params).then(function (res) {\n        if (res.data.code === 1) {\n          _this.total = res.data.data.total;\n          _this.records = res.data.data.records;\n        }\n      }).catch(function (err) {\n        _this.$message.error('请求出错: ' + err.message);\n      });\n    },\n    //pagesize发生变化触发\n    handleSizeChange: function handleSizeChange(pageSize) {\n      this.pageSize = pageSize;\n      this.pageQuery();\n    },\n    handleCurrentChange: function handleCurrentChange(page) {\n      this.page = page;\n      this.pageQuery();\n    },\n    //启用禁用\n    handleStartOrStop: function handleStartOrStop(row) {\n      var _this2 = this;\n      if (row.username === 'admin') {\n        this.$message.error('超级管理员不能更改');\n        return;\n      }\n      // alert(`id=${row.id}, status=${row.status}`)\n      //弹出确认框\n      this.$confirm('确认修改员工状态, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        var p = {\n          id: row.id,\n          status: row.status === 1 ? 0 : 1\n        };\n        (0, _employee.enableOrDisableEmployee)(p).then(function (res) {\n          if (res.data.code === 1) {\n            _this2.$message.success('操作成功');\n            _this2.pageQuery();\n          }\n        });\n      });\n    },\n    //跳转到添加员工页面\n    handAddEmp: function handAddEmp() {\n      //路由跳转\n      this.$router.push('/employee/add');\n    }\n  }\n};", {"version": 3, "names": ["_employee", "require", "_default", "exports", "default", "data", "name", "page", "pageSize", "total", "records", "created", "pageQuery", "methods", "_this", "params", "getEmployeeList", "then", "res", "code", "catch", "err", "$message", "error", "message", "handleSizeChange", "handleCurrentChange", "handleStartOrStop", "row", "_this2", "username", "$confirm", "confirmButtonText", "cancelButtonText", "type", "p", "id", "status", "enableOrDisableEmployee", "success", "handAddEmp", "$router", "push"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { getEmployeeList, enableOrDisableEmployee } from '@/api/employee'\r\nexport default {\r\n  //模型数据\r\n  data() {\r\n    return {\r\n      name: '', //员工姓名，对应输入框\r\n      page: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      records: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.pageQuery()\r\n  },\r\n  methods: {\r\n    //分页查询\r\n    pageQuery() {\r\n      //准备参数\r\n      const params = {\r\n        name: this.name,\r\n        page: this.page,\r\n        pageSize: this.pageSize,\r\n      }\r\n      //发送ajax请求访问后端\r\n      getEmployeeList(params)\r\n        .then((res) => {\r\n          if (res.data.code === 1) {\r\n            this.total = res.data.data.total\r\n            this.records = res.data.data.records\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          this.$message.error('请求出错: ' + err.message)\r\n        })\r\n    },\r\n    //pagesize发生变化触发\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize\r\n      this.pageQuery()\r\n    },\r\n    handleCurrentChange(page) {\r\n      this.page = page\r\n      this.pageQuery()\r\n    },\r\n    //启用禁用\r\n    handleStartOrStop(row) {\r\n      if(row.username==='admin'){\r\n        this.$message.error('超级管理员不能更改')\r\n        return\r\n      }\r\n      // alert(`id=${row.id}, status=${row.status}`)\r\n      //弹出确认框\r\n      this.$confirm('确认修改员工状态, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        const p = {\r\n          id: row.id,\r\n          status: row.status === 1 ? 0 : 1,\r\n        }\r\n        enableOrDisableEmployee(p).then((res) => {\r\n          if (res.data.code === 1) {\r\n            this.$message.success('操作成功')\r\n            this.pageQuery()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    //跳转到添加员工页面\r\n    handAddEmp(){\r\n      //路由跳转\r\n      this.$router.push('/employee/add')\r\n    }\r\n  },\r\n}\r\n"], "mappings": ";;;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AAAyE,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAC1D;EACb;EACAC,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE;KACV;EACH,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA;IACL,IAAI,CAACC,SAAS,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,SAAS,WAATA,SAASA,CAAA;MAAA,IAAAE,KAAA;MACP;MACA,IAAMC,MAAM,GAAG;QACbT,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,QAAQ,EAAE,IAAI,CAACA;OAChB;MACD;MACA,IAAAQ,yBAAe,EAACD,MAAM,CAAC,CACpBE,IAAI,CAAC,UAACC,GAAG,EAAI;QACZ,IAAIA,GAAG,CAACb,IAAI,CAACc,IAAI,KAAK,CAAC,EAAE;UACvBL,KAAI,CAACL,KAAK,GAAGS,GAAG,CAACb,IAAI,CAACA,IAAI,CAACI,KAAK;UAChCK,KAAI,CAACJ,OAAO,GAAGQ,GAAG,CAACb,IAAI,CAACA,IAAI,CAACK,OAAO;;MAExC,CAAC,CAAC,CACDU,KAAK,CAAC,UAACC,GAAG,EAAI;QACbP,KAAI,CAACQ,QAAQ,CAACC,KAAK,CAAC,QAAQ,GAAGF,GAAG,CAACG,OAAO,CAAC;MAC7C,CAAC,CAAC;IACN,CAAC;IACD;IACAC,gBAAgB,WAAhBA,gBAAgBA,CAACjB,QAAQ;MACvB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACI,SAAS,EAAE;IAClB,CAAC;IACDc,mBAAmB,WAAnBA,mBAAmBA,CAACnB,IAAI;MACtB,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACK,SAAS,EAAE;IAClB,CAAC;IACD;IACAe,iBAAiB,WAAjBA,iBAAiBA,CAACC,GAAG;MAAA,IAAAC,MAAA;MACnB,IAAGD,GAAG,CAACE,QAAQ,KAAG,OAAO,EAAC;QACxB,IAAI,CAACR,QAAQ,CAACC,KAAK,CAAC,WAAW,CAAC;QAChC;;MAEF;MACA;MACA,IAAI,CAACQ,QAAQ,CAAC,iBAAiB,EAAE,IAAI,EAAE;QACrCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;OACP,CAAC,CAACjB,IAAI,CAAC,YAAK;QACX,IAAMkB,CAAC,GAAG;UACRC,EAAE,EAAER,GAAG,CAACQ,EAAE;UACVC,MAAM,EAAET,GAAG,CAACS,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG;SAChC;QACD,IAAAC,iCAAuB,EAACH,CAAC,CAAC,CAAClB,IAAI,CAAC,UAACC,GAAG,EAAI;UACtC,IAAIA,GAAG,CAACb,IAAI,CAACc,IAAI,KAAK,CAAC,EAAE;YACvBU,MAAI,CAACP,QAAQ,CAACiB,OAAO,CAAC,MAAM,CAAC;YAC7BV,MAAI,CAACjB,SAAS,EAAE;;QAEpB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD;IACA4B,UAAU,WAAVA,UAAUA,CAAA;MACR;MACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;IACpC;;CAEH", "ignoreList": []}]}