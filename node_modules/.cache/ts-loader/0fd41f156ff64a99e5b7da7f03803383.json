{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue", "mtime": 1756349952451}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.function.name\");\nvar _employee = require(\"@/api/employee\");\nvar _default = exports.default = {\n  //模型数据\n  data: function data() {\n    return {\n      name: '',\n      page: 1,\n      pageSize: 10,\n      total: 0,\n      records: []\n    };\n  },\n  created: function created() {\n    this.pageQuery();\n  },\n  methods: {\n    //分页查询\n    pageQuery: function pageQuery() {\n      var _this = this;\n      //准备参数\n      var params = {\n        name: this.name,\n        page: this.page,\n        pageSize: this.pageSize\n      };\n      //发送ajax请求访问后端\n      (0, _employee.getEmployeeList)(params).then(function (res) {\n        if (res.data.code === 1) {\n          _this.total = res.data.data.total;\n          _this.records = res.data.data.records;\n        }\n      }).catch(function (err) {\n        _this.$message.error('请求出错: ' + err.message);\n      });\n    },\n    //pagesize发生变化触发\n    handleSizeChange: function handleSizeChange(pageSize) {\n      this.pageSize = pageSize;\n      this.pageQuery();\n    },\n    handleCurrentChange: function handleCurrentChange(page) {\n      this.page = page;\n      this.pageQuery();\n    },\n    //启用禁用\n    handleStartOrStop: function handleStartOrStop(row) {\n      var _this2 = this;\n      // alert(`id=${row.id}, status=${row.status}`)\n      (0, _employee.enableOrDisableEmployee)(row).then(function (res) {\n        if (res.data.code === 1) {\n          _this2.$message.success('操作成功');\n          _this2.pageQuery();\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["_employee", "require", "_default", "exports", "default", "data", "name", "page", "pageSize", "total", "records", "created", "pageQuery", "methods", "_this", "params", "getEmployeeList", "then", "res", "code", "catch", "err", "$message", "error", "message", "handleSizeChange", "handleCurrentChange", "handleStartOrStop", "row", "_this2", "enableOrDisableEmployee", "success"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { getEmployeeList,enableOrDisableEmployee } from '@/api/employee'\r\nexport default {\r\n  //模型数据\r\n  data() {\r\n    return {\r\n      name: '', //员工姓名，对应输入框\r\n      page: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      records: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.pageQuery()\r\n  },\r\n  methods: {\r\n    //分页查询\r\n    pageQuery() {\r\n      //准备参数\r\n      const params = {\r\n        name: this.name,\r\n        page: this.page,\r\n        pageSize: this.pageSize,\r\n      }\r\n      //发送ajax请求访问后端\r\n      getEmployeeList(params)\r\n        .then((res) => {\r\n          if (res.data.code === 1) {\r\n            this.total = res.data.data.total\r\n            this.records = res.data.data.records\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          this.$message.error('请求出错: ' + err.message)\r\n        })\r\n    },\r\n    //pagesize发生变化触发\r\n    handleSizeChange(pageSize){\r\n      this.pageSize=pageSize\r\n      this.pageQuery()\r\n    },\r\n    handleCurrentChange(page){\r\n      this.page=page\r\n      this.pageQuery()\r\n    },\r\n    //启用禁用\r\n    handleStartOrStop(row){\r\n      // alert(`id=${row.id}, status=${row.status}`)\r\n      enableOrDisableEmployee(row).then(res=>{\r\n        if(res.data.code===1){\r\n          this.$message.success('操作成功')\r\n          this.pageQuery()\r\n        }\r\n      })\r\n    },\r\n\r\n  },\r\n}\r\n"], "mappings": ";;;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AAAwE,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACzD;EACb;EACAC,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE;KACV;EACH,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA;IACL,IAAI,CAACC,SAAS,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,SAAS,WAATA,SAASA,CAAA;MAAA,IAAAE,KAAA;MACP;MACA,IAAMC,MAAM,GAAG;QACbT,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,QAAQ,EAAE,IAAI,CAACA;OAChB;MACD;MACA,IAAAQ,yBAAe,EAACD,MAAM,CAAC,CACpBE,IAAI,CAAC,UAACC,GAAG,EAAI;QACZ,IAAIA,GAAG,CAACb,IAAI,CAACc,IAAI,KAAK,CAAC,EAAE;UACvBL,KAAI,CAACL,KAAK,GAAGS,GAAG,CAACb,IAAI,CAACA,IAAI,CAACI,KAAK;UAChCK,KAAI,CAACJ,OAAO,GAAGQ,GAAG,CAACb,IAAI,CAACA,IAAI,CAACK,OAAO;;MAExC,CAAC,CAAC,CACDU,KAAK,CAAC,UAACC,GAAG,EAAI;QACbP,KAAI,CAACQ,QAAQ,CAACC,KAAK,CAAC,QAAQ,GAAGF,GAAG,CAACG,OAAO,CAAC;MAC7C,CAAC,CAAC;IACN,CAAC;IACD;IACAC,gBAAgB,WAAhBA,gBAAgBA,CAACjB,QAAQ;MACvB,IAAI,CAACA,QAAQ,GAACA,QAAQ;MACtB,IAAI,CAACI,SAAS,EAAE;IAClB,CAAC;IACDc,mBAAmB,WAAnBA,mBAAmBA,CAACnB,IAAI;MACtB,IAAI,CAACA,IAAI,GAACA,IAAI;MACd,IAAI,CAACK,SAAS,EAAE;IAClB,CAAC;IACD;IACAe,iBAAiB,WAAjBA,iBAAiBA,CAACC,GAAG;MAAA,IAAAC,MAAA;MACnB;MACA,IAAAC,iCAAuB,EAACF,GAAG,CAAC,CAACX,IAAI,CAAC,UAAAC,GAAG,EAAE;QACrC,IAAGA,GAAG,CAACb,IAAI,CAACc,IAAI,KAAG,CAAC,EAAC;UACnBU,MAAI,CAACP,QAAQ,CAACS,OAAO,CAAC,MAAM,CAAC;UAC7BF,MAAI,CAACjB,SAAS,EAAE;;MAEpB,CAAC,CAAC;IACJ;;CAGH", "ignoreList": []}]}