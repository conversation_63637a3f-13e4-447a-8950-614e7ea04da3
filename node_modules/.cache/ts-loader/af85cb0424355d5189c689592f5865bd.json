{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/setMeal.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/setMeal.ts", "mtime": 1695191940000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es7.object.get-own-property-descriptors\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.object.keys\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.querySetmealById = exports.getSetmealPage = exports.enableOrDisableSetmeal = exports.editSetmeal = exports.deleteSetmeal = exports.addSetmeal = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/defineProperty.js\"));\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n *\n * 套餐管理\n *\n **/\n//套餐分页查询\nvar getSetmealPage = exports.getSetmealPage = function getSetmealPage(params) {\n  return (0, _request.default)({\n    url: '/setmeal/page',\n    method: 'GET',\n    params: params\n  });\n};\n//套餐启售停售\nvar enableOrDisableSetmeal = exports.enableOrDisableSetmeal = function enableOrDisableSetmeal(params) {\n  return (0, _request.default)({\n    url: \"/setmeal/status/\".concat(params.status),\n    method: 'POST',\n    params: {\n      id: params.id\n    }\n  });\n};\n//删除套餐\nvar deleteSetmeal = exports.deleteSetmeal = function deleteSetmeal(ids) {\n  return (0, _request.default)({\n    url: '/setmeal',\n    method: 'DELETE',\n    params: {\n      ids: ids\n    }\n  });\n};\n// 修改数据接口\nvar editSetmeal = exports.editSetmeal = function editSetmeal(params) {\n  return (0, _request.default)({\n    url: '/setmeal',\n    method: 'put',\n    data: _objectSpread({}, params)\n  });\n};\n// 新增数据接口\nvar addSetmeal = exports.addSetmeal = function addSetmeal(params) {\n  return (0, _request.default)({\n    url: '/setmeal',\n    method: 'post',\n    data: _objectSpread({}, params)\n  });\n};\n// 查询详情接口\nvar querySetmealById = exports.querySetmealById = function querySetmealById(id) {\n  return (0, _request.default)({\n    url: \"/setmeal/\".concat(id),\n    method: 'get'\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty2", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "getSetmealPage", "exports", "params", "request", "url", "method", "enableOrDisableSetmeal", "concat", "status", "id", "deleteSetmeal", "ids", "editSetmeal", "data", "addSetmeal", "querySetmealById"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/setMeal.ts"], "sourcesContent": ["import request from '@/utils/request'\n/**\n *\n * 套餐管理\n *\n **/\n\n//套餐分页查询\nexport const getSetmealPage = (params: any) => {\n    return request({\n        url: '/setmeal/page',\n        method: 'GET',\n        params: params\n    })\n}\n\n//套餐启售停售\nexport const enableOrDisableSetmeal = (params: any) => {\n    return request({\n        url: `/setmeal/status/${params.status}`,\n        method: 'POST',\n        params: {id: params.id}\n    })\n}\n\n//删除套餐\nexport const deleteSetmeal = (ids: string) => {//1,2,3\n    return request({\n        url: '/setmeal',\n        method: 'DELETE',\n        params: {ids: ids}\n    })\n}\n\n\n  \n// 修改数据接口\nexport const editSetmeal = (params: any) => {\n    return request({\n        url: '/setmeal',\n        method: 'put',\n        data: { ...params }\n    })\n}\n\n// 新增数据接口\nexport const addSetmeal = (params: any) => {\n    return request({\n        url: '/setmeal',\n        method: 'post',\n        data: { ...params }\n    })\n}\n\n// 查询详情接口\nexport const querySetmealById = (id: string | (string | null)[]) => {\n    return request({\n        url: `/setmeal/${id}`,\n        method: 'get'\n    })\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,SAAAC,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAe,gBAAA,CAAAC,OAAA,EAAAjB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAe,yBAAA,GAAAf,MAAA,CAAAgB,gBAAA,CAAAnB,CAAA,EAAAG,MAAA,CAAAe,yBAAA,CAAAhB,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAiB,cAAA,CAAApB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AACrC;;;;;AAMA;AACO,IAAMqB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,SAAjBA,cAAcA,CAAIE,MAAW,EAAI;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;GACX,CAAC;AACN,CAAC;AAED;AACO,IAAMI,sBAAsB,GAAAL,OAAA,CAAAK,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAIJ,MAAW,EAAI;EAClD,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,qBAAAG,MAAA,CAAqBL,MAAM,CAACM,MAAM,CAAE;IACvCH,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE;MAACO,EAAE,EAAEP,MAAM,CAACO;IAAE;GACzB,CAAC;AACN,CAAC;AAED;AACO,IAAMC,aAAa,GAAAT,OAAA,CAAAS,aAAA,GAAG,SAAhBA,aAAaA,CAAIC,GAAW,EAAI;EACzC,OAAO,IAAAR,gBAAO,EAAC;IACXC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE,QAAQ;IAChBH,MAAM,EAAE;MAACS,GAAG,EAAEA;IAAG;GACpB,CAAC;AACN,CAAC;AAID;AACO,IAAMC,WAAW,GAAAX,OAAA,CAAAW,WAAA,GAAG,SAAdA,WAAWA,CAAIV,MAAW,EAAI;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAAtB,aAAA,KAAOW,MAAM;GACpB,CAAC;AACN,CAAC;AAED;AACO,IAAMY,UAAU,GAAAb,OAAA,CAAAa,UAAA,GAAG,SAAbA,UAAUA,CAAIZ,MAAW,EAAI;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAAtB,aAAA,KAAOW,MAAM;GACpB,CAAC;AACN,CAAC;AAED;AACO,IAAMa,gBAAgB,GAAAd,OAAA,CAAAc,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIN,EAA8B,EAAI;EAC/D,OAAO,IAAAN,gBAAO,EAAC;IACXC,GAAG,cAAAG,MAAA,CAAcE,EAAE,CAAE;IACrBJ,MAAM,EAAE;GACX,CAAC;AACN,CAAC", "ignoreList": []}]}