{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/requestOptimize.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/requestOptimize.ts", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.removePending = exports.pending = exports.getRequestKey = exports.checkPending = void 0;\nvar _md = _interopRequireDefault(require(\"md5\"));\n//根据请求的地址，方式，参数，统一计算出当前请求的md5值作为key\nvar getRequestKey = exports.getRequestKey = function getRequestKey(config) {\n  if (!config) {\n    // 如果没有获取到请求的相关配置信息，根据时间戳生成\n    return (0, _md.default)(+new Date());\n  }\n  var data = typeof config.data === 'string' ? config.data : JSON.stringify(config.data);\n  // console.log(config,pending,config.url,md5(config.url + '&' + config.method + '&' + data),'config')\n  return (0, _md.default)(config.url + '&' + config.method + '&' + data);\n};\n// 存储key值\nvar pending = exports.pending = {};\n// 检查key值\nvar checkPending = exports.checkPending = function checkPending(key) {\n  return !!pending[key];\n};\n// 删除key值\nvar removePending = exports.removePending = function removePending(key) {\n  // console.log(key,'key')\n  delete pending[key];\n};", {"version": 3, "names": ["_md", "_interopRequireDefault", "require", "getRequestKey", "exports", "config", "md5", "Date", "data", "JSON", "stringify", "url", "method", "pending", "checkPending", "key", "removePending"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/requestOptimize.ts"], "sourcesContent": ["import md5 from 'md5';\r\n\r\n//根据请求的地址，方式，参数，统一计算出当前请求的md5值作为key\r\nconst getRequestKey = (config) => {\r\n    if (!config) {\r\n        // 如果没有获取到请求的相关配置信息，根据时间戳生成\r\n        return md5(+new Date());\r\n    }\r\n\r\n    const data = typeof config.data === 'string' ? config.data : JSON.stringify(config.data);\r\n    // console.log(config,pending,config.url,md5(config.url + '&' + config.method + '&' + data),'config')\r\n    return md5(config.url + '&' + config.method + '&' + data);\r\n}\r\n\r\n// 存储key值\r\nconst pending = {};\r\n// 检查key值\r\nconst checkPending = (key) => !!pending[key];\r\n// 删除key值\r\nconst removePending = (key) => {\r\n    // console.log(key,'key')\r\n    delete pending[key];\r\n};\r\n\r\nexport {\r\n    getRequestKey,\r\n    pending,\r\n    checkPending,\r\n    removePending\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,GAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACA,IAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,SAAhBA,aAAaA,CAAIE,MAAM,EAAI;EAC7B,IAAI,CAACA,MAAM,EAAE;IACT;IACA,OAAO,IAAAC,WAAG,EAAC,CAAC,IAAIC,IAAI,EAAE,CAAC;;EAG3B,IAAMC,IAAI,GAAG,OAAOH,MAAM,CAACG,IAAI,KAAK,QAAQ,GAAGH,MAAM,CAACG,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,IAAI,CAAC;EACxF;EACA,OAAO,IAAAF,WAAG,EAACD,MAAM,CAACM,GAAG,GAAG,GAAG,GAAGN,MAAM,CAACO,MAAM,GAAG,GAAG,GAAGJ,IAAI,CAAC;AAC7D,CAAC;AAED;AACA,IAAMK,OAAO,GAAAT,OAAA,CAAAS,OAAA,GAAG,EAAE;AAClB;AACA,IAAMC,YAAY,GAAAV,OAAA,CAAAU,YAAA,GAAG,SAAfA,YAAYA,CAAIC,GAAG;EAAA,OAAK,CAAC,CAACF,OAAO,CAACE,GAAG,CAAC;AAAA;AAC5C;AACA,IAAMC,aAAa,GAAAZ,OAAA,CAAAY,aAAA,GAAG,SAAhBA,aAAaA,CAAID,GAAG,EAAI;EAC1B;EACA,OAAOF,OAAO,CAACE,GAAG,CAAC;AACvB,CAAC", "ignoreList": []}]}