{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/router.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/router.ts", "mtime": 1691978423000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _typeof2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/typeof.js\"));\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.weak-map\");\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _vueRouter = _interopRequireDefault(require(\"vue-router\"));\nvar _index = _interopRequireDefault(require(\"@/layout/index.vue\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != (0, _typeof2.default)(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n// import {\n//   getToken,\n//   setToken,\n//   removeToken,\n//   getStoreId,\n//   setStoreId,\n//   removeStoreId,\n//   setUserInfo,\n//   getUserInfo,\n//   removeUserInfo\n// } from \"@/utils/cookies\";\n// import store from \"@/store\";\n_vue.default.use(_vueRouter.default);\nvar router = new _vueRouter.default({\n  scrollBehavior: function scrollBehavior(to, from, savedPosition) {\n    if (savedPosition) {\n      return savedPosition;\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  },\n  base: process.env.BASE_URL,\n  routes: [{\n    path: \"/login\",\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require(\"@/views/login/index.vue\"));\n      });\n    },\n    meta: {\n      title: \"苍穹外卖\",\n      hidden: true,\n      notNeedAuth: true\n    }\n  }, {\n    path: \"/404\",\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require(\"@/views/404.vue\"));\n      });\n    },\n    meta: {\n      title: \"苍穹外卖\",\n      hidden: true,\n      notNeedAuth: true\n    }\n  }, {\n    path: \"/\",\n    component: _index.default,\n    redirect: \"/dashboard\",\n    children: [{\n      path: \"dashboard\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/dashboard/index.vue\"));\n        });\n      },\n      name: \"Dashboard\",\n      meta: {\n        title: \"工作台\",\n        icon: \"dashboard\",\n        affix: true\n      }\n    }, {\n      path: \"/statistics\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/statistics/index.vue\"));\n        });\n      },\n      meta: {\n        title: \"数据统计\",\n        icon: \"icon-statistics\"\n      }\n    }, {\n      path: \"order\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/orderDetails/index.vue\"));\n        });\n      },\n      meta: {\n        title: \"订单管理\",\n        icon: \"icon-order\"\n      }\n    }, {\n      path: \"setmeal\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/setmeal/index.vue\"));\n        });\n      },\n      meta: {\n        title: \"套餐管理\",\n        icon: \"icon-combo\"\n      }\n    }, {\n      path: \"dish\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/dish/index.vue\"));\n        });\n      },\n      meta: {\n        title: \"菜品管理\",\n        icon: \"icon-dish\"\n      }\n    }, {\n      path: \"/dish/add\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/dish/addDishtype.vue\"));\n        });\n      },\n      meta: {\n        title: \"添加菜品\",\n        hidden: true\n      }\n    }, {\n      path: \"category\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/category/index.vue\"));\n        });\n      },\n      meta: {\n        title: \"分类管理\",\n        icon: \"icon-category\"\n      }\n    }, {\n      path: \"employee\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/employee/index.vue\"));\n        });\n      },\n      meta: {\n        title: \"员工管理\",\n        icon: \"icon-employee\"\n      }\n    }, {\n      path: \"/employee/add\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/employee/addEmployee.vue\"));\n        });\n      },\n      meta: {\n        title: \"添加/修改员工\",\n        hidden: true\n      }\n    }, {\n      path: \"/setmeal/add\",\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require(\"@/views/setmeal/addSetmeal.vue\"));\n        });\n      },\n      meta: {\n        title: \"添加套餐\",\n        hidden: true\n      }\n    }]\n  }, {\n    path: \"*\",\n    redirect: \"/404\",\n    meta: {\n      hidden: true\n    }\n  }]\n});\nvar _default = exports.default = router;", {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_index", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "_typeof2", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "<PERSON><PERSON>", "use", "Router", "router", "scroll<PERSON>eh<PERSON>or", "to", "from", "savedPosition", "x", "y", "base", "process", "env", "BASE_URL", "routes", "path", "component", "Promise", "resolve", "then", "meta", "title", "hidden", "notNeedAuth", "Layout", "redirect", "children", "name", "icon", "affix", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/router.ts"], "sourcesContent": ["import Vue from \"vue\";\r\nimport Router from \"vue-router\";\r\nimport Layout from \"@/layout/index.vue\";\r\n// import {\r\n//   getToken,\r\n//   setToken,\r\n//   removeToken,\r\n//   getStoreId,\r\n//   setStoreId,\r\n//   removeStoreId,\r\n//   setUserInfo,\r\n//   getUserInfo,\r\n//   removeUserInfo\r\n// } from \"@/utils/cookies\";\r\n// import store from \"@/store\";\r\n\r\nVue.use(Router);\r\n\r\nconst router = new Router({\r\n  scrollBehavior: (to, from, savedPosition) => {\r\n    if (savedPosition) {\r\n      return savedPosition;\r\n    }\r\n    return { x: 0, y: 0 };\r\n  },\r\n  base: process.env.BASE_URL,\r\n  routes: [\r\n    {\r\n      path: \"/login\",\r\n      component: () => import(\"@/views/login/index.vue\"),\r\n      meta: { title: \"苍穹外卖\", hidden: true, notNeedAuth: true }\r\n    },\r\n    {\r\n      path: \"/404\",\r\n      component: () => import(\"@/views/404.vue\"),\r\n      meta: { title: \"苍穹外卖\", hidden: true, notNeedAuth: true }\r\n    },\r\n    {\r\n      path: \"/\",\r\n      component: Layout,\r\n      redirect: \"/dashboard\",\r\n      children: [\r\n        {\r\n          path: \"dashboard\",\r\n          component: () =>  import(\"@/views/dashboard/index.vue\"),\r\n          name: \"Dashboard\",\r\n          meta: {\r\n            title: \"工作台\",\r\n            icon: \"dashboard\",\r\n            affix: true\r\n          }\r\n        },\r\n\t\t    {\r\n          path: \"/statistics\",\r\n          component: () => import(\"@/views/statistics/index.vue\"),\r\n          meta: {\r\n            title: \"数据统计\",\r\n            icon: \"icon-statistics\"\r\n          }\r\n        },\r\n        {\r\n          path: \"order\",\r\n          component: () => import(\"@/views/orderDetails/index.vue\"),\r\n          meta: {\r\n            title: \"订单管理\",\r\n            icon: \"icon-order\"\r\n          }\r\n        },\r\n        {\r\n          path: \"setmeal\",\r\n          component: () => import(\"@/views/setmeal/index.vue\"),\r\n          meta: {\r\n            title: \"套餐管理\",\r\n            icon: \"icon-combo\"\r\n          }\r\n        },\r\n        {\r\n          path: \"dish\",\r\n          component: () => import(\"@/views/dish/index.vue\"),\r\n          meta: {\r\n            title: \"菜品管理\",\r\n            icon: \"icon-dish\"\r\n          }\r\n        },\r\n        {\r\n          path: \"/dish/add\",\r\n          component: () => import(\"@/views/dish/addDishtype.vue\"),\r\n          meta: {\r\n            title: \"添加菜品\",\r\n            hidden: true\r\n          }\r\n        },\r\n        \r\n        {\r\n          path: \"category\",\r\n          component: () => import(\"@/views/category/index.vue\"),\r\n          meta: {\r\n            title: \"分类管理\",\r\n            icon: \"icon-category\"\r\n          }\r\n        },\r\n        {\r\n          path: \"employee\",\r\n          component: () => import(\"@/views/employee/index.vue\"),\r\n          meta: {\r\n            title: \"员工管理\",\r\n            icon: \"icon-employee\"\r\n          }\r\n        },\r\n        \r\n        {\r\n          path: \"/employee/add\",\r\n          component: () => import(\"@/views/employee/addEmployee.vue\"),\r\n          meta: {\r\n            title: \"添加/修改员工\",\r\n            hidden: true\r\n          }\r\n        },\r\n        \r\n        {\r\n          path: \"/setmeal/add\",\r\n          component: () => import(\"@/views/setmeal/addSetmeal.vue\"),\r\n          meta: {\r\n            title: \"添加套餐\",\r\n            hidden: true\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      path: \"*\",\r\n      redirect: \"/404\",\r\n      meta: { hidden: true }\r\n    }\r\n  ]\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAwC,SAAAG,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,wBAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,oBAAAW,QAAA,CAAAD,OAAA,EAAAV,CAAA,0BAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAM,GAAA,CAAAZ,CAAA,UAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,GAAAM,CAAA,CAAAQ,GAAA,CAAAd,CAAA,EAAAQ,CAAA,cAAAO,EAAA,IAAAf,CAAA,gBAAAe,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,EAAA,OAAAR,CAAA,IAAAD,CAAA,GAAAY,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAApB,CAAA,EAAAe,EAAA,OAAAR,CAAA,CAAAM,GAAA,IAAAN,CAAA,CAAAO,GAAA,IAAAR,CAAA,CAAAE,CAAA,EAAAO,EAAA,EAAAR,CAAA,IAAAC,CAAA,CAAAO,EAAA,IAAAf,CAAA,CAAAe,EAAA,WAAAP,CAAA,KAAAR,CAAA,EAAAC,CAAA;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEAoB,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;AAEf,IAAMC,MAAM,GAAG,IAAID,kBAAM,CAAC;EACxBE,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,EAAE,EAAEC,IAAI,EAAEC,aAAa,EAAI;IAC1C,IAAIA,aAAa,EAAE;MACjB,OAAOA,aAAa;;IAEtB,OAAO;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;EACvB,CAAC;EACDC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BC,MAAM,EAAE,CACN;IACEC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClD6C,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAI;GACvD,EACD;IACER,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,iBAAiB;MAAA;IAAA,CAAC;IAC1C6C,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAI;GACvD,EACD;IACER,IAAI,EAAE,GAAG;IACTC,SAAS,EAAEQ,cAAM;IACjBC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE,CACR;MACEX,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAgB,6BAA6B;QAAA;MAAA,CAAC;MACvDoD,IAAI,EAAE,WAAW;MACjBP,IAAI,EAAE;QACJC,KAAK,EAAE,KAAK;QACZO,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE;;KAEV,EACH;MACId,IAAI,EAAE,aAAa;MACnBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,8BAA8B;QAAA;MAAA,CAAC;MACvD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbO,IAAI,EAAE;;KAET,EACD;MACEb,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,gCAAgC;QAAA;MAAA,CAAC;MACzD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbO,IAAI,EAAE;;KAET,EACD;MACEb,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,2BAA2B;QAAA;MAAA,CAAC;MACpD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbO,IAAI,EAAE;;KAET,EACD;MACEb,IAAI,EAAE,MAAM;MACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,wBAAwB;QAAA;MAAA,CAAC;MACjD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbO,IAAI,EAAE;;KAET,EACD;MACEb,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,8BAA8B;QAAA;MAAA,CAAC;MACvD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;;KAEX,EAED;MACEP,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,4BAA4B;QAAA;MAAA,CAAC;MACrD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbO,IAAI,EAAE;;KAET,EACD;MACEb,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,4BAA4B;QAAA;MAAA,CAAC;MACrD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbO,IAAI,EAAE;;KAET,EAED;MACEb,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,kCAAkC;QAAA;MAAA,CAAC;MAC3D6C,IAAI,EAAE;QACJC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE;;KAEX,EAED;MACEP,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,OAAAzC,uBAAA,CAAAH,OAAA,CAAe,gCAAgC;QAAA;MAAA,CAAC;MACzD6C,IAAI,EAAE;QACJC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;;KAEX;GAEJ,EACD;IACEP,IAAI,EAAE,GAAG;IACTU,QAAQ,EAAE,MAAM;IAChBL,IAAI,EAAE;MAAEE,MAAM,EAAE;IAAI;GACrB;CAEJ,CAAC;AAAC,IAAAQ,QAAA,GAAAC,OAAA,CAAA1C,OAAA,GAEYc,MAAM", "ignoreList": []}]}