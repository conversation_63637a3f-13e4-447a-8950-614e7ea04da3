{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/validate.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/validate.ts", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isValidUsername = exports.isExternal = void 0;\nvar isValidUsername = exports.isValidUsername = function isValidUsername(str) {\n  return ['admin', 'editor'].indexOf(str.trim()) >= 0;\n};\nvar isExternal = exports.isExternal = function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path);\n};", {"version": 3, "names": ["isValidUsername", "exports", "str", "indexOf", "trim", "isExternal", "path", "test"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/utils/validate.ts"], "sourcesContent": ["export const isValidUsername = (str: string) => ['admin', 'editor'].indexOf(str.trim()) >= 0;\r\n\r\nexport const isExternal = (path: string) => /^(https?:|mailto:|tel:)/.test(path);\r\n"], "mappings": ";;;;;;AAAO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,SAAlBA,eAAeA,CAAIE,GAAW;EAAA,OAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACC,OAAO,CAACD,GAAG,CAACE,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA;AAErF,IAAMC,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAAG,SAAbA,UAAUA,CAAIC,IAAY;EAAA,OAAK,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAAA", "ignoreList": []}]}