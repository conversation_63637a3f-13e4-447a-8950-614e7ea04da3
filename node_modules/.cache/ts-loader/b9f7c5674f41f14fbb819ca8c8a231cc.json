{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue", "mtime": 1691720868000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/asyncToGenerator.js\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _user = require(\"@/store/modules/user\");\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    var _this;\n    (0, _classCallCheck2.default)(this, default_1);\n    _this = _callSuper(this, default_1, arguments);\n    _this.validateUsername = function (rule, value, callback) {\n      if (!value) {\n        callback(new Error('请输入用户名'));\n      } else {\n        callback();\n      }\n    };\n    _this.validatePassword = function (rule, value, callback) {\n      if (value.length < 6) {\n        callback(new Error('密码必须在6位以上'));\n      } else {\n        callback();\n      }\n    };\n    _this.loginForm = {\n      username: 'admin',\n      password: '123456'\n    };\n    _this.loginRules = {\n      username: [{\n        validator: _this.validateUsername,\n        trigger: 'blur'\n      }],\n      password: [{\n        validator: _this.validatePassword,\n        trigger: 'blur'\n      }]\n    };\n    _this.loading = false;\n    return _this;\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"onRouteChange\",\n    value: function onRouteChange(route) {}\n    // 登录\n  }, {\n    key: \"handleLogin\",\n    value: function handleLogin() {\n      var _this2 = this;\n      this.$refs.loginForm.validate(/*#__PURE__*/function () {\n        var _ref = (0, _asyncToGenerator2.default)(/*#__PURE__*/regeneratorRuntime.mark(function _callee(valid) {\n          return regeneratorRuntime.wrap(function (_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                if (!valid) {\n                  _context.next = 2;\n                  break;\n                }\n                _this2.loading = true;\n                _context.next = 1;\n                return _user.UserModule.Login(_this2.loginForm).then(function (res) {\n                  if (String(res.code) === '1') {\n                    //登录成功，跳转到系统首页\n                    _this2.$router.push('/');\n                  } else {\n                    // this.$message.error(res.msg)\n                    _this2.loading = false;\n                  }\n                }).catch(function () {\n                  // this.$message.error('用户名或密码错误！')\n                  _this2.loading = false;\n                });\n              case 1:\n                _context.next = 3;\n                break;\n              case 2:\n                return _context.abrupt(\"return\", false);\n              case 3:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('$route', {\n  immediate: true\n})], default_1.prototype, \"onRouteChange\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'Login'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_user", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_this", "_classCallCheck2", "validateUsername", "rule", "value", "callback", "Error", "validatePassword", "length", "loginForm", "username", "password", "loginRules", "validator", "trigger", "loading", "_inherits2", "_createClass2", "key", "onRouteChange", "route", "handleLogin", "_this2", "$refs", "validate", "_ref", "_asyncToGenerator2", "regeneratorRuntime", "mark", "_callee", "valid", "wrap", "_context", "prev", "next", "UserModule", "<PERSON><PERSON>", "then", "res", "String", "code", "$router", "push", "catch", "abrupt", "stop", "_x", "arguments", "<PERSON><PERSON>", "__decorate", "Watch", "immediate", "Component", "name", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { Route } from 'vue-router'\r\nimport { Form as ElForm, Input } from 'element-ui'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport { isValidUsername } from '@/utils/validate'\r\n\r\n@Component({\r\n  name: 'Login',\r\n})\r\nexport default class extends Vue {\r\n  private validateUsername = (rule: any, value: string, callback: Function) => {\r\n    if (!value) {\r\n      callback(new Error('请输入用户名'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private validatePassword = (rule: any, value: string, callback: Function) => {\r\n    if (value.length < 6) {\r\n      callback(new Error('密码必须在6位以上'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private loginForm = {\r\n    username: 'admin',\r\n    password: '123456',\r\n  } as {\r\n    username: String\r\n    password: String\r\n  }\r\n\r\n  loginRules = {\r\n    username: [{ validator: this.validateUsername, trigger: 'blur' }],\r\n    password: [{ validator: this.validatePassword, trigger: 'blur' }],\r\n  }\r\n  private loading = false\r\n  private redirect?: string\r\n\r\n  @Watch('$route', { immediate: true })\r\n  private onRouteChange(route: Route) {}\r\n\r\n  // 登录\r\n  private handleLogin() {\r\n    (this.$refs.loginForm as ElForm).validate(async (valid: boolean) => {\r\n      if (valid) {\r\n        this.loading = true\r\n        await UserModule.Login(this.loginForm as any)\r\n          .then((res: any) => {\r\n            if (String(res.code) === '1') {\r\n              //登录成功，跳转到系统首页\r\n              this.$router.push('/')\r\n            } else {\r\n              // this.$message.error(res.msg)\r\n              this.loading = false\r\n            }\r\n          })\r\n          .catch(() => {\r\n            // this.$message.error('用户名或密码错误！')\r\n            this.loading = false\r\n          })\r\n      } else {\r\n        return false\r\n      }\r\n    })\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAD,OAAA;AAAiD,SAAAE,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAMjD,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAd,OAAA,QAAAW,SAAA;;IACUE,KAAA,CAAAE,gBAAgB,GAAG,UAACC,IAAS,EAAEC,KAAa,EAAEC,QAAkB,EAAI;MAC1E,IAAI,CAACD,KAAK,EAAE;QACVC,QAAQ,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;OAC9B,MAAM;QACLD,QAAQ,EAAE;;IAEd,CAAC;IACOL,KAAA,CAAAO,gBAAgB,GAAG,UAACJ,IAAS,EAAEC,KAAa,EAAEC,QAAkB,EAAI;MAC1E,IAAID,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;QACpBH,QAAQ,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC;OACjC,MAAM;QACLD,QAAQ,EAAE;;IAEd,CAAC;IACOL,KAAA,CAAAS,SAAS,GAAG;MAClBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;KAIX;IAEDX,KAAA,CAAAY,UAAU,GAAG;MACXF,QAAQ,EAAE,CAAC;QAAEG,SAAS,EAAEb,KAAA,CAAKE,gBAAgB;QAAEY,OAAO,EAAE;MAAM,CAAE,CAAC;MACjEH,QAAQ,EAAE,CAAC;QAAEE,SAAS,EAAEb,KAAA,CAAKO,gBAAgB;QAAEO,OAAO,EAAE;MAAM,CAAE;KACjE;IACOd,KAAA,CAAAe,OAAO,GAAG,KAAK;IAAA,OAAAf,KAAA;EA8BzB;EAAC,IAAAgB,UAAA,CAAA7B,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAAkB,aAAA,CAAA9B,OAAA,EAAAW,SAAA;IAAAoB,GAAA;IAAAd,KAAA,EA1BS,SAAAe,aAAaA,CAACC,KAAY,GAAG;IAErC;EAAA;IAAAF,GAAA;IAAAd,KAAA,EACQ,SAAAiB,WAAWA,CAAA;MAAA,IAAAC,MAAA;MAChB,IAAI,CAACC,KAAK,CAACd,SAAoB,CAACe,QAAQ;QAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAvC,OAAA,eAAAwC,kBAAA,CAAAC,IAAA,CAAC,SAAAC,QAAOC,KAAc;UAAA,OAAAH,kBAAA,CAAAI,IAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;cAAA;gBAAA,KACzDJ,KAAK;kBAAAE,QAAA,CAAAE,IAAA;kBAAA;gBAAA;gBACPZ,MAAI,CAACP,OAAO,GAAG,IAAI;gBAAAiB,QAAA,CAAAE,IAAA;gBAAA,OACbC,gBAAU,CAACC,KAAK,CAACd,MAAI,CAACb,SAAgB,CAAC,CAC1C4B,IAAI,CAAC,UAACC,GAAQ,EAAI;kBACjB,IAAIC,MAAM,CAACD,GAAG,CAACE,IAAI,CAAC,KAAK,GAAG,EAAE;oBAC5B;oBACAlB,MAAI,CAACmB,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;mBACvB,MAAM;oBACL;oBACApB,MAAI,CAACP,OAAO,GAAG,KAAK;;gBAExB,CAAC,CAAC,CACD4B,KAAK,CAAC,YAAK;kBACV;kBACArB,MAAI,CAACP,OAAO,GAAG,KAAK;gBACtB,CAAC,CAAC;cAAA;gBAAAiB,QAAA,CAAAE,IAAA;gBAAA;cAAA;gBAAA,OAAAF,QAAA,CAAAY,MAAA,WAEG,KAAK;cAAA;cAAA;gBAAA,OAAAZ,QAAA,CAAAa,IAAA;YAAA;UAAA,GAAAhB,OAAA;QAAA,CAEf;QAAA,iBAAAiB,EAAA;UAAA,OAAArB,IAAA,CAAAhC,KAAA,OAAAsD,SAAA;QAAA;MAAA,IAAC;IACJ;EAAC;AAAA,EAxD0BC,yBAAG,CAyD/B;AA1BC,IAAAC,iBAAA,GADC,IAAAC,2BAAK,EAAC,QAAQ,EAAE;EAAEC,SAAS,EAAE;AAAI,CAAE,CAAC,C,6CACC;AA/BxCrD,SAAA,OAAAmD,iBAAA,GAHC,IAAAG,+BAAS,EAAC;EACTC,IAAI,EAAE;CACP,CAAC,C,YA0DD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAApE,OAAA,G", "ignoreList": []}]}