{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/category.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/category.ts", "mtime": 1692166918000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es7.object.get-own-property-descriptors\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.object.keys\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getCategoryPage = exports.getCategoryByType = exports.enableOrDisableEmployee = exports.editCategory = exports.deleCategory = exports.addCategory = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/defineProperty.js\"));\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n *\n * 分类管理\n *\n **/\n// 查询分类列表接口\nvar getCategoryPage = exports.getCategoryPage = function getCategoryPage(params) {\n  return (0, _request.default)({\n    url: '/category/page',\n    method: 'get',\n    params: params\n  });\n};\n// 删除当前列的接口\nvar deleCategory = exports.deleCategory = function deleCategory(ids) {\n  return (0, _request.default)({\n    url: '/category',\n    method: 'delete',\n    params: {\n      id: ids\n    }\n  });\n};\n// 修改接口\nvar editCategory = exports.editCategory = function editCategory(params) {\n  return (0, _request.default)({\n    url: '/category',\n    method: 'put',\n    data: _objectSpread({}, params)\n  });\n};\n// 新增接口\nvar addCategory = exports.addCategory = function addCategory(params) {\n  return (0, _request.default)({\n    url: '/category',\n    method: 'post',\n    data: _objectSpread({}, params)\n  });\n};\n// 修改---启用禁用接口\nvar enableOrDisableEmployee = exports.enableOrDisableEmployee = function enableOrDisableEmployee(params) {\n  return (0, _request.default)({\n    url: \"/category/status/\".concat(params.status),\n    method: 'post',\n    params: {\n      id: params.id\n    }\n  });\n};\n// 根据类型查询分类：1为菜品分类 2为套餐分类\nvar getCategoryByType = exports.getCategoryByType = function getCategoryByType(params) {\n  return (0, _request.default)({\n    url: \"/category/list\",\n    method: 'get',\n    params: params\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty2", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "getCategoryPage", "exports", "params", "request", "url", "method", "deleCategory", "ids", "id", "editCategory", "data", "addCategory", "enableOrDisableEmployee", "concat", "status", "getCategoryByType"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/category.ts"], "sourcesContent": ["import request from '@/utils/request';\r\n/**\r\n *\r\n * 分类管理\r\n *\r\n **/\r\n\r\n// 查询分类列表接口\r\nexport const getCategoryPage = (params: any) => {\r\n  return request({\r\n    url: '/category/page',\r\n    method: 'get',\r\n    params\r\n  });\r\n};\r\n\r\n// 删除当前列的接口\r\nexport const deleCategory = (ids: string) => {\r\n  return request({\r\n    url: '/category',\r\n    method: 'delete',\r\n    params: { id:ids }\r\n  });\r\n};\r\n\r\n// 修改接口\r\nexport const editCategory = (params: any) => {\r\n  return request({\r\n    url: '/category',\r\n    method: 'put',\r\n    data: { ...params }\r\n  });\r\n};\r\n\r\n// 新增接口\r\nexport const addCategory = (params: any) => {\r\n  return request({\r\n    url: '/category',\r\n    method: 'post',\r\n    data: { ...params }\r\n  });\r\n};\r\n\r\n// 修改---启用禁用接口\r\nexport const enableOrDisableEmployee = (params: any) => {\r\n  return request({\r\n    url: `/category/status/${params.status}`,\r\n    method: 'post',\r\n    params: { id:params.id }\r\n  })\r\n}\r\n\r\n// 根据类型查询分类：1为菜品分类 2为套餐分类\r\nexport const getCategoryByType = (params: any) => {\r\n  return request({\r\n    url: `/category/list`,\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAsC,SAAAC,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAe,gBAAA,CAAAC,OAAA,EAAAjB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAe,yBAAA,GAAAf,MAAA,CAAAgB,gBAAA,CAAAnB,CAAA,EAAAG,MAAA,CAAAe,yBAAA,CAAAhB,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAiB,cAAA,CAAApB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AACtC;;;;;AAMA;AACO,IAAMqB,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,SAAlBA,eAAeA,CAAIE,MAAW,EAAI;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAC;AACJ,CAAC;AAED;AACO,IAAMI,YAAY,GAAAL,OAAA,CAAAK,YAAA,GAAG,SAAfA,YAAYA,CAAIC,GAAW,EAAI;EAC1C,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,QAAQ;IAChBH,MAAM,EAAE;MAAEM,EAAE,EAACD;IAAG;GACjB,CAAC;AACJ,CAAC;AAED;AACO,IAAME,YAAY,GAAAR,OAAA,CAAAQ,YAAA,GAAG,SAAfA,YAAYA,CAAIP,MAAW,EAAI;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAAnB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMS,WAAW,GAAAV,OAAA,CAAAU,WAAA,GAAG,SAAdA,WAAWA,CAAIT,MAAW,EAAI;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAAnB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMU,uBAAuB,GAAAX,OAAA,CAAAW,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAIV,MAAW,EAAI;EACrD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,sBAAAS,MAAA,CAAsBX,MAAM,CAACY,MAAM,CAAE;IACxCT,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE;MAAEM,EAAE,EAACN,MAAM,CAACM;IAAE;GACvB,CAAC;AACJ,CAAC;AAED;AACO,IAAMO,iBAAiB,GAAAd,OAAA,CAAAc,iBAAA,GAAG,SAApBA,iBAAiBA,CAAIb,MAAW,EAAI;EAC/C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,kBAAkB;IACrBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;GACT,CAAC;AACJ,CAAC", "ignoreList": []}]}