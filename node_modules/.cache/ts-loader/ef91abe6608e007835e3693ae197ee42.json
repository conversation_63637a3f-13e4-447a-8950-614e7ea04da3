{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/turnoverStatistics.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/turnoverStatistics.vue", "mtime": 1656313985000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/typeof.js\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.weak-map\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar echarts = _interopRequireWildcard(require(\"echarts\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    (0, _classCallCheck2.default)(this, default_1);\n    return _callSuper(this, default_1, arguments);\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"getData\",\n    value: function getData() {\n      var _this = this;\n      this.$nextTick(function () {\n        _this.initChart();\n      });\n    }\n  }, {\n    key: \"initChart\",\n    value: function initChart() {\n      var chartDom = document.getElementById('main');\n      var myChart = echarts.init(chartDom);\n      var option;\n      option = {\n        // title: {\n        //   text: '营业额(元)',\n        //   top: 'bottom',\n        //   left: 'center',\n        //   textAlign: 'center',\n        //   textStyle: {\n        //     fontSize: 12,\n        //     fontWeight: 'normal',\n        //   },\n        // },\n        tooltip: {\n          trigger: 'axis'\n        },\n        grid: {\n          top: '5%',\n          left: '10',\n          right: '50',\n          bottom: '12%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          axisLabel: {\n            //X轴字体颜色\n            textStyle: {\n              color: '#666',\n              fontSize: '12px'\n            }\n          },\n          axisLine: {\n            //X轴线颜色\n            lineStyle: {\n              color: '#E5E4E4',\n              width: 1\n            }\n          },\n          data: this.turnoverdata.dateList\n        },\n        yAxis: [{\n          type: 'value',\n          min: 0,\n          //max: 50000,\n          //interval: 1000,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px'\n            }\n            // formatter: \"{value} ml\",//单位\n          }\n        }],\n        series: [{\n          name: '营业额',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false,\n          showSymbol: false,\n          symbolSize: 10,\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#F29C1B',\n              lineStyle: {\n                color: '#FFD000'\n              }\n            },\n            emphasis: {\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FFC100'\n            }\n          },\n          data: this.turnoverdata.turnoverList\n        }]\n      };\n      option && myChart.setOption(option);\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)()], default_1.prototype, \"turnoverdata\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('turnoverdata')], default_1.prototype, \"getData\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'TurnoverStatistics'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "echarts", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "_typeof", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "default_1", "_Vue", "_classCallCheck2", "arguments", "_inherits2", "_createClass2", "key", "value", "getData", "_this", "$nextTick", "initChart", "chartDom", "document", "getElementById", "myChart", "init", "option", "tooltip", "trigger", "grid", "top", "left", "right", "bottom", "containLabel", "xAxis", "type", "boundaryGap", "axisLabel", "textStyle", "color", "fontSize", "axisLine", "lineStyle", "width", "data", "turnoverdata", "dateList", "yAxis", "min", "series", "name", "smooth", "showSymbol", "symbolSize", "itemStyle", "normal", "emphasis", "borderWidth", "borderColor", "turnoverList", "setOption", "<PERSON><PERSON>", "__decorate", "Prop", "Watch", "Component", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/turnoverStatistics.vue?vue&type=script&lang=ts"], "sourcesContent": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'TurnoverStatistics',\n})\nexport default class extends Vue {\n  @Prop() private turnoverdata!: any\n  @Watch('turnoverdata')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('main') as any\n    const myChart = echarts.init(chartDom)\n\n    var option: any\n    option = {\n      // title: {\n      //   text: '营业额(元)',\n      //   top: 'bottom',\n      //   left: 'center',\n      //   textAlign: 'center',\n      //   textStyle: {\n      //     fontSize: 12,\n      //     fontWeight: 'normal',\n      //   },\n      // },\n      tooltip: {\n        trigger: 'axis',\n      },\n      grid: {\n        top: '5%',\n        left: '10',\n        right: '50',\n        bottom: '12%',\n        containLabel: true,\n      },\n      xAxis: {\n        type: 'category',\n        boundaryGap: false,\n        axisLabel: {\n          //X轴字体颜色\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n        },\n        axisLine: {\n          //X轴线颜色\n          lineStyle: {\n            color: '#E5E4E4',\n            width: 1, //x轴线的宽度\n          },\n        },\n        data: this.turnoverdata.dateList, //后端传来的动态数据\n      },\n      yAxis: [\n        {\n          type: 'value',\n          min: 0,\n          //max: 50000,\n          //interval: 1000,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px',\n            }\n            // formatter: \"{value} ml\",//单位\n          }\n        }\n      ],\n      series: [\n        {\n          name: '营业额',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10,\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#F29C1B',\n              lineStyle: {\n                color: '#FFD000',\n              },\n            },\n            emphasis: {\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FFC100',\n            },\n          },\n\n          data: this.turnoverdata.turnoverList,\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAkC,SAAAE,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,wBAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,gBAAAW,OAAA,CAAAX,CAAA,0BAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAM,GAAA,CAAAZ,CAAA,UAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,GAAAM,CAAA,CAAAQ,GAAA,CAAAd,CAAA,EAAAQ,CAAA,cAAAO,EAAA,IAAAf,CAAA,gBAAAe,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,EAAA,OAAAR,CAAA,IAAAD,CAAA,GAAAY,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAApB,CAAA,EAAAe,EAAA,OAAAR,CAAA,CAAAM,GAAA,IAAAN,CAAA,CAAAO,GAAA,IAAAR,CAAA,CAAAE,CAAA,EAAAO,EAAA,EAAAR,CAAA,IAAAC,CAAA,CAAAO,EAAA,IAAAf,CAAA,CAAAe,EAAA,WAAAP,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAoB,WAAApB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAgB,gBAAA,CAAAZ,OAAA,EAAAJ,CAAA,OAAAiB,2BAAA,CAAAb,OAAA,EAAAT,CAAA,EAAAuB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAApB,CAAA,EAAAN,CAAA,YAAAsB,gBAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAA0B,WAAA,IAAArB,CAAA,CAAAsB,KAAA,CAAA3B,CAAA,EAAAD,CAAA;AAAA,SAAAwB,0BAAA,cAAAvB,CAAA,IAAA4B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA5B,CAAA,aAAAuB,yBAAA,YAAAA,0BAAA,aAAAvB,CAAA;AAIlC,IAAA+B,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAxB,OAAA,QAAAsB,SAAA;IAAA,OAAAX,UAAA,OAAAW,SAAA,EAAAG,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAA1B,OAAA,EAAAsB,SAAA,EAAAC,IAAA;EAAA,WAAAI,aAAA,CAAA3B,OAAA,EAAAsB,SAAA;IAAAM,GAAA;IAAAC,KAAA,EAGE,SAAAC,OAAOA,CAAA;MAAA,IAAAC,KAAA;MACL,IAAI,CAACC,SAAS,CAAC,YAAK;QAClBD,KAAI,CAACE,SAAS,EAAE;MAClB,CAAC,CAAC;IACJ;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAI,SAASA,CAAA;MAEP,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAQ;MACvD,IAAMC,OAAO,GAAGjD,OAAO,CAACkD,IAAI,CAACJ,QAAQ,CAAC;MAEtC,IAAIK,MAAW;MACfA,MAAM,GAAG;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,OAAO,EAAE;UACPC,OAAO,EAAE;SACV;QACDC,IAAI,EAAE;UACJC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,YAAY,EAAE;SACf;QACDC,KAAK,EAAE;UACLC,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,KAAK;UAClBC,SAAS,EAAE;YACT;YACAC,SAAS,EAAE;cACTC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;;WAEb;UACDC,QAAQ,EAAE;YACR;YACAC,SAAS,EAAE;cACTH,KAAK,EAAE,SAAS;cAChBI,KAAK,EAAE;;WAEV;UACDC,IAAI,EAAE,IAAI,CAACC,YAAY,CAACC;SACzB;QACDC,KAAK,EAAE,CACL;UACEZ,IAAI,EAAE,OAAO;UACba,GAAG,EAAE,CAAC;UACN;UACA;UACAX,SAAS,EAAE;YACTC,SAAS,EAAE;cACTC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;;YAEZ;;SAEH,CACF;QACDS,MAAM,EAAE,CACN;UACEC,IAAI,EAAE,KAAK;UACXf,IAAI,EAAE,MAAM;UACZ;UACAgB,MAAM,EAAE,KAAK;UACbC,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,EAAE;UACd;UACAC,SAAS,EAAE;YACTC,MAAM,EAAE;cACNhB,KAAK,EAAE,SAAS;cAChBG,SAAS,EAAE;gBACTH,KAAK,EAAE;;aAEV;YACDiB,QAAQ,EAAE;cACRjB,KAAK,EAAE,MAAM;cACbkB,WAAW,EAAE,CAAC;cACdC,WAAW,EAAE;;WAEhB;UAEDd,IAAI,EAAE,IAAI,CAACC,YAAY,CAACc;SACzB;OAEJ;MACDlC,MAAM,IAAIF,OAAO,CAACqC,SAAS,CAACnC,MAAM,CAAC;IACrC;EAAC;AAAA,EAjG0BoC,yBAAG,CAkG/B;AAjGS,IAAAC,iBAAA,GAAP,IAAAC,0BAAI,GAAE,C,8CAA2B;AAElC,IAAAD,iBAAA,GADC,IAAAE,2BAAK,EAAC,cAAc,CAAC,C,uCAKrB;AAPHxD,SAAA,OAAAsD,iBAAA,GAHC,IAAAG,+BAAS,EAAC;EACTf,IAAI,EAAE;CACP,CAAC,C,YAmGD;AAAA,IAAAgB,QAAA,GAAAC,OAAA,CAAAjF,OAAA,G", "ignoreList": []}]}