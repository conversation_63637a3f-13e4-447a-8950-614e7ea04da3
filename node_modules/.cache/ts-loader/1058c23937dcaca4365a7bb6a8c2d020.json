{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/order.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/order.ts", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es7.object.get-own-property-descriptors\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.object.keys\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.queryOrderDetailById = exports.orderReject = exports.orderCancel = exports.orderAccept = exports.getOrderListBy = exports.getOrderDetailPage = exports.deliveryOrder = exports.completeOrder = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/defineProperty.js\"));\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n// 查询列表页接口\nvar getOrderDetailPage = exports.getOrderDetailPage = function getOrderDetailPage(params) {\n  return (0, _request.default)({\n    url: '/order/conditionSearch',\n    method: 'get',\n    params: params\n  });\n};\n// 查看接口\nvar queryOrderDetailById = exports.queryOrderDetailById = function queryOrderDetailById(params) {\n  return (0, _request.default)({\n    url: \"/order/details/\".concat(params.orderId),\n    method: 'get'\n  });\n};\n// 派送接口\nvar deliveryOrder = exports.deliveryOrder = function deliveryOrder(params) {\n  return (0, _request.default)({\n    url: \"/order/delivery/\".concat(params.id),\n    method: 'put' /*  */\n  });\n};\n//完成接口\nvar completeOrder = exports.completeOrder = function completeOrder(params) {\n  return (0, _request.default)({\n    url: \"/order/complete/\".concat(params.id),\n    method: 'put' /*  */\n  });\n};\n//订单取消\nvar orderCancel = exports.orderCancel = function orderCancel(params) {\n  return (0, _request.default)({\n    url: '/order/cancel',\n    method: 'put' /*  */,\n    data: _objectSpread({}, params)\n  });\n};\n//接单\nvar orderAccept = exports.orderAccept = function orderAccept(params) {\n  return (0, _request.default)({\n    url: '/order/confirm',\n    method: 'put' /*  */,\n    data: _objectSpread({}, params)\n  });\n};\n//拒单\nvar orderReject = exports.orderReject = function orderReject(params) {\n  return (0, _request.default)({\n    url: '/order/rejection',\n    method: 'put' /*  */,\n    data: _objectSpread({}, params)\n  });\n};\n//获取待处理，待派送，派送中数量\nvar getOrderListBy = exports.getOrderListBy = function getOrderListBy(params) {\n  return (0, _request.default)({\n    url: '/order/statistics',\n    method: 'get' /*  */\n  });\n};", {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty2", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "getOrderDetailPage", "exports", "params", "request", "url", "method", "queryOrderDetailById", "concat", "orderId", "deliveryOrder", "id", "completeOrder", "orderCancel", "data", "orderAccept", "orderReject", "getOrderListBy"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/api/order.ts"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询列表页接口\r\nexport const getOrderDetailPage = (params: any) => {\r\n  return request({\r\n    url: '/order/conditionSearch',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 查看接口\r\nexport const queryOrderDetailById = (params: any) => {\r\n  return request({\r\n    url: `/order/details/${params.orderId}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 派送接口\r\nexport const deliveryOrder = (params: any) => {\r\n  return request({\r\n    url: `/order/delivery/${params.id}`,\r\n    method: 'put' /*  */\r\n  })\r\n}\r\n//完成接口\r\nexport const completeOrder = (params: any) => {\r\n  return request({\r\n    url: `/order/complete/${params.id}`,\r\n    method: 'put' /*  */\r\n  })\r\n}\r\n\r\n//订单取消\r\nexport const orderCancel = (params: any) => {\r\n  return request({\r\n    url: '/order/cancel',\r\n    method: 'put' /*  */,\r\n    data: { ...params }\r\n  })\r\n}\r\n\r\n//接单\r\nexport const orderAccept = (params: any) => {\r\n  return request({\r\n    url: '/order/confirm',\r\n    method: 'put' /*  */,\r\n    data: { ...params }\r\n  })\r\n}\r\n\r\n//拒单\r\nexport const orderReject = (params: any) => {\r\n  return request({\r\n    url: '/order/rejection',\r\n    method: 'put' /*  */,\r\n    data: { ...params }\r\n  })\r\n}\r\n\r\n//获取待处理，待派送，派送中数量\r\nexport const getOrderListBy = (params: any) => {\r\n  return request({\r\n    url: '/order/statistics',\r\n    method: 'get' /*  */\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,SAAAC,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAe,gBAAA,CAAAC,OAAA,EAAAjB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAe,yBAAA,GAAAf,MAAA,CAAAgB,gBAAA,CAAAnB,CAAA,EAAAG,MAAA,CAAAe,yBAAA,CAAAhB,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAiB,cAAA,CAAApB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAErC;AACO,IAAMqB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,SAArBA,kBAAkBA,CAAIE,MAAW,EAAI;EAChD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;GACD,CAAC;AACJ,CAAC;AAED;AACO,IAAMI,oBAAoB,GAAAL,OAAA,CAAAK,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIJ,MAAW,EAAI;EAClD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,oBAAAG,MAAA,CAAoBL,MAAM,CAACM,OAAO,CAAE;IACvCH,MAAM,EAAE;GACT,CAAC;AACJ,CAAC;AAED;AACO,IAAMI,aAAa,GAAAR,OAAA,CAAAQ,aAAA,GAAG,SAAhBA,aAAaA,CAAIP,MAAW,EAAI;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,qBAAAG,MAAA,CAAqBL,MAAM,CAACQ,EAAE,CAAE;IACnCL,MAAM,EAAE,KAAK,CAAC;GACf,CAAC;AACJ,CAAC;AACD;AACO,IAAMM,aAAa,GAAAV,OAAA,CAAAU,aAAA,GAAG,SAAhBA,aAAaA,CAAIT,MAAW,EAAI;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,qBAAAG,MAAA,CAAqBL,MAAM,CAACQ,EAAE,CAAE;IACnCL,MAAM,EAAE,KAAK,CAAC;GACf,CAAC;AACJ,CAAC;AAED;AACO,IAAMO,WAAW,GAAAX,OAAA,CAAAW,WAAA,GAAG,SAAdA,WAAWA,CAAIV,MAAW,EAAI;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK,CAAC;IACdQ,IAAI,EAAAtB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMY,WAAW,GAAAb,OAAA,CAAAa,WAAA,GAAG,SAAdA,WAAWA,CAAIZ,MAAW,EAAI;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK,CAAC;IACdQ,IAAI,EAAAtB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMa,WAAW,GAAAd,OAAA,CAAAc,WAAA,GAAG,SAAdA,WAAWA,CAAIb,MAAW,EAAI;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK,CAAC;IACdQ,IAAI,EAAAtB,aAAA,KAAOW,MAAM;GAClB,CAAC;AACJ,CAAC;AAED;AACO,IAAMc,cAAc,GAAAf,OAAA,CAAAe,cAAA,GAAG,SAAjBA,cAAcA,CAAId,MAAW,EAAI;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK,CAAC;GACf,CAAC;AACJ,CAAC", "ignoreList": []}]}