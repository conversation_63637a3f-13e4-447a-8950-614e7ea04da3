{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _vueClassComponent = require(\"vue-class-component\");\nvar _app = require(\"@/store/modules/app\");\nvar _components = require(\"./components\");\nvar _resize = _interopRequireDefault(require(\"./mixin/resize\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_mixins) {\n  function default_1() {\n    (0, _classCallCheck2.default)(this, default_1);\n    return _callSuper(this, default_1, arguments);\n  }\n  (0, _inherits2.default)(default_1, _mixins);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"classObj\",\n    get: function get() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === _app.DeviceType.Mobile\n      };\n    }\n  }, {\n    key: \"handleClickOutside\",\n    value: function handleClickOutside() {\n      _app.AppModule.CloseSideBar(false);\n    }\n  }]);\n}((0, _vueClassComponent.mixins)(_resize.default));\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'Layout',\n  components: {\n    AppMain: _components.AppMain,\n    Navbar: _components.Navbar,\n    Sidebar: _components.Sidebar\n  }\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_vueClassComponent", "_app", "_components", "_resize", "_interopRequireDefault", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_mixins", "_classCallCheck2", "arguments", "_inherits2", "_createClass2", "key", "get", "hideSidebar", "sidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "device", "DeviceType", "Mobile", "value", "handleClickOutside", "AppModule", "CloseSideBar", "mixins", "ResizeMixin", "__decorate", "Component", "name", "components", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sidebar", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { Component } from 'vue-property-decorator'\r\nimport { mixins } from 'vue-class-component'\r\nimport { DeviceType, AppModule } from '@/store/modules/app'\r\nimport { AppMain, Navbar, Sidebar } from './components'\r\nimport ResizeMixin from './mixin/resize'\r\n\r\n@Component({\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    Sidebar,\r\n  },\r\n})\r\nexport default class extends mixins(ResizeMixin) {\r\n  get classObj() {\r\n    return {\r\n      hideSidebar: !this.sidebar.opened,\r\n      openSidebar: this.sidebar.opened,\r\n      withoutAnimation: this.sidebar.withoutAnimation,\r\n      mobile: this.device === DeviceType.Mobile,\r\n    }\r\n  }\r\n\r\n  private handleClickOutside() {\r\n    AppModule.CloseSideBar(false)\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAC,sBAAA,CAAAL,OAAA;AAAwC,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAUxC,IAAAe,SAAA,0BAAAC,OAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAb,OAAA,QAAAW,SAAA;IAAA,OAAAhB,UAAA,OAAAgB,SAAA,EAAAG,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAf,OAAA,EAAAW,SAAA,EAAAC,OAAA;EAAA,WAAAI,aAAA,CAAAhB,OAAA,EAAAW,SAAA;IAAAM,GAAA;IAAAC,GAAA,EACE,SAAAA,IAAA,EAAY;MACV,OAAO;QACLC,WAAW,EAAE,CAAC,IAAI,CAACC,OAAO,CAACC,MAAM;QACjCC,WAAW,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM;QAChCE,gBAAgB,EAAE,IAAI,CAACH,OAAO,CAACG,gBAAgB;QAC/CC,MAAM,EAAE,IAAI,CAACC,MAAM,KAAKC,eAAU,CAACC;OACpC;IACH;EAAC;IAAAV,GAAA;IAAAW,KAAA,EAEO,SAAAC,kBAAkBA,CAAA;MACxBC,cAAS,CAACC,YAAY,CAAC,KAAK,CAAC;IAC/B;EAAC;AAAA,EAZ0B,IAAAC,yBAAM,EAACC,eAAW,CAAC,CAa/C;AAbDtB,SAAA,OAAAuB,iBAAA,GARC,IAAAC,+BAAS,EAAC;EACTC,IAAI,EAAE,QAAQ;EACdC,UAAU,EAAE;IACVC,OAAO,EAAPA,mBAAO;IACPC,MAAM,EAANA,kBAAM;IACNC,OAAO,EAAPA;;CAEH,CAAC,C,YAcD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA1C,OAAA,G", "ignoreList": []}]}