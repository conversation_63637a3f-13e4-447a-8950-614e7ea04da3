{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _cookies = require(\"@/utils/cookies\");\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    var _this;\n    (0, _classCallCheck2.default)(this, default_1);\n    _this = _callSuper(this, default_1, arguments);\n    _this.headers = {\n      token: (0, _cookies.getToken)()\n    };\n    _this.imageUrl = '';\n    return _this;\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"handleRemove\",\n    value: function handleRemove() {}\n  }, {\n    key: \"onChange\",\n    value: function onChange(val) {\n      this.imageUrl = val;\n    }\n  }, {\n    key: \"handleError\",\n    value: function handleError(err, file, fileList) {\n      console.log(err, file, fileList, 'handleError');\n      this.$message({\n        message: '图片上传失败',\n        type: 'error'\n      });\n    }\n  }, {\n    key: \"handleAvatarSuccess\",\n    value: function handleAvatarSuccess(response, file, fileList) {\n      // this.imageUrl = response.data\n      // this.imageUrl = `http://************:8080/common/download?name=${response.data}`\n      this.imageUrl = \"\".concat(response.data);\n      // this.imageUrl = `${baseUrl}/common/download?name=${response.data}`\n      this.$emit('imageChange', this.imageUrl);\n    }\n  }, {\n    key: \"oploadImgDel\",\n    value: function oploadImgDel() {\n      this.imageUrl = '';\n      this.$emit('imageChange', this.imageUrl);\n    }\n  }, {\n    key: \"beforeAvatarUpload\",\n    value: function beforeAvatarUpload(file) {\n      var isLt2M = file.size / 1024 / 1024 < this.size;\n      if (!isLt2M) {\n        this.$message({\n          message: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u80FD\\u8D85\\u8FC7\".concat(this.size, \"M!\"),\n          type: 'error'\n        });\n        return false;\n      }\n    }\n  }]);\n}(_vuePropertyDecorator.Vue);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: '.jpg,.jpeg,.png'\n})], default_1.prototype, \"type\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: 2\n})], default_1.prototype, \"size\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Prop)({\n  default: ''\n})], default_1.prototype, \"propImageUrl\", void 0);\n(0, _tslib.__decorate)([(0, _vuePropertyDecorator.Watch)('propImageUrl')], default_1.prototype, \"onChange\", null);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'UploadImage'\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_cookies", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_this", "_classCallCheck2", "headers", "token", "getToken", "imageUrl", "_inherits2", "_createClass2", "key", "value", "handleRemove", "onChange", "val", "handleError", "err", "file", "fileList", "console", "log", "$message", "message", "type", "handleAvatarSuccess", "response", "concat", "data", "$emit", "oploadImgDel", "beforeAvatarUpload", "isLt2M", "size", "<PERSON><PERSON>", "__decorate", "Prop", "Watch", "Component", "name", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { Vue, Component, Prop, Watch } from 'vue-property-decorator'\r\nimport { baseUrl } from '@/config.json'\r\nimport { getToken } from '@/utils/cookies'\r\n@Component({\r\n  name: 'UploadImage'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: '.jpg,.jpeg,.png' }) type: string\r\n  @Prop({ default: 2 }) size: number\r\n  @Prop({ default: '' }) propImageUrl: string\r\n\r\n  private headers = {\r\n    token: getToken()\r\n  }\r\n  private imageUrl = ''\r\n  handleRemove() {}\r\n\r\n  @Watch('propImageUrl')\r\n  private onChange(val) {\r\n    this.imageUrl = val\r\n  }\r\n\r\n  handleError(err, file, fileList) {\r\n    console.log(err, file, fileList, 'handleError')\r\n    this.$message({\r\n      message: '图片上传失败',\r\n      type: 'error'\r\n    })\r\n  }\r\n\r\n  handleAvatarSuccess(response: any, file: any, fileList: any) {\r\n    // this.imageUrl = response.data\r\n    // this.imageUrl = `http://************:8080/common/download?name=${response.data}`\r\n    this.imageUrl = `${response.data}`\r\n    // this.imageUrl = `${baseUrl}/common/download?name=${response.data}`\r\n\r\n    this.$emit('imageChange', this.imageUrl)\r\n  }\r\n\r\n  oploadImgDel() {\r\n    this.imageUrl = ''\r\n    this.$emit('imageChange', this.imageUrl)\r\n  }\r\n  beforeAvatarUpload(file) {\r\n    const isLt2M = file.size / 1024 / 1024 < this.size\r\n    if (!isLt2M) {\r\n      this.$message({\r\n        message: `上传文件大小不能超过${this.size}M!`,\r\n        type: 'error'\r\n      })\r\n      return false\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAA0C,SAAAE,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAI1C,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAd,OAAA,QAAAW,SAAA;;IAKUE,KAAA,CAAAE,OAAO,GAAG;MAChBC,KAAK,EAAE,IAAAC,iBAAQ;KAChB;IACOJ,KAAA,CAAAK,QAAQ,GAAG,EAAE;IAAA,OAAAL,KAAA;EAuCvB;EAAC,IAAAM,UAAA,CAAAnB,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAAQ,aAAA,CAAApB,OAAA,EAAAW,SAAA;IAAAU,GAAA;IAAAC,KAAA,EAtCC,SAAAC,YAAYA,CAAA,GAAI;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAGT,SAAAE,QAAQA,CAACC,GAAG;MAClB,IAAI,CAACP,QAAQ,GAAGO,GAAG;IACrB;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAI,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAEC,QAAQ;MAC7BC,OAAO,CAACC,GAAG,CAACJ,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAE,aAAa,CAAC;MAC/C,IAAI,CAACG,QAAQ,CAAC;QACZC,OAAO,EAAE,QAAQ;QACjBC,IAAI,EAAE;OACP,CAAC;IACJ;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAAa,mBAAmBA,CAACC,QAAa,EAAER,IAAS,EAAEC,QAAa;MACzD;MACA;MACA,IAAI,CAACX,QAAQ,MAAAmB,MAAA,CAAMD,QAAQ,CAACE,IAAI,CAAE;MAClC;MAEA,IAAI,CAACC,KAAK,CAAC,aAAa,EAAE,IAAI,CAACrB,QAAQ,CAAC;IAC1C;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAAkB,YAAYA,CAAA;MACV,IAAI,CAACtB,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACqB,KAAK,CAAC,aAAa,EAAE,IAAI,CAACrB,QAAQ,CAAC;IAC1C;EAAC;IAAAG,GAAA;IAAAC,KAAA,EACD,SAAAmB,kBAAkBA,CAACb,IAAI;MACrB,IAAMc,MAAM,GAAGd,IAAI,CAACe,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAACA,IAAI;MAClD,IAAI,CAACD,MAAM,EAAE;QACX,IAAI,CAACV,QAAQ,CAAC;UACZC,OAAO,iEAAAI,MAAA,CAAe,IAAI,CAACM,IAAI,OAAI;UACnCT,IAAI,EAAE;SACP,CAAC;QACF,OAAO,KAAK;;IAEhB;EAAC;AAAA,EA9C0BU,yBAAG,CA+C/B;AA9CuC,IAAAC,iBAAA,GAArC,IAAAC,0BAAI,EAAC;EAAE9C,OAAO,EAAE;AAAiB,CAAE,CAAC,C,sCAAa;AAC5B,IAAA6C,iBAAA,GAArB,IAAAC,0BAAI,EAAC;EAAE9C,OAAO,EAAE;AAAC,CAAE,CAAC,C,sCAAa;AACX,IAAA6C,iBAAA,GAAtB,IAAAC,0BAAI,EAAC;EAAE9C,OAAO,EAAE;AAAE,CAAE,CAAC,C,8CAAqB;AAS3C,IAAA6C,iBAAA,GADC,IAAAE,2BAAK,EAAC,cAAc,CAAC,C,wCAGrB;AAdHpC,SAAA,OAAAkC,iBAAA,GAHC,IAAAG,+BAAS,EAAC;EACTC,IAAI,EAAE;CACP,CAAC,C,YAgDD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAnD,OAAA,G", "ignoreList": []}]}