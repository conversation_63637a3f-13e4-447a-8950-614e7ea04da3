{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.replace\");\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/asyncToGenerator.js\"));\nrequire(\"core-js/modules/es6.array.find\");\nvar _toConsumableArray2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/toConsumableArray.js\"));\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.function.name\");\nvar _classCallCheck2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime-corejs2/helpers/inherits.js\"));\nvar _tslib = require(\"tslib\");\nvar _vuePropertyDecorator = require(\"vue-property-decorator\");\nvar _app = require(\"@/store/modules/app\");\nvar _user = require(\"@/store/modules/user\");\nvar _SidebarItem = _interopRequireDefault(require(\"./SidebarItem.vue\"));\nvar _variables = _interopRequireDefault(require(\"@/styles/_variables.scss\"));\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar default_1 = /*#__PURE__*/function (_Vue) {\n  function default_1() {\n    var _this;\n    (0, _classCallCheck2.default)(this, default_1);\n    _this = _callSuper(this, default_1, arguments);\n    _this.restKey = 0;\n    return _this;\n  }\n  (0, _inherits2.default)(default_1, _Vue);\n  return (0, _createClass2.default)(default_1, [{\n    key: \"name\",\n    get: function get() {\n      return _user.UserModule.userInfo.name ? _user.UserModule.userInfo.name : JSON.parse(_jsCookie.default.get('user_info')).name;\n    }\n  }, {\n    key: \"defOpen\",\n    get: function get() {\n      var _this2 = this;\n      // const urlArr = this.$route.path.split('/')\n      // const openStr = urlArr.length > 2 ? `/${urlArr[1]}` : '/'\n      var path = ['/'];\n      this.routes.forEach(function (n, i) {\n        if (n.meta.roles && n.meta.roles[0] === _this2.roles[0]) {\n          path.splice(0, 1, n.path);\n        }\n      });\n      return path;\n    }\n  }, {\n    key: \"defAct\",\n    get: function get() {\n      var path = this.$route.path;\n      return path;\n    }\n  }, {\n    key: \"sidebar\",\n    get: function get() {\n      return _app.AppModule.sidebar;\n    }\n  }, {\n    key: \"roles\",\n    get: function get() {\n      return _user.UserModule.roles;\n    }\n  }, {\n    key: \"routes\",\n    get: function get() {\n      var routes = JSON.parse(JSON.stringify((0, _toConsumableArray2.default)(this.$router.options.routes)));\n      console.log('-=-=routes=-=-=', routes);\n      console.log('-=-=routes=-=-=', this.roles[0]);\n      var menuList = [];\n      var menu = routes.find(function (item) {\n        return item.path === '/';\n      });\n      if (menu) {\n        menuList = menu.children;\n      }\n      console.log('-=-=routes=-wwww=-=', routes);\n      return menuList;\n    }\n  }, {\n    key: \"variables\",\n    get: function get() {\n      return _variables.default;\n    }\n  }, {\n    key: \"isCollapse\",\n    get: function get() {\n      return !this.sidebar.opened;\n    }\n  }, {\n    key: \"logout\",\n    value: function () {\n      var _logout = (0, _asyncToGenerator2.default)(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {\n        var _this3 = this;\n        return regeneratorRuntime.wrap(function (_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              this.$store.dispatch('LogOut').then(function () {\n                // location.href = '/'\n                _this3.$router.replace({\n                  path: '/login'\n                });\n              });\n              // this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n            case 1:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function logout() {\n        return _logout.apply(this, arguments);\n      }\n      return logout;\n    }()\n  }]);\n}(_vuePropertyDecorator.Vue);\ndefault_1 = (0, _tslib.__decorate)([(0, _vuePropertyDecorator.Component)({\n  name: 'SideBar',\n  components: {\n    SidebarItem: _SidebarItem.default\n  }\n})], default_1);\nvar _default = exports.default = default_1;", {"version": 3, "names": ["_vuePropertyDecorator", "require", "_app", "_user", "_SidebarItem", "_interopRequireDefault", "_variables", "_js<PERSON><PERSON>ie", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "default_1", "_Vue", "_this", "_classCallCheck2", "restKey", "_inherits2", "_createClass2", "key", "get", "UserModule", "userInfo", "name", "JSON", "parse", "Cookies", "_this2", "path", "routes", "for<PERSON>ach", "n", "i", "meta", "roles", "splice", "$route", "AppModule", "sidebar", "stringify", "_toConsumableArray2", "$router", "options", "console", "log", "menuList", "menu", "find", "item", "children", "variables", "opened", "value", "_logout", "_asyncToGenerator2", "regeneratorRuntime", "mark", "_callee", "_this3", "wrap", "_context", "prev", "next", "$store", "dispatch", "then", "replace", "stop", "logout", "arguments", "<PERSON><PERSON>", "__decorate", "Component", "components", "SidebarItem", "_default", "exports"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { AppModule } from '@/store/modules/app'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport SidebarItem from './SidebarItem.vue'\r\nimport variables from '@/styles/_variables.scss'\r\nimport { getSidebarStatus, setSidebarStatus } from '@/utils/cookies'\r\nimport Cookies from 'js-cookie'\r\n@Component({\r\n  name: 'SideBar',\r\n  components: {\r\n    SidebarItem\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private restKey: number = 0\r\n  get name() {\r\n    return (UserModule.userInfo as any).name\r\n      ? (UserModule.userInfo as any).name\r\n      : JSON.parse(Cookies.get('user_info') as any).name\r\n  }\r\n  get defOpen() {\r\n    // const urlArr = this.$route.path.split('/')\r\n    // const openStr = urlArr.length > 2 ? `/${urlArr[1]}` : '/'\r\n    let path = ['/']\r\n    this.routes.forEach((n: any, i: number) => {\r\n      if (n.meta.roles && n.meta.roles[0] === this.roles[0]) {\r\n        path.splice(0, 1, n.path)\r\n      }\r\n    })\r\n    return path\r\n  }\r\n\r\n  get defAct() {\r\n    let path = this.$route.path\r\n    return path\r\n  }\r\n\r\n  get sidebar() {\r\n    return AppModule.sidebar\r\n  }\r\n\r\n  get roles() {\r\n    return UserModule.roles\r\n  }\r\n\r\n  get routes() {\r\n    let routes = JSON.parse(\r\n      JSON.stringify([...(this.$router as any).options.routes])\r\n    )\r\n    console.log('-=-=routes=-=-=', routes)\r\n    console.log('-=-=routes=-=-=', this.roles[0])\r\n    let menuList = []\r\n    let menu = routes.find(item => item.path === '/')\r\n    if (menu) {\r\n      menuList = menu.children\r\n    }\r\n    console.log('-=-=routes=-wwww=-=', routes)\r\n    return menuList\r\n  }\r\n\r\n  get variables() {\r\n    return variables\r\n  }\r\n\r\n  get isCollapse() {\r\n    return !this.sidebar.opened\r\n  }\r\n  private async logout() {\r\n    this.$store.dispatch('LogOut').then(() => {\r\n      // location.href = '/'\r\n      this.$router.replace({ path: '/login' })\r\n    })\r\n    // this.$router.push(`/login?redirect=${this.$route.fullPath}`)\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAEA,IAAAM,SAAA,GAAAF,sBAAA,CAAAJ,OAAA;AAA+B,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAO/B,IAAAe,SAAA,0BAAAC,IAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAd,OAAA,QAAAW,SAAA;;IACUE,KAAA,CAAAE,OAAO,GAAW,CAAC;IAAA,OAAAF,KAAA;EA4D7B;EAAC,IAAAG,UAAA,CAAAhB,OAAA,EAAAW,SAAA,EAAAC,IAAA;EAAA,WAAAK,aAAA,CAAAjB,OAAA,EAAAW,SAAA;IAAAO,GAAA;IAAAC,GAAA,EA3DC,SAAAA,IAAA,EAAQ;MACN,OAAQC,gBAAU,CAACC,QAAgB,CAACC,IAAI,GACnCF,gBAAU,CAACC,QAAgB,CAACC,IAAI,GACjCC,IAAI,CAACC,KAAK,CAACC,iBAAO,CAACN,GAAG,CAAC,WAAW,CAAQ,CAAC,CAACG,IAAI;IACtD;EAAC;IAAAJ,GAAA;IAAAC,GAAA,EACD,SAAAA,IAAA,EAAW;MAAA,IAAAO,MAAA;MACT;MACA;MACA,IAAIC,IAAI,GAAG,CAAC,GAAG,CAAC;MAChB,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,UAACC,CAAM,EAAEC,CAAS,EAAI;QACxC,IAAID,CAAC,CAACE,IAAI,CAACC,KAAK,IAAIH,CAAC,CAACE,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,KAAKP,MAAI,CAACO,KAAK,CAAC,CAAC,CAAC,EAAE;UACrDN,IAAI,CAACO,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEJ,CAAC,CAACH,IAAI,CAAC;;MAE7B,CAAC,CAAC;MACF,OAAOA,IAAI;IACb;EAAC;IAAAT,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAU;MACR,IAAIQ,IAAI,GAAG,IAAI,CAACQ,MAAM,CAACR,IAAI;MAC3B,OAAOA,IAAI;IACb;EAAC;IAAAT,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAW;MACT,OAAOiB,cAAS,CAACC,OAAO;IAC1B;EAAC;IAAAnB,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAS;MACP,OAAOC,gBAAU,CAACa,KAAK;IACzB;EAAC;IAAAf,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAU;MACR,IAAIS,MAAM,GAAGL,IAAI,CAACC,KAAK,CACrBD,IAAI,CAACe,SAAS,KAAAC,mBAAA,CAAAvC,OAAA,EAAM,IAAI,CAACwC,OAAe,CAACC,OAAO,CAACb,MAAM,CAAC,CAAC,CAC1D;MACDc,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEf,MAAM,CAAC;MACtCc,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC;MAC7C,IAAIW,QAAQ,GAAG,EAAE;MACjB,IAAIC,IAAI,GAAGjB,MAAM,CAACkB,IAAI,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACpB,IAAI,KAAK,GAAG;MAAA,EAAC;MACjD,IAAIkB,IAAI,EAAE;QACRD,QAAQ,GAAGC,IAAI,CAACG,QAAQ;;MAE1BN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEf,MAAM,CAAC;MAC1C,OAAOgB,QAAQ;IACjB;EAAC;IAAA1B,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAa;MACX,OAAO8B,kBAAS;IAClB;EAAC;IAAA/B,GAAA;IAAAC,GAAA,EAED,SAAAA,IAAA,EAAc;MACZ,OAAO,CAAC,IAAI,CAACkB,OAAO,CAACa,MAAM;IAC7B;EAAC;IAAAhC,GAAA;IAAAiC,KAAA;MAAA,IAAAC,OAAA,OAAAC,kBAAA,CAAArD,OAAA,eAAAsD,kBAAA,CAAAC,IAAA,CACO,SAAAC,QAAA;QAAA,IAAAC,MAAA;QAAA,OAAAH,kBAAA,CAAAI,IAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACN,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,YAAK;gBACvC;gBACAP,MAAI,CAACjB,OAAO,CAACyB,OAAO,CAAC;kBAAEtC,IAAI,EAAE;gBAAQ,CAAE,CAAC;cAC1C,CAAC,CAAC;cACF;YAAA;YAAA;cAAA,OAAAgC,QAAA,CAAAO,IAAA;UAAA;QAAA,GAAAV,OAAA;MAAA,CACD;MAAA,SANaW,MAAMA,CAAA;QAAA,OAAAf,OAAA,CAAA9C,KAAA,OAAA8D,SAAA;MAAA;MAAA,OAAND,MAAM;IAAA;EAAA;AAAA,EAtDOE,yBAAG,CA6D/B;AA7DD1D,SAAA,OAAA2D,iBAAA,GANC,IAAAC,+BAAS,EAAC;EACTjD,IAAI,EAAE,SAAS;EACfkD,UAAU,EAAE;IACVC,WAAW,EAAXA;;CAEH,CAAC,C,YA8DD;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA3E,OAAA,G", "ignoreList": []}]}