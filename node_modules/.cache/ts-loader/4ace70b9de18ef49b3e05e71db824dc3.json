{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/icons/components/dashboard.ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/icons/components/dashboard.ts", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _vueSvgicon = _interopRequireDefault(require(\"vue-svgicon\"));\n/* eslint-disable */\n/* tslint:disable */\n// @ts-ignore\n\n_vueSvgicon.default.register({\n  'dashboard': {\n    width: 32,\n    height: 32,\n    viewBox: '0 0 1024 1024',\n    data: '<path pid=\"4758\" d=\"M888 462.4L544 142.4c-19.2-17.6-48-17.6-65.6 0l-344 320c-9.6 9.6-16 22.4-16 35.2v355.2c0 27.2 22.4 49.6 49.6 49.6h240V657.6c0-56 46.4-102.4 102.4-102.4 56 0 102.4 46.4 102.4 102.4v246.4h240c27.2 0 49.6-22.4 49.6-49.6V497.6c1.6-12.8-4.8-25.6-14.4-35.2z\"/>'\n  }\n});", {"version": 3, "names": ["_vueSvgicon", "_interopRequireDefault", "require", "icon", "register", "width", "height", "viewBox", "data"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/icons/components/dashboard.ts"], "sourcesContent": ["/* eslint-disable */\r\n/* tslint:disable */\r\n// @ts-ignore\r\nimport icon from 'vue-svgicon'\r\nicon.register({\r\n  'dashboard': {\r\n    width: 32,\r\n    height: 32,\r\n    viewBox: '0 0 1024 1024',\r\n    data: '<path pid=\"4758\" d=\"M888 462.4L544 142.4c-19.2-17.6-48-17.6-65.6 0l-344 320c-9.6 9.6-16 22.4-16 35.2v355.2c0 27.2 22.4 49.6 49.6 49.6h240V657.6c0-56 46.4-102.4 102.4-102.4 56 0 102.4 46.4 102.4 102.4v246.4h240c27.2 0 49.6-22.4 49.6-49.6V497.6c1.6-12.8-4.8-25.6-14.4-35.2z\"/>'\r\n  }\r\n})\r\n"], "mappings": ";;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AAEAC,mBAAI,CAACC,QAAQ,CAAC;EACZ,WAAW,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,eAAe;IACxBC,IAAI,EAAE;;CAET,CAAC", "ignoreList": []}]}