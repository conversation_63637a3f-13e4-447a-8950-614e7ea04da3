{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js??ref--14-2!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue", "mtime": 1756362725169}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _default = exports.default = {\n  data: function data(vm) {\n    return {\n      ruleForm: {\n        name: '',\n        username: '',\n        sex: '1',\n        phone: '',\n        idNumber: ''\n      },\n      rules: {\n        name: [{\n          required: true,\n          message: '请输入员工姓名',\n          trigger: 'blur'\n        }],\n        username: [{\n          required: true,\n          message: '请输入员工账号',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          trigger: 'blur',\n          validator: function validator(rule, value, callback) {\n            if (value === '' || !/^1[3456789]\\d{9}$/.test(value)) {\n              callback(new Error('请输入正确的手机号'));\n            } else {\n              callback();\n            }\n          }\n        }]\n      }\n    };\n  }\n};", {"version": 3, "names": ["data", "vm", "ruleForm", "name", "username", "sex", "phone", "idNumber", "rules", "required", "message", "trigger", "validator", "rule", "value", "callback", "test", "Error"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue?vue&type=script&lang=ts"], "sourcesContent": ["\r\nexport default {\r\n  data(vm) {\r\n    return {\r\n      ruleForm: {\r\n        name: '',\r\n        username: '',\r\n        sex: '1',\r\n        phone: '',\r\n        idNumber: '',\r\n      },\r\n      rules: {\r\n        name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],\r\n        username:[\r\n          {\r\n            required: true, message: '请输入员工账号', trigger: 'blur' \r\n          }\r\n        ],\r\n        phone:[\r\n        {\r\n            required: true, trigger: 'blur' ,validator: (rule, value, callback)=>{\r\n              if(value===''||(!/^1[3456789]\\d{9}$/.test(value))){\r\n                callback(new Error('请输入正确的手机号'))\r\n              }else{\r\n                callback()\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n    }\r\n  },\r\n}\r\n"], "mappings": ";;;;;;iCACe;EACbA,IAAI,WAAJA,IAAIA,CAACC,EAAE;IACL,OAAO;MACLC,QAAQ,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;OACX;MACDC,KAAK,EAAE;QACLL,IAAI,EAAE,CAAC;UAAEM,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAE,CAAC;QAC/DP,QAAQ,EAAC,CACP;UACEK,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;SAC9C,CACF;QACDL,KAAK,EAAC,CACN;UACIG,QAAQ,EAAE,IAAI;UAAEE,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE,SAAXA,SAASA,CAAGC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAG;YACnE,IAAGD,KAAK,KAAG,EAAE,IAAG,CAAC,mBAAmB,CAACE,IAAI,CAACF,KAAK,CAAE,EAAC;cAChDC,QAAQ,CAAC,IAAIE,KAAK,CAAC,WAAW,CAAC,CAAC;aACjC,MAAI;cACHF,QAAQ,EAAE;;UAEd;SACD;;KAGN;EACH;CACD", "ignoreList": []}]}