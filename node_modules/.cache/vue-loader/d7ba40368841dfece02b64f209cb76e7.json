{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Hamburger/index.vue?vue&type=style&index=0&id=4e6f274c&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Hamburger/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.svg-icon {\r\n  vertical-align: middle;\r\n}\r\n\r\n.is-active {\r\n  transform: rotate(180deg);\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuBA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Hamburger", "sourcesContent": ["<template>\r\n  <div :class=\"[{'is-active': isActive}]\" @click=\"toggleClick\">\r\n    <svg-icon name=\"hamburger\" width=\"20\" height=\"20\" />\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\n\r\n@Component({\r\n  'name': 'Hamburger'\r\n})\r\n\r\nexport default class extends Vue {\r\n  @Prop({ 'default': false }) private isActive!: boolean\r\n\r\n  private toggleClick() {\r\n    this.$emit('toggleClick');\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.svg-icon {\r\n  vertical-align: middle;\r\n}\r\n\r\n.is-active {\r\n  transform: rotate(180deg);\r\n}\r\n</style>\r\n"]}]}