{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--12-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue?vue&type=template&id=ac903da6", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue", "mtime": 1655864776000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"h2\", {\n    staticClass: \"homeTitle\"\n  }, [_vm._v(\"\\n    今日数据\"), _c(\"i\", [_vm._v(_vm._s(_vm.days[1]))]), _c(\"span\", [_c(\"router-link\", {\n    attrs: {\n      to: \"statistics\"\n    }\n  }, [_vm._v(\"详细数据\")])], 1)]), _c(\"div\", {\n    staticClass: \"overviewBox\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"p\", {\n    staticClass: \"tit\"\n  }, [_vm._v(\"营业额\")]), _c(\"p\", {\n    staticClass: \"num\"\n  }, [_vm._v(\"¥ \" + _vm._s(_vm.overviewData.turnover))])]), _c(\"li\", [_c(\"p\", {\n    staticClass: \"tit\"\n  }, [_vm._v(\"有效订单\")]), _c(\"p\", {\n    staticClass: \"num\"\n  }, [_vm._v(_vm._s(_vm.overviewData.validOrderCount))])]), _c(\"li\", [_c(\"p\", {\n    staticClass: \"tit\"\n  }, [_vm._v(\"订单完成率\")]), _c(\"p\", {\n    staticClass: \"num\"\n  }, [_vm._v(\"\\n          \" + _vm._s((_vm.overviewData.orderCompletionRate * 100).toFixed(0)) + \"%\\n        \")])]), _c(\"li\", [_c(\"p\", {\n    staticClass: \"tit\"\n  }, [_vm._v(\"平均客单价\")]), _c(\"p\", {\n    staticClass: \"num\"\n  }, [_vm._v(\"¥ \" + _vm._s(_vm.overviewData.unitPrice))])]), _c(\"li\", [_c(\"p\", {\n    staticClass: \"tit\"\n  }, [_vm._v(\"新增用户\")]), _c(\"p\", {\n    staticClass: \"num\"\n  }, [_vm._v(_vm._s(_vm.overviewData.newUsers))])])])])]);\n};\nvar staticRenderFns = exports.staticRenderFns = [];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "_v", "_s", "days", "attrs", "to", "overviewData", "turnover", "validOrderCount", "orderCompletionRate", "toFixed", "unitPrice", "newUsers", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\"div\", { staticClass: \"container\" }, [\n    _c(\"h2\", { staticClass: \"homeTitle\" }, [\n      _vm._v(\"\\n    今日数据\"),\n      _c(\"i\", [_vm._v(_vm._s(_vm.days[1]))]),\n      _c(\n        \"span\",\n        [\n          _c(\"router-link\", { attrs: { to: \"statistics\" } }, [\n            _vm._v(\"详细数据\"),\n          ]),\n        ],\n        1\n      ),\n    ]),\n    _c(\"div\", { staticClass: \"overviewBox\" }, [\n      _c(\"ul\", [\n        _c(\"li\", [\n          _c(\"p\", { staticClass: \"tit\" }, [_vm._v(\"营业额\")]),\n          _c(\"p\", { staticClass: \"num\" }, [\n            _vm._v(\"¥ \" + _vm._s(_vm.overviewData.turnover)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"p\", { staticClass: \"tit\" }, [_vm._v(\"有效订单\")]),\n          _c(\"p\", { staticClass: \"num\" }, [\n            _vm._v(_vm._s(_vm.overviewData.validOrderCount)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"p\", { staticClass: \"tit\" }, [_vm._v(\"订单完成率\")]),\n          _c(\"p\", { staticClass: \"num\" }, [\n            _vm._v(\n              \"\\n          \" +\n                _vm._s(\n                  (_vm.overviewData.orderCompletionRate * 100).toFixed(0)\n                ) +\n                \"%\\n        \"\n            ),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"p\", { staticClass: \"tit\" }, [_vm._v(\"平均客单价\")]),\n          _c(\"p\", { staticClass: \"num\" }, [\n            _vm._v(\"¥ \" + _vm._s(_vm.overviewData.unitPrice)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"p\", { staticClass: \"tit\" }, [_vm._v(\"新增用户\")]),\n          _c(\"p\", { staticClass: \"num\" }, [\n            _vm._v(_vm._s(_vm.overviewData.newUsers)),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CAC7CJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCL,GAAG,CAACM,EAAE,CAAC,YAAY,CAAC,EACpBL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACtCP,EAAE,CACA,MAAM,EACN,CACEA,EAAE,CAAC,aAAa,EAAE;IAAEQ,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAa;EAAE,CAAC,EAAE,CACjDV,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAChDL,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAC9BL,GAAG,CAACM,EAAE,CAAC,IAAI,GAAGN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACW,YAAY,CAACC,QAAQ,CAAC,CAAC,CACjD,CAAC,CACH,CAAC,EACFX,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACjDL,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAC9BL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACW,YAAY,CAACE,eAAe,CAAC,CAAC,CACjD,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAClDL,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAC9BL,GAAG,CAACM,EAAE,CACJ,cAAc,GACZN,GAAG,CAACO,EAAE,CACJ,CAACP,GAAG,CAACW,YAAY,CAACG,mBAAmB,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CACxD,CAAC,GACD,aACJ,CAAC,CACF,CAAC,CACH,CAAC,EACFd,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAClDL,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAC9BL,GAAG,CAACM,EAAE,CAAC,IAAI,GAAGN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACW,YAAY,CAACK,SAAS,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFf,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACjDL,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAC9BL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACW,YAAY,CAACM,QAAQ,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAAnB,OAAA,CAAAmB,eAAA,GAAG,EAAE;AACxBpB,MAAM,CAACqB,aAAa,GAAG,IAAI", "ignoreList": []}]}