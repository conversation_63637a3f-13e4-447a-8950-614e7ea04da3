{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue?vue&type=template&id=89f292fa", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"container\">\n  <h2 class=\"homeTitle\">\n    菜品总览<span><router-link to=\"dish\">菜品管理</router-link></span>\n  </h2>\n  <div class=\"orderviewBox\">\n    <ul>\n      <li>\n        <span class=\"status\"><i class=\"iconfont icon-open\"></i>已启售</span>\n        <span class=\"num\">{{ dishesData.sold }}</span>\n      </li>\n      <li>\n        <span class=\"status\"><i class=\"iconfont icon-stop\"></i>已停售</span>\n        <span class=\"num\">{{ dishesData.discontinued }}</span>\n      </li>\n      <li class=\"add\">\n        <router-link to=\"/dish/add\">\n          <i></i>\n          <p>新增菜品</p>\n        </router-link>\n      </li>\n    </ul>\n  </div>\n</div>\n", null]}