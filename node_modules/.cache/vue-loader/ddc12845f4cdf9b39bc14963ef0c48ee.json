{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/orderStatistics.vue?vue&type=template&id=45742eda", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/orderStatistics.vue", "mtime": 1656314117000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"container\">\n  <h2 class=\"homeTitle\">订单统计</h2>\n  <div class=\"charBox\">\n    <div class=\"orderProportion\">\n      <div>\n        <p>订单完成率</p>\n        <p>{{ (orderdata.orderCompletionRate * 100).toFixed(1) }}%</p>\n      </div>\n      <div class=\"symbol\">=</div>\n      <div>\n        <p>有效订单</p>\n        <p>{{ orderdata.validOrderCount }}</p>\n      </div>\n      <div class=\"symbol\">/</div>\n      <div>\n        <p>订单总数</p>\n        <p>{{ orderdata.totalOrderCount }}</p>\n      </div>\n    </div>\n    <div id=\"ordermain\" style=\"width: 100%; height: 300px\"></div>\n    <ul class=\"orderListLine\">\n      <li class=\"one\"><span></span>订单总数（个）</li>\n      <li class=\"three\"><span></span>有效订单（个）</li>\n    </ul>\n  </div>\n</div>\n", null]}