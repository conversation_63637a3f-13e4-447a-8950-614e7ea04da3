{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/addSetmeal.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/addSetmeal.vue", "mtime": 1695192173000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Vue } from 'vue-property-decorator'\r\nimport HeadLable from '@/components/HeadLable/index.vue'\r\nimport ImageUpload from '@/components/ImgUpload/index.vue'\r\nimport AddDish from './components/AddDish.vue'\r\nimport { querySetmealById, addSetmeal, editSetmeal } from '@/api/setMeal'\r\nimport { getCategoryList } from '@/api/dish'\r\nimport { baseUrl } from '@/config.json'\r\n\r\n@Component({\r\n  name: 'addShop',\r\n  components: {\r\n    HeadLable,\r\n    AddDish,\r\n    ImageUpload\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private value: string = ''\r\n  private setMealList: [] = []\r\n  private seachKey: string = ''\r\n  private dishList: [] = []\r\n  private imageUrl: string = ''\r\n  private actionType: string = ''\r\n  private dishTable: [] = []\r\n  private dialogVisible: boolean = false\r\n  private checkList: any[] = []\r\n  private ruleForm = {\r\n    name: '',\r\n    categoryId: '',\r\n    price: '',\r\n    code: '',\r\n    image: '',\r\n    description: '',\r\n    dishList: [],\r\n    status: true,\r\n    idType: ''\r\n  }\r\n\r\n  get rules() {\r\n    return {\r\n      name: {\r\n        required: true,\r\n        validator: (rule: any, value: string, callback: Function) => {\r\n          if (!value) {\r\n            callback(new Error('请输入套餐名称'))\r\n          } else {\r\n            const reg = /^([A-Za-z0-9\\u4e00-\\u9fa5]){2,20}$/\r\n            if (!reg.test(value)) {\r\n              callback(new Error('套餐名称输入不符，请输入2-20个字符'))\r\n            } else {\r\n              callback()\r\n            }\r\n          }\r\n        },\r\n        trigger: 'blur'\r\n      },\r\n      idType: {\r\n        required: true,\r\n        message: '请选择套餐分类',\r\n        trigger: 'change'\r\n      },\r\n      image: {\r\n        required: true,\r\n        message: '菜品图片不能为空'\r\n      },\r\n      price: {\r\n        required: true,\r\n        // 'message': '请输入套餐价格',\r\n        validator: (rules: any, value: string, callback: Function) => {\r\n          const reg = /^([1-9]\\d{0,5}|0)(\\.\\d{1,2})?$/\r\n          if (!reg.test(value) || Number(value) <= 0) {\r\n            callback(\r\n              new Error(\r\n                '套餐价格格式有误，请输入大于零且最多保留两位小数的金额'\r\n              )\r\n            )\r\n          } else {\r\n            callback()\r\n          }\r\n        },\r\n        trigger: 'blur'\r\n      },\r\n      code: { required: true, message: '请输入商品码', trigger: 'blur' }\r\n    }\r\n  }\r\n\r\n  created() {\r\n    this.getDishTypeList()\r\n    this.actionType = this.$route.query.id ? 'edit' : 'add'\r\n    if (this.actionType == 'edit') {\r\n      this.init()\r\n    }\r\n  }\r\n\r\n  private async init() {\r\n    querySetmealById(this.$route.query.id).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.ruleForm = res.data.data\r\n        this.ruleForm.status = res.data.data.status == '1'\r\n        ;(this.ruleForm as any).price = res.data.data.price\r\n        // this.imageUrl = `http://172.17.2.120:8080/common/download?name=${res.data.data.image}`\r\n        this.imageUrl = res.data.data.image\r\n        this.checkList = res.data.data.setmealDishes\r\n        this.dishTable = res.data.data.setmealDishes.reverse()\r\n        this.ruleForm.idType = res.data.data.categoryId\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n  private seachHandle() {\r\n    this.seachKey = this.value\r\n  }\r\n  // 获取套餐分类\r\n  private getDishTypeList() {\r\n    getCategoryList({ type: 2, page: 1, pageSize: 1000 }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.setMealList = res.data.data.map((obj: any) => ({\r\n          ...obj,\r\n          idType: obj.id\r\n        }))\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n\r\n  // 通过套餐ID获取菜品列表分类\r\n  // private getDishList (id:number) {\r\n  //   getDishListType({id}).then(res => {\r\n  //     if (res.data.code == 200) {\r\n  //       const { data } = res.data\r\n  //       this.dishList = data\r\n  //     } else {\r\n  //       this.$message.error(res.data.desc)\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  // 删除套餐菜品\r\n  delDishHandle(index: any) {\r\n    this.dishTable.splice(index, 1)\r\n    this.checkList = this.dishTable\r\n    // this.checkList.splice(index, 1)\r\n  }\r\n\r\n  // 获取添加菜品数据 - 确定加菜倒序展示\r\n  private getCheckList(value: any) {\r\n    this.checkList = [...value].reverse()\r\n  }\r\n\r\n  // 添加菜品\r\n  openAddDish(st: string) {\r\n    this.seachKey = ''\r\n    this.dialogVisible = true\r\n  }\r\n  // 取消添加菜品\r\n  handleClose(done: any) {\r\n    // this.$refs.adddish.close()\r\n    this.dialogVisible = false\r\n    this.checkList = JSON.parse(JSON.stringify(this.dishTable))\r\n    // this.dialogVisible = false\r\n  }\r\n\r\n  // 保存添加菜品列表\r\n  public addTableList() {\r\n    this.dishTable = JSON.parse(JSON.stringify(this.checkList))\r\n    this.dishTable.forEach((n: any) => {\r\n      n.copies = 1\r\n    })\r\n    this.dialogVisible = false\r\n  }\r\n\r\n  public submitForm(formName: any, st: any) {\r\n    ;(this.$refs[formName] as any).validate((valid: any) => {\r\n      if (valid) {\r\n        if (this.dishTable.length === 0) {\r\n          return this.$message.error('套餐下菜品不能为空')\r\n        }\r\n        if (!this.ruleForm.image) return this.$message.error('套餐图片不能为空')\r\n        let prams = { ...this.ruleForm } as any\r\n        prams.setmealDishes = this.dishTable.map((obj: any) => ({\r\n          copies: obj.copies,\r\n          dishId: obj.dishId,\r\n          name: obj.name,\r\n          price: obj.price\r\n        }))\r\n        ;(prams as any).status =\r\n          this.actionType === 'add' ? 0 : this.ruleForm.status ? 1 : 0\r\n        prams.categoryId = this.ruleForm.idType\r\n        // delete prams.dishList\r\n        if (this.actionType == 'add') {\r\n          delete prams.id\r\n          addSetmeal(prams)\r\n            .then(res => {\r\n              if (res && res.data && res.data.code === 1) {\r\n                this.$message.success('套餐添加成功！')\r\n                if (!st) {\r\n                  this.$router.push({ path: '/setmeal' })\r\n                } else {\r\n                  ;(this as any).$refs.ruleForm.resetFields()\r\n                  this.dishList = []\r\n                  this.dishTable = []\r\n                  this.ruleForm = {\r\n                    name: '',\r\n                    categoryId: '',\r\n                    price: '',\r\n                    code: '',\r\n                    image: '',\r\n                    description: '',\r\n                    dishList: [],\r\n                    status: true,\r\n                    id: '',\r\n                    idType: ''\r\n                  } as any\r\n                  this.imageUrl = ''\r\n                }\r\n              } else {\r\n                this.$message.error(res.data.msg)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        } else {\r\n          delete prams.updateTime\r\n          editSetmeal(prams)\r\n            .then(res => {\r\n              if (res.data.code === 1) {\r\n                this.$message.success('套餐修改成功！')\r\n                this.$router.push({ path: '/setmeal' })\r\n              } else {\r\n                // this.$message.error(res.data.desc || res.data.message)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        }\r\n      } else {\r\n        // console.log('error submit!!')\r\n        return false\r\n      }\r\n    })\r\n  }\r\n\r\n  imageChange(value: any) {\r\n    this.ruleForm.image = value\r\n  }\r\n}\r\n", {"version": 3, "sources": ["addSetmeal.vue"], "names": [], "mappings": ";AA8JA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "addSetmeal.vue", "sourceRoot": "src/views/setmeal", "sourcesContent": ["<template>\r\n  <div class=\"addBrand-container\">\r\n    <div class=\"container\">\r\n      <el-form ref=\"ruleForm\"\r\n               :model=\"ruleForm\"\r\n               :rules=\"rules\"\r\n               :inline=\"true\"\r\n               label-width=\"180px\"\r\n               class=\"demo-ruleForm\">\r\n        <div>\r\n          <el-form-item label=\"套餐名称:\"\r\n                        prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"\r\n                      placeholder=\"请填写套餐名称\"\r\n                      maxlength=\"14\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"套餐分类:\"\r\n                        prop=\"idType\">\r\n            <el-select v-model=\"ruleForm.idType\"\r\n                       placeholder=\"请选择套餐分类\"\r\n                       @change=\"$forceUpdate()\">\r\n              <el-option v-for=\"(item, index) in setMealList\"\r\n                         :key=\"index\"\r\n                         :label=\"item.name\"\r\n                         :value=\"item.id\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </div>\r\n        <div>\r\n          <el-form-item label=\"套餐价格:\"\r\n                        prop=\"price\">\r\n            <el-input v-model=\"ruleForm.price\"\r\n                      placeholder=\"请设置套餐价格\" />\r\n          </el-form-item>\r\n        </div>\r\n        <div>\r\n          <el-form-item label=\"套餐菜品:\"\r\n                        required>\r\n            <el-form-item>\r\n              <div class=\"addDish\">\r\n                <span v-if=\"dishTable.length == 0\"\r\n                      class=\"addBut\"\r\n                      @click=\"openAddDish('new')\">\r\n                  + 添加菜品</span>\r\n                <div v-if=\"dishTable.length != 0\"\r\n                     class=\"content\">\r\n                  <div class=\"addBut\"\r\n                       style=\"margin-bottom: 20px\"\r\n                       @click=\"openAddDish('change')\">\r\n                    + 添加菜品\r\n                  </div>\r\n                  <div class=\"table\">\r\n                    <el-table :data=\"dishTable\"\r\n                              style=\"width: 100%\">\r\n                      <el-table-column prop=\"name\"\r\n                                       label=\"名称\"\r\n                                       width=\"180\"\r\n                                       align=\"center\" />\r\n                      <el-table-column prop=\"price\"\r\n                                       label=\"原价\"\r\n                                       width=\"180\"\r\n                                       align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                          {{ (Number(scope.row.price).toFixed(2) * 100) / 100 }}\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column prop=\"address\"\r\n                                       label=\"份数\"\r\n                                       align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                          <el-input-number v-model=\"scope.row.copies\"\r\n                                           size=\"small\"\r\n                                           :min=\"1\"\r\n                                           :max=\"99\"\r\n                                           label=\"描述文字\" />\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column prop=\"address\"\r\n                                       label=\"操作\"\r\n                                       width=\"180px;\"\r\n                                       align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                          <el-button type=\"text\"\r\n                                     size=\"small\"\r\n                                     class=\"delBut non\"\r\n                                     @click=\"delDishHandle(scope.$index)\">\r\n                            删除\r\n                          </el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-form-item>\r\n        </div>\r\n        <div>\r\n          <el-form-item label=\"套餐图片:\"\r\n                        required\r\n                        prop=\"image\">\r\n            <image-upload :prop-image-url=\"imageUrl\"\r\n                          @imageChange=\"imageChange\">\r\n              图片大小不超过2M<br>仅能上传 PNG JPEG JPG类型图片<br>建议上传200*200或300*300尺寸的图片\r\n            </image-upload>\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"address\">\r\n          <el-form-item label=\"套餐描述:\">\r\n            <el-input v-model=\"ruleForm.description\"\r\n                      type=\"textarea\"\r\n                      :rows=\"3\"\r\n                      maxlength=\"200\"\r\n                      placeholder=\"套餐描述，最长200字\" />\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"subBox address\">\r\n          <el-form-item>\r\n            <el-button @click=\"() => $router.back()\">\r\n              取消\r\n            </el-button>\r\n            <el-button type=\"primary\"\r\n                       :class=\"{ continue: actionType === 'add' }\"\r\n                       @click=\"submitForm('ruleForm', false)\">\r\n              保存\r\n            </el-button>\r\n            <el-button v-if=\"actionType == 'add'\"\r\n                       type=\"primary\"\r\n                       @click=\"submitForm('ruleForm', true)\">\r\n              保存并继续添加\r\n            </el-button>\r\n          </el-form-item>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <el-dialog v-if=\"dialogVisible\"\r\n               title=\"添加菜品\"\r\n               class=\"addDishList\"\r\n               :visible.sync=\"dialogVisible\"\r\n               width=\"60%\"\r\n               :before-close=\"handleClose\">\r\n      <AddDish v-if=\"dialogVisible\"\r\n               ref=\"adddish\"\r\n               :check-list=\"checkList\"\r\n               :seach-key=\"seachKey\"\r\n               :dish-list=\"dishList\"\r\n               @checkList=\"getCheckList\" />\r\n      <span slot=\"footer\"\r\n            class=\"dialog-footer\">\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n        <el-button type=\"primary\"\r\n                   @click=\"addTableList\">添 加</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue } from 'vue-property-decorator'\r\nimport HeadLable from '@/components/HeadLable/index.vue'\r\nimport ImageUpload from '@/components/ImgUpload/index.vue'\r\nimport AddDish from './components/AddDish.vue'\r\nimport { querySetmealById, addSetmeal, editSetmeal } from '@/api/setMeal'\r\nimport { getCategoryList } from '@/api/dish'\r\nimport { baseUrl } from '@/config.json'\r\n\r\n@Component({\r\n  name: 'addShop',\r\n  components: {\r\n    HeadLable,\r\n    AddDish,\r\n    ImageUpload\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private value: string = ''\r\n  private setMealList: [] = []\r\n  private seachKey: string = ''\r\n  private dishList: [] = []\r\n  private imageUrl: string = ''\r\n  private actionType: string = ''\r\n  private dishTable: [] = []\r\n  private dialogVisible: boolean = false\r\n  private checkList: any[] = []\r\n  private ruleForm = {\r\n    name: '',\r\n    categoryId: '',\r\n    price: '',\r\n    code: '',\r\n    image: '',\r\n    description: '',\r\n    dishList: [],\r\n    status: true,\r\n    idType: ''\r\n  }\r\n\r\n  get rules() {\r\n    return {\r\n      name: {\r\n        required: true,\r\n        validator: (rule: any, value: string, callback: Function) => {\r\n          if (!value) {\r\n            callback(new Error('请输入套餐名称'))\r\n          } else {\r\n            const reg = /^([A-Za-z0-9\\u4e00-\\u9fa5]){2,20}$/\r\n            if (!reg.test(value)) {\r\n              callback(new Error('套餐名称输入不符，请输入2-20个字符'))\r\n            } else {\r\n              callback()\r\n            }\r\n          }\r\n        },\r\n        trigger: 'blur'\r\n      },\r\n      idType: {\r\n        required: true,\r\n        message: '请选择套餐分类',\r\n        trigger: 'change'\r\n      },\r\n      image: {\r\n        required: true,\r\n        message: '菜品图片不能为空'\r\n      },\r\n      price: {\r\n        required: true,\r\n        // 'message': '请输入套餐价格',\r\n        validator: (rules: any, value: string, callback: Function) => {\r\n          const reg = /^([1-9]\\d{0,5}|0)(\\.\\d{1,2})?$/\r\n          if (!reg.test(value) || Number(value) <= 0) {\r\n            callback(\r\n              new Error(\r\n                '套餐价格格式有误，请输入大于零且最多保留两位小数的金额'\r\n              )\r\n            )\r\n          } else {\r\n            callback()\r\n          }\r\n        },\r\n        trigger: 'blur'\r\n      },\r\n      code: { required: true, message: '请输入商品码', trigger: 'blur' }\r\n    }\r\n  }\r\n\r\n  created() {\r\n    this.getDishTypeList()\r\n    this.actionType = this.$route.query.id ? 'edit' : 'add'\r\n    if (this.actionType == 'edit') {\r\n      this.init()\r\n    }\r\n  }\r\n\r\n  private async init() {\r\n    querySetmealById(this.$route.query.id).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.ruleForm = res.data.data\r\n        this.ruleForm.status = res.data.data.status == '1'\r\n        ;(this.ruleForm as any).price = res.data.data.price\r\n        // this.imageUrl = `http://172.17.2.120:8080/common/download?name=${res.data.data.image}`\r\n        this.imageUrl = res.data.data.image\r\n        this.checkList = res.data.data.setmealDishes\r\n        this.dishTable = res.data.data.setmealDishes.reverse()\r\n        this.ruleForm.idType = res.data.data.categoryId\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n  private seachHandle() {\r\n    this.seachKey = this.value\r\n  }\r\n  // 获取套餐分类\r\n  private getDishTypeList() {\r\n    getCategoryList({ type: 2, page: 1, pageSize: 1000 }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.setMealList = res.data.data.map((obj: any) => ({\r\n          ...obj,\r\n          idType: obj.id\r\n        }))\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n\r\n  // 通过套餐ID获取菜品列表分类\r\n  // private getDishList (id:number) {\r\n  //   getDishListType({id}).then(res => {\r\n  //     if (res.data.code == 200) {\r\n  //       const { data } = res.data\r\n  //       this.dishList = data\r\n  //     } else {\r\n  //       this.$message.error(res.data.desc)\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  // 删除套餐菜品\r\n  delDishHandle(index: any) {\r\n    this.dishTable.splice(index, 1)\r\n    this.checkList = this.dishTable\r\n    // this.checkList.splice(index, 1)\r\n  }\r\n\r\n  // 获取添加菜品数据 - 确定加菜倒序展示\r\n  private getCheckList(value: any) {\r\n    this.checkList = [...value].reverse()\r\n  }\r\n\r\n  // 添加菜品\r\n  openAddDish(st: string) {\r\n    this.seachKey = ''\r\n    this.dialogVisible = true\r\n  }\r\n  // 取消添加菜品\r\n  handleClose(done: any) {\r\n    // this.$refs.adddish.close()\r\n    this.dialogVisible = false\r\n    this.checkList = JSON.parse(JSON.stringify(this.dishTable))\r\n    // this.dialogVisible = false\r\n  }\r\n\r\n  // 保存添加菜品列表\r\n  public addTableList() {\r\n    this.dishTable = JSON.parse(JSON.stringify(this.checkList))\r\n    this.dishTable.forEach((n: any) => {\r\n      n.copies = 1\r\n    })\r\n    this.dialogVisible = false\r\n  }\r\n\r\n  public submitForm(formName: any, st: any) {\r\n    ;(this.$refs[formName] as any).validate((valid: any) => {\r\n      if (valid) {\r\n        if (this.dishTable.length === 0) {\r\n          return this.$message.error('套餐下菜品不能为空')\r\n        }\r\n        if (!this.ruleForm.image) return this.$message.error('套餐图片不能为空')\r\n        let prams = { ...this.ruleForm } as any\r\n        prams.setmealDishes = this.dishTable.map((obj: any) => ({\r\n          copies: obj.copies,\r\n          dishId: obj.dishId,\r\n          name: obj.name,\r\n          price: obj.price\r\n        }))\r\n        ;(prams as any).status =\r\n          this.actionType === 'add' ? 0 : this.ruleForm.status ? 1 : 0\r\n        prams.categoryId = this.ruleForm.idType\r\n        // delete prams.dishList\r\n        if (this.actionType == 'add') {\r\n          delete prams.id\r\n          addSetmeal(prams)\r\n            .then(res => {\r\n              if (res && res.data && res.data.code === 1) {\r\n                this.$message.success('套餐添加成功！')\r\n                if (!st) {\r\n                  this.$router.push({ path: '/setmeal' })\r\n                } else {\r\n                  ;(this as any).$refs.ruleForm.resetFields()\r\n                  this.dishList = []\r\n                  this.dishTable = []\r\n                  this.ruleForm = {\r\n                    name: '',\r\n                    categoryId: '',\r\n                    price: '',\r\n                    code: '',\r\n                    image: '',\r\n                    description: '',\r\n                    dishList: [],\r\n                    status: true,\r\n                    id: '',\r\n                    idType: ''\r\n                  } as any\r\n                  this.imageUrl = ''\r\n                }\r\n              } else {\r\n                this.$message.error(res.data.msg)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        } else {\r\n          delete prams.updateTime\r\n          editSetmeal(prams)\r\n            .then(res => {\r\n              if (res.data.code === 1) {\r\n                this.$message.success('套餐修改成功！')\r\n                this.$router.push({ path: '/setmeal' })\r\n              } else {\r\n                // this.$message.error(res.data.desc || res.data.message)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        }\r\n      } else {\r\n        // console.log('error submit!!')\r\n        return false\r\n      }\r\n    })\r\n  }\r\n\r\n  imageChange(value: any) {\r\n    this.ruleForm.image = value\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.avatar-uploader .el-icon-plus:after {\r\n  position: absolute;\r\n  display: inline-block;\r\n  content: ' ' !important;\r\n  left: calc(50% - 20px);\r\n  top: calc(50% - 40px);\r\n  width: 40px;\r\n  height: 40px;\r\n  background: url('./../../assets/icons/<EMAIL>') center center\r\n    no-repeat;\r\n  background-size: 20px;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n// .el-form-item__error {\r\n//   top: 90%;\r\n// }\r\n.addBrand-container {\r\n  .avatar-uploader .el-upload {\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .avatar-uploader .el-upload:hover {\r\n    border-color: #ffc200;\r\n  }\r\n\r\n  .avatar-uploader-icon {\r\n    font-size: 28px;\r\n    color: #8c939d;\r\n    width: 200px;\r\n    height: 160px;\r\n    line-height: 160px;\r\n    text-align: center;\r\n  }\r\n\r\n  .avatar {\r\n    width: 200px;\r\n    height: 160px;\r\n    display: block;\r\n  }\r\n\r\n  // .el-form--inline .el-form-item__content {\r\n  //   width: 293px;\r\n  // }\r\n\r\n  .el-input {\r\n    width: 293px;\r\n  }\r\n\r\n  .address {\r\n    .el-form-item__content {\r\n      width: 777px !important;\r\n    }\r\n  }\r\n  .el-input__prefix {\r\n    top: 2px;\r\n  }\r\n\r\n  .addDish {\r\n    .el-input {\r\n      width: 130px;\r\n    }\r\n\r\n    .el-input-number__increase {\r\n      border-left: solid 1px #fbe396;\r\n      background: #fffbf0;\r\n    }\r\n\r\n    .el-input-number__decrease {\r\n      border-right: solid 1px #fbe396;\r\n      background: #fffbf0;\r\n    }\r\n\r\n    input {\r\n      border: 1px solid #fbe396;\r\n    }\r\n\r\n    .table {\r\n      border: solid 1px #ebeef5;\r\n      border-radius: 3px;\r\n\r\n      th {\r\n        padding: 5px 0;\r\n      }\r\n\r\n      td {\r\n        padding: 7px 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .addDishList {\r\n    .seachDish {\r\n      position: absolute;\r\n      top: 12px;\r\n      right: 20px;\r\n    }\r\n\r\n    .el-dialog__footer {\r\n      padding-top: 27px;\r\n    }\r\n\r\n    .el-dialog__body {\r\n      padding: 0;\r\n      border-bottom: solid 1px #efefef;\r\n    }\r\n    .seachDish {\r\n      .el-input__inner {\r\n        height: 40px;\r\n        line-height: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.addBrand {\r\n  &-container {\r\n    margin: 30px;\r\n\r\n    .container {\r\n      position: relative;\r\n      z-index: 1;\r\n      background: #fff;\r\n      padding: 30px;\r\n      border-radius: 4px;\r\n      min-height: 500px;\r\n\r\n      .subBox {\r\n        padding-top: 30px;\r\n        text-align: center;\r\n        border-top: solid 1px $gray-5;\r\n      }\r\n      .el-input {\r\n        width: 350px;\r\n      }\r\n      .addDish {\r\n        width: 777px;\r\n\r\n        .addBut {\r\n          background: #ffc200;\r\n          display: inline-block;\r\n          padding: 0px 20px;\r\n          border-radius: 3px;\r\n          line-height: 40px;\r\n          cursor: pointer;\r\n          border-radius: 4px;\r\n          color: #333333;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .content {\r\n          background: #fafafb;\r\n          padding: 20px;\r\n          border: solid 1px #d8dde3;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}