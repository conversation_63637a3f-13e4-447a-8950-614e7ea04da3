{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--12-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue?vue&type=template&id=89f292fa", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"h2\", {\n    staticClass: \"homeTitle\"\n  }, [_vm._v(\"\\n    菜品总览\"), _c(\"span\", [_c(\"router-link\", {\n    attrs: {\n      to: \"dish\"\n    }\n  }, [_vm._v(\"菜品管理\")])], 1)]), _c(\"div\", {\n    staticClass: \"orderviewBox\"\n  }, [_c(\"ul\", [_c(\"li\", [_vm._m(0), _c(\"span\", {\n    staticClass: \"num\"\n  }, [_vm._v(_vm._s(_vm.dishesData.sold))])]), _c(\"li\", [_vm._m(1), _c(\"span\", {\n    staticClass: \"num\"\n  }, [_vm._v(_vm._s(_vm.dishesData.discontinued))])]), _c(\"li\", {\n    staticClass: \"add\"\n  }, [_c(\"router-link\", {\n    attrs: {\n      to: \"/dish/add\"\n    }\n  }, [_c(\"i\"), _c(\"p\", [_vm._v(\"新增菜品\")])])], 1)])])]);\n};\nvar staticRenderFns = exports.staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"span\", {\n    staticClass: \"status\"\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-open\"\n  }), _vm._v(\"已启售\")]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"span\", {\n    staticClass: \"status\"\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-stop\"\n  }), _vm._v(\"已停售\")]);\n}];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "_v", "attrs", "to", "_m", "_s", "dishesData", "sold", "discontinued", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/cuisineStatistics.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\"div\", { staticClass: \"container\" }, [\n    _c(\"h2\", { staticClass: \"homeTitle\" }, [\n      _vm._v(\"\\n    菜品总览\"),\n      _c(\n        \"span\",\n        [_c(\"router-link\", { attrs: { to: \"dish\" } }, [_vm._v(\"菜品管理\")])],\n        1\n      ),\n    ]),\n    _c(\"div\", { staticClass: \"orderviewBox\" }, [\n      _c(\"ul\", [\n        _c(\"li\", [\n          _vm._m(0),\n          _c(\"span\", { staticClass: \"num\" }, [\n            _vm._v(_vm._s(_vm.dishesData.sold)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _vm._m(1),\n          _c(\"span\", { staticClass: \"num\" }, [\n            _vm._v(_vm._s(_vm.dishesData.discontinued)),\n          ]),\n        ]),\n        _c(\n          \"li\",\n          { staticClass: \"add\" },\n          [\n            _c(\"router-link\", { attrs: { to: \"/dish/add\" } }, [\n              _c(\"i\"),\n              _c(\"p\", [_vm._v(\"新增菜品\")]),\n            ]),\n          ],\n          1\n        ),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c,\n      _setup = _vm._self._setupProxy\n    return _c(\"span\", { staticClass: \"status\" }, [\n      _c(\"i\", { staticClass: \"iconfont icon-open\" }),\n      _vm._v(\"已启售\"),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c,\n      _setup = _vm._self._setupProxy\n    return _c(\"span\", { staticClass: \"status\" }, [\n      _c(\"i\", { staticClass: \"iconfont icon-stop\" }),\n      _vm._v(\"已停售\"),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CAC7CJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCL,GAAG,CAACM,EAAE,CAAC,YAAY,CAAC,EACpBL,EAAE,CACA,MAAM,EACN,CAACA,EAAE,CAAC,aAAa,EAAE;IAAEM,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAO;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAChE,CACF,CAAC,CACF,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC,EACTR,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CACjCL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,UAAU,CAACC,IAAI,CAAC,CAAC,CACpC,CAAC,CACH,CAAC,EACFX,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC,EACTR,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CACjCL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,UAAU,CAACE,YAAY,CAAC,CAAC,CAC5C,CAAC,CACH,CAAC,EACFZ,EAAE,CACA,IAAI,EACJ;IAAEI,WAAW,EAAE;EAAM,CAAC,EACtB,CACEJ,EAAE,CAAC,aAAa,EAAE;IAAEM,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAY;EAAE,CAAC,EAAE,CAChDP,EAAE,CAAC,GAAG,CAAC,EACPA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIQ,eAAe,GAAAf,OAAA,CAAAe,eAAA,GAAG,CACpB,YAAY;EACV,IAAId,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CAC3CJ,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CL,GAAG,CAACM,EAAE,CAAC,KAAK,CAAC,CACd,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIN,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CAC3CJ,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CL,GAAG,CAACM,EAAE,CAAC,KAAK,CAAC,CACd,CAAC;AACJ,CAAC,CACF;AACDR,MAAM,CAACiB,aAAa,GAAG,IAAI", "ignoreList": []}]}