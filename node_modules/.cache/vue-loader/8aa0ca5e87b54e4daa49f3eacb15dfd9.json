{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/HeadLable/index.vue", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/HeadLable/index.vue", "mtime": 1691561690000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=1496266e&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=ts\"\nexport * from \"./index.vue?vue&type=script&lang=ts\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1496266e&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1496266e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('1496266e')) {\n      api.createRecord('1496266e', component.options)\n    } else {\n      api.reload('1496266e', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=1496266e&scoped=true\", function () {\n      api.rerender('1496266e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/HeadLable/index.vue\"\nexport default component.exports"]}