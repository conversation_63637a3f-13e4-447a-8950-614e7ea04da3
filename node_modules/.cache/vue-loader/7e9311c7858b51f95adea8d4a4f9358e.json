{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Empty/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Empty/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Vue, Component, Prop } from 'vue-property-decorator'\r\n@Component({\r\n  name: 'Empty'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: false }) isSearch: boolean //用来区分是搜索还是默认无数据\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAeA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Empty", "sourcesContent": ["<!--  -->\r\n<template>\r\n  <div class=\"empty-box\">\r\n    <div class=\"img-box\">\r\n      <img v-if=\"!isSearch\"\r\n           src=\"../../assets/table_empty.png\"\r\n           alt=\"\">\r\n      <img v-else\r\n           src=\"../../assets/search_table_empty.png\">\r\n      <p>{{ !isSearch ? '这里空空如也~' : 'Sorry，木有找到您搜索的内容哦~' }}</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang='ts'>\r\nimport { Vue, Component, Prop } from 'vue-property-decorator'\r\n@Component({\r\n  name: 'Empty'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: false }) isSearch: boolean //用来区分是搜索还是默认无数据\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.empty-box {\r\n  text-align: center;\r\n  margin: 120px 0;\r\n  img {\r\n    margin: 0 atuo;\r\n    width: 238px;\r\n    height: 184px;\r\n    margin-top: 156px;\r\n    margin-bottom: 26px;\r\n  }\r\n  p {\r\n    color: #818693;\r\n  }\r\n}\r\n/* @import url(); 引入css类 */\r\n</style>\r\n"]}]}