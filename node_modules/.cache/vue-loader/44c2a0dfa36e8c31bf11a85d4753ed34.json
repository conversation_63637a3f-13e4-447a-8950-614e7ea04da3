{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue?vue&type=template&id=e87156b0&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue", "mtime": 1756362725169}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"addBrand-container\">\n  <div class=\"container\">\n    <el-form\n      :model=\"ruleForm\"\n      :rules=\"rules\"\n      ref=\"ruleForm\"\n      label-width=\"180px\"\n    >\n      <el-form-item label=\"账号\" prop=\"username\">\n        <el-input v-model=\"ruleForm.username\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"员工姓名\" prop=\"name\">\n        <el-input v-model=\"ruleForm.name\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"手机号\" prop=\"phone\">\n        <el-input v-model=\"ruleForm.phone\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"sex\">\n        <el-radio v-model=\"ruleForm.sex\" label=\"1\">男</el-radio>\n        <el-radio v-model=\"ruleForm.sex\" label=\"2\">女</el-radio>\n      </el-form-item>\n      <el-form-item label=\"身份证号\" prop=\"idNumber\">\n        <el-input v-model=\"ruleForm.idNumber\"></el-input>\n      </el-form-item>\n      <div class=\"subBox\">\n        <el-button type=\"primary\" @click=\"submitForm('ruleForm', false)\"\n          >保存</el-button\n        >\n        <el-button\n          v-if=\"this.optType === 'add'\"\n          type=\"primary\"\n          @click=\"submitForm('ruleForm', true)\"\n          >保存并继续添加员工\n        </el-button>\n        <el-button @click=\"() => this.$router.push('/employee')\"\n          >返回</el-button\n        >\n      </div>\n    </el-form>\n  </div>\n</div>\n", null]}