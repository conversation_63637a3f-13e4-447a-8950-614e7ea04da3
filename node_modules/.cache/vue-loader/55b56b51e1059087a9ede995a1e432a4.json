{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue?vue&type=style&index=0&id=e87156b0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/addEmployee.vue", "mtime": 1693964898000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.addBrand {\r\n  &-container {\r\n    margin: 30px;\r\n    margin-top: 30px;\r\n    .HeadLable {\r\n      background-color: transparent;\r\n      margin-bottom: 0px;\r\n      padding-left: 0px;\r\n    }\r\n    .container {\r\n      position: relative;\r\n      z-index: 1;\r\n      background: #fff;\r\n      padding: 30px;\r\n      border-radius: 4px;\r\n      // min-height: 500px;\r\n      .subBox {\r\n        padding-top: 30px;\r\n        text-align: center;\r\n        border-top: solid 1px $gray-5;\r\n      }\r\n    }\r\n    .idNumber {\r\n      margin-bottom: 39px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 29px;\r\n    }\r\n    .el-input {\r\n      width: 293px;\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["addEmployee.vue"], "names": [], "mappings": ";AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addEmployee.vue", "sourceRoot": "src/views/employee", "sourcesContent": ["<template>\r\n  <div class=\"addBrand-container\">\r\n    <div class=\"container\">\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"180px\">\r\n        <el-form-item label=\"账号\" prop=\"username\">\r\n          <el-input v-model=\"ruleForm.username\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"员工姓名\" prop=\"name\">\r\n          <el-input v-model=\"ruleForm.name\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"ruleForm.phone\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"性别\" prop=\"sex\">\r\n            <el-radio v-model=\"ruleForm.sex\" label=\"1\">男</el-radio>\r\n            <el-radio v-model=\"ruleForm.sex\" label=\"2\">女</el-radio>\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idNumber\">\r\n          <el-input v-model=\"ruleForm.idNumber\"></el-input>\r\n        </el-form-item>\r\n        <div class=\"subBox\">\r\n          <el-button type=\"primary\" @click=\"submitForm('ruleForm',false)\">保存</el-button>\r\n          <el-button \r\n            v-if=\"this.optType === 'add'\" \r\n            type=\"primary\" \r\n            @click=\"submitForm('ruleForm',true)\">保存并继续添加员工\r\n          </el-button>\r\n          <el-button @click=\"() => this.$router.push('/employee')\">返回</el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\n\r\nexport default {\r\n  \r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.addBrand {\r\n  &-container {\r\n    margin: 30px;\r\n    margin-top: 30px;\r\n    .HeadLable {\r\n      background-color: transparent;\r\n      margin-bottom: 0px;\r\n      padding-left: 0px;\r\n    }\r\n    .container {\r\n      position: relative;\r\n      z-index: 1;\r\n      background: #fff;\r\n      padding: 30px;\r\n      border-radius: 4px;\r\n      // min-height: 500px;\r\n      .subBox {\r\n        padding-top: 30px;\r\n        text-align: center;\r\n        border-top: solid 1px $gray-5;\r\n      }\r\n    }\r\n    .idNumber {\r\n      margin-bottom: 39px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 29px;\r\n    }\r\n    .el-input {\r\n      width: 293px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}