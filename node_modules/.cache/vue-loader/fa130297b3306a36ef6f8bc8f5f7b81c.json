{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/components/password.vue?vue&type=style&index=0&id=2ff4cc77&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/components/password.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.navbar {\r\n  .pwdCon {\r\n    .el-dialog__body {\r\n      padding-top: 60px;\r\n      padding: 60px 100px 0;\r\n    }\r\n    .el-input__inner {\r\n      padding: 0 12px;\r\n    }\r\n    .el-form-item {\r\n      margin-bottom: 26px;\r\n    }\r\n    .el-form-item__label {\r\n      text-align: left;\r\n    }\r\n    .el-dialog__footer {\r\n      padding-top: 14px;\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["password.vue"], "names": [], "mappings": ";AA+FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "password.vue", "sourceRoot": "src/layout/components/components", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"修改密码\"\r\n    :visible.sync=\"dialogFormVisible\"\r\n    width=\"568px\"\r\n    class=\"pwdCon\"\r\n    @close=\"handlePwdClose()\"\r\n  >\r\n    <el-form :model=\"form\" label-width=\"85px\" :rules=\"rules\" ref=\"form\">\r\n      <el-form-item label=\"原始密码：\" prop=\"oldPassword\">\r\n        <el-input\r\n          v-model=\"form.oldPassword\"\r\n          type=\"password\"\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"新密码：\" prop=\"newPassword\">\r\n        <el-input\r\n          v-model=\"form.newPassword\"\r\n          type=\"password\"\r\n          placeholder=\"6 - 20位密码，数字或字母，区分大小写\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码：\" prop=\"affirmPassword\">\r\n        <el-input\r\n          v-model=\"form.affirmPassword\"\r\n          type=\"password\"\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handlePwdClose()\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSave()\">保 存</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script lang=\"ts\">\r\nimport { Component, Vue, Prop } from 'vue-property-decorator'\r\nimport { Form as ElForm, Input } from 'element-ui'\r\n// 接口\r\nimport { editPassword } from '@/api/users'\r\n@Component({\r\n  name: 'Password',\r\n})\r\nexport default class extends Vue {\r\n  @Prop() private dialogFormVisible!: any\r\n  private validatePwd = (rule: any, value: any, callback: Function) => {\r\n    const reg = /^[0-9A-Za-z]{6,20}$/\r\n    if (!value) {\r\n      callback(new Error('请输入'))\r\n    } else if (!reg.test(value)) {\r\n      callback(new Error('6 - 20位密码，数字或字母，区分大小写'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private validatePass2 = (rule, value, callback) => {\r\n    if (!value) {\r\n      callback(new Error('请再次输入密码'))\r\n    } else if (value !== this.form.newPassword) {\r\n      callback(new Error('密码不一致，请重新输入密码'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  rules = {\r\n    oldPassword: [{ validator: this.validatePwd, trigger: 'blur' }],\r\n    newPassword: [{ validator: this.validatePwd, trigger: 'blur' }],\r\n    affirmPassword: [{ validator: this.validatePass2, trigger: 'blur' }],\r\n  }\r\n  private form = {} as any\r\n  private affirmPassword = ''\r\n  handleSave() {\r\n    ;(this.$refs.form as ElForm).validate(async (valid: boolean) => {\r\n      if (valid) {\r\n        const parnt = {\r\n          oldPassword: this.form.oldPassword,\r\n          newPassword: this.form.newPassword,\r\n        }\r\n        await editPassword(parnt)\r\n        this.$emit('handleclose')\r\n        ;(this.$refs.form as ElForm).resetFields()\r\n      } else {\r\n        return false\r\n      }\r\n    })\r\n  }\r\n  handlePwdClose() {\r\n    ;(this.$refs.form as ElForm).resetFields()\r\n    this.$emit('handleclose')\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.navbar {\r\n  .pwdCon {\r\n    .el-dialog__body {\r\n      padding-top: 60px;\r\n      padding: 60px 100px 0;\r\n    }\r\n    .el-input__inner {\r\n      padding: 0 12px;\r\n    }\r\n    .el-form-item {\r\n      margin-bottom: 26px;\r\n    }\r\n    .el-form-item__label {\r\n      text-align: left;\r\n    }\r\n    .el-dialog__footer {\r\n      padding-top: 14px;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}