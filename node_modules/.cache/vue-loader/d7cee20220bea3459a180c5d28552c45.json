{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/HeadLable/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/HeadLable/index.vue", "mtime": 1691561690000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\n\r\n@Component({\r\n  'name': 'Hamburger'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ 'default': false }) private goback!: boolean\r\n  @Prop({ 'default': false }) private butList!: boolean\r\n  @Prop({ 'default': '集团管理' }) private title!: string\r\n\r\n  private toggleClick() {\r\n    this.$emit('toggleClick')\r\n  }\r\n\r\n  private goBack() {\r\n    this.$router.go(-1)\r\n  }\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeadLable", "sourcesContent": ["<template>\r\n  <div class=\"HeadLable\">\r\n    <span\r\n      v-if=\"goback\"\r\n      class=\"goBack\"\r\n      @click=\"goBack()\"\r\n    ><img\r\n      src=\"@/assets/icons/<EMAIL>\"\r\n      alt=\"\"\r\n    > 返回</span>\r\n    <span v-if=\"!butList\">{{ title }}</span>\r\n    <div v-if=\"butList\">\r\n      <slot />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\n\r\n@Component({\r\n  'name': 'Hamburger'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ 'default': false }) private goback!: boolean\r\n  @Prop({ 'default': false }) private butList!: boolean\r\n  @Prop({ 'default': '集团管理' }) private title!: string\r\n\r\n  private toggleClick() {\r\n    this.$emit('toggleClick')\r\n  }\r\n\r\n  private goBack() {\r\n    this.$router.go(-1)\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .HeadLable{\r\n    // position: absolute;\r\n    background: #fff;\r\n    color: #333333;\r\n    height: 64px;\r\n    font-size: 16px;\r\n    // width: 300px;\r\n    padding-left: 22px;\r\n    line-height: 64px;\r\n    font-weight: 700;\r\n    margin-bottom: 15px;\r\n    top:0px;\r\n    left: 0px;\r\n    opacity: 0;\r\n    animation: opacity 500ms ease-out 800ms forwards;\r\n    .goBack{\r\n      border-right: solid 1px #d8dde3;\r\n      padding-right: 14px;\r\n      margin-right: 14px;\r\n      font-size: 16px;\r\n      color: #333333;\r\n      cursor: pointer;\r\n      font-weight: 400;\r\n      img{\r\n        position: relative;\r\n        top:24px;\r\n        margin-right: 5px;\r\n        width: 18px;\r\n        height: 18px;\r\n        float: left;\r\n      }\r\n    }\r\n  }\r\n  @keyframes opacity {\r\n     0% {\r\n       opacity: 0;\r\n       left: 80px;\r\n     }\r\n     100% {\r\n       opacity: 1;\r\n       left: 0;\r\n     }\r\n   }\r\n</style>\r\n"]}]}