{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue?vue&type=template&id=057296a1", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue", "mtime": 1656301911000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"title-index\">\n  <div class=\"month\">\n    <ul class=\"tabs\">\n      <li\n        class=\"li-tab\"\n        v-for=\"(item, index) in tabsParam\"\n        @click=\"toggleTabs(index)\"\n        :class=\"{ active: index === nowIndex }\"\n        :key=\"index\"\n      >\n        {{ item }}\n        <span></span>\n      </li>\n    </ul>\n  </div>\n  <div class=\"get-time\">\n    <p>\n      已选时间：{{ tateData[0] }} 至\n      {{ tateData[tateData.length - 1] }}\n    </p>\n  </div>\n  <el-button\n    icon=\"iconfont icon-download\"\n    class=\"right-el-button\"\n    @click=\"handleExport\"\n    >数据导出</el-button\n  >\n</div>\n", null]}