{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/category/index.vue?vue&type=style&index=1&id=7f48609b&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/category/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n// .customClass {\r\n//   .el-button--primary {\r\n//     background-color: #ffc200 !important ;\r\n//   }\r\n// }\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4cA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/category", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"container\">\r\n      <div class=\"tableBar\"\r\n           style=\"display: inline-block; width: 100%\">\r\n        <label style=\"margin-right: 10px\">分类名称：</label>\r\n        <el-input v-model=\"name\"\r\n                  placeholder=\"请填写分类名称\"\r\n                  style=\"width: 15%\"\r\n                  clearable\r\n                  @clear=\"init\"\r\n                  @keyup.enter.native=\"init\" />\r\n\r\n        <label style=\"margin-right: 5px; margin-left: 20px\">分类类型：</label>\r\n        <el-select v-model=\"categoryType\"\r\n                   placeholder=\"请选择\"\r\n                   clearable\r\n                   style=\"width: 15%\"\r\n                   @clear=\"init\">\r\n          <el-option v-for=\"item in options\"\r\n                     :key=\"item.value\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.value\" />\r\n        </el-select>\r\n\r\n        <div style=\"float: right\">\r\n          <el-button type=\"primary\"\r\n                     class=\"continue\"\r\n                     @click=\"addClass('class')\">\r\n            + 新增菜品分类\r\n          </el-button>\r\n          <el-button type=\"primary\"\r\n                     style=\"margin-left:20px\"\r\n                     @click=\"addClass('meal')\">\r\n            + 新增套餐分类\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-button class=\"normal-btn continue\"\r\n                   @click=\"init(true)\">\r\n          查询\r\n        </el-button>\r\n      </div>\r\n      <el-table v-if=\"tableData.length\"\r\n                :data=\"tableData\"\r\n                stripe\r\n                class=\"tableBox\">\r\n        <el-table-column prop=\"name\"\r\n                         label=\"分类名称\" />\r\n        <el-table-column prop=\"type\"\r\n                         label=\"分类类型\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.type == '1' ? '菜品分类' : '套餐分类' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"sort\"\r\n                         label=\"排序\" />\r\n        <el-table-column label=\"状态\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"tableColumn-status\"\r\n                 :class=\"{ 'stop-use': String(scope.row.status) === '0' }\">\r\n              {{ String(scope.row.status) === '0' ? '禁用' : '启用' }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateTime\"\r\n                         label=\"操作时间\" />\r\n        <el-table-column label=\"操作\"\r\n                         width=\"200\"\r\n                         align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       size=\"small\"\r\n                       class=\"blueBug\"\r\n                       @click=\"editHandle(scope.row)\">\r\n              修改\r\n            </el-button>\r\n            <el-button type=\"text\"\r\n                       size=\"small\"\r\n                       class=\"delBut\"\r\n                       @click=\"deleteHandle(scope.row.id)\">\r\n              删除\r\n            </el-button>\r\n            <el-button type=\"text\"\r\n                       size=\"small\"\r\n                       class=\"non\"\r\n                       :class=\"{\r\n                         blueBug: scope.row.status == '0',\r\n                         delBut: scope.row.status != '0'\r\n                       }\"\r\n                       @click=\"statusHandle(scope.row)\">\r\n              {{ scope.row.status == '1' ? '禁用' : '启用' }}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <Empty v-else\r\n             :is-search=\"isSearch\" />\r\n      <el-pagination v-if=\"counts > 10\"\r\n                     class=\"pageList\"\r\n                     :page-sizes=\"[10, 20, 30, 40]\"\r\n                     :page-size=\"pageSize\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     :total=\"counts\"\r\n                     @size-change=\"handleSizeChange\"\r\n                     @current-change=\"handleCurrentChange\" />\r\n    </div>\r\n    <el-dialog :title=\"classData.title\"\r\n               :visible.sync=\"classData.dialogVisible\"\r\n               width=\"30%\"\r\n               :before-close=\"handleClose\">\r\n      <el-form ref=\"classData\"\r\n               :model=\"classData\"\r\n               class=\"demo-form-inline\"\r\n               :rules=\"rules\"\r\n               label-width=\"100px\">\r\n        <el-form-item label=\"分类名称：\"\r\n                      prop=\"name\">\r\n          <el-input v-model=\"classData.name\"\r\n                    placeholder=\"请输入分类名称\"\r\n                    maxlength=\"20\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排序：\"\r\n                      prop=\"sort\">\r\n          <el-input v-model=\"classData.sort\"\r\n                    placeholder=\"请输入排序\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <span slot=\"footer\"\r\n            class=\"dialog-footer\">\r\n        <el-button size=\"medium\"\r\n                   @click=\"\r\n            ;(classData.dialogVisible = false), $refs.classData.resetFields()\r\n                   \">取 消</el-button>\r\n        <el-button type=\"primary\"\r\n                   :class=\"{ continue: actionType === 'add' }\"\r\n                   size=\"medium\"\r\n                   @click=\"submitForm()\">确 定</el-button>\r\n        <el-button v-if=\"action != 'edit'\"\r\n                   type=\"primary\"\r\n                   size=\"medium\"\r\n                   @click=\"submitForm('go')\">\r\n          保存并继续添加\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue } from 'vue-property-decorator'\r\nimport HeadLable from '@/components/HeadLable/index.vue'\r\nimport {\r\n  getCategoryPage,\r\n  deleCategory,\r\n  editCategory,\r\n  addCategory,\r\n  enableOrDisableEmployee\r\n} from '@/api/category'\r\nimport Empty from '@/components/Empty/index.vue'\r\n\r\n@Component({\r\n  name: 'Category',\r\n  components: {\r\n    HeadLable,\r\n    Empty\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private options: any = [\r\n    {\r\n      value: 1,\r\n      label: '菜品分类'\r\n    },\r\n    {\r\n      value: 2,\r\n      label: '套餐分类'\r\n    }\r\n  ]\r\n  private actionType: string = ''\r\n  private id = ''\r\n  private status = ''\r\n  private categoryType: number = null\r\n  private name: string = ''\r\n  private action: string = ''\r\n  private counts: number = 0\r\n  private page: number = 1\r\n  private pageSize: number = 10\r\n  private tableData = []\r\n  private type = ''\r\n  private isSearch: boolean = false\r\n  private classData: any = {\r\n    title: '添加菜品分类',\r\n    dialogVisible: false,\r\n    categoryId: '',\r\n    name: '',\r\n    sort: ''\r\n  }\r\n\r\n  get rules() {\r\n    return {\r\n      name: [\r\n        {\r\n          required: true,\r\n          trigger: 'blur',\r\n          validator: (rule: any, value: string, callback: Function) => {\r\n            // const reg = /[\\u4e00-\\u9fa5]/\r\n            var reg = new RegExp('^[A-Za-z\\u4e00-\\u9fa5]+$')\r\n            if (!value) {\r\n              callback(new Error(this.classData.title + '不能为空'))\r\n            } else if (value.length < 2) {\r\n              callback(new Error('分类名称输入不符，请输入2-20个字符'))\r\n            } else if (!reg.test(value)) {\r\n              callback(new Error('分类名称包含特殊字符'))\r\n            } else {\r\n              callback()\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      sort: [\r\n        {\r\n          required: true,\r\n          trigger: 'blur',\r\n          validator: (rule: any, value: string, callback: Function) => {\r\n            if (value || String(value) === '0') {\r\n              const reg = /^\\d+$/\r\n              if (!reg.test(value)) {\r\n                callback(new Error('排序只能输入数字类型'))\r\n              } else if (Number(value) > 99) {\r\n                callback(new Error('排序只能输入0-99数字'))\r\n              } else {\r\n                callback()\r\n              }\r\n            } else {\r\n              callback(new Error('排序不能为空'))\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  }\r\n\r\n  created() {\r\n    this.init()\r\n  }\r\n\r\n  // 初始化信息\r\n  private async init(isSearch?) {\r\n    this.isSearch = isSearch\r\n    await getCategoryPage({\r\n      page: this.page,\r\n      pageSize: this.pageSize,\r\n      name: this.name ? this.name : undefined,\r\n      type: this.categoryType ? this.categoryType : undefined\r\n    })\r\n      .then(res => {\r\n        if (String(res.data.code) === '1') {\r\n          this.tableData =\r\n            res && res.data && res.data.data && res.data.data.records\r\n          this.counts = Number(res.data.data.total)\r\n        } else {\r\n          this.$message.error(res.data.desc)\r\n        }\r\n      })\r\n      .catch(err => {\r\n        console.log(err, 'err')\r\n        this.$message.error('请求出错了：' + err.message)\r\n      })\r\n  }\r\n\r\n  // 添加\r\n  private addClass(st: any) {\r\n    if (st == 'class') {\r\n      this.classData.title = '新增菜品分类'\r\n      this.type = '1'\r\n    } else {\r\n      this.classData.title = '新增套餐分类'\r\n      this.type = '2'\r\n    }\r\n    this.action = 'add'\r\n    this.classData.name = ''\r\n    this.classData.sort = ''\r\n    this.classData.dialogVisible = true\r\n    this.actionType = 'add'\r\n  }\r\n\r\n  // 修改\r\n  private editHandle(dat: any) {\r\n    this.classData.title = '修改分类'\r\n    this.action = 'edit'\r\n    this.classData.name = dat.name\r\n    this.classData.sort = dat.sort\r\n    this.classData.id = dat.id\r\n    this.classData.dialogVisible = true\r\n    this.actionType = 'edit'\r\n  }\r\n\r\n  // 关闭弹窗\r\n  private handleClose(st: string) {\r\n    console.log(this.$refs.classData, 'this.$refs.classData')\r\n    this.classData.dialogVisible = false\r\n    //对该表单项进行重置，将其值重置为初始值并移除校验结果\r\n    this.$refs.classData.resetFields()\r\n  }\r\n\r\n  //状态修改\r\n  private statusHandle(row: any) {\r\n    this.id = row.id\r\n    this.status = row.status\r\n    this.$confirm('确认调整该分类的状态?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning',\r\n      customClass: 'customClass'\r\n    }).then(() => {\r\n      enableOrDisableEmployee({ id: this.id, status: !this.status ? 1 : 0 })\r\n        .then(res => {\r\n          if (String(res.status) === '200') {\r\n            this.$message.success('分类状态更改成功！')\r\n            this.init()\r\n          }\r\n        })\r\n        .catch(err => {\r\n          this.$message.error('请求出错了：' + err.message)\r\n        })\r\n    })\r\n  }\r\n\r\n  //删除\r\n  private deleteHandle(id: any) {\r\n    this.$confirm('此操作将永久删除该分类，是否继续？', '确定删除', {\r\n      confirmButtonText: '删除',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => {\r\n      deleCategory(id)\r\n        .then(res => {\r\n          if (res.data.code === 1) {\r\n            this.$message.success('删除成功！')\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.data.msg)\r\n          }\r\n        })\r\n        .catch(err => {\r\n          this.$message.error('请求出错了：' + err.message)\r\n        })\r\n    })\r\n  }\r\n\r\n  $refs!: {\r\n    classData: any\r\n  }\r\n\r\n  //数据提交\r\n  submitForm(st: any) {\r\n    if (this.action === 'add') {\r\n      this.$refs.classData.validate((value: boolean) => {\r\n        if (value) {\r\n          addCategory({\r\n            name: this.classData.name,\r\n            type: this.type,\r\n            sort: this.classData.sort\r\n          })\r\n            .then(res => {\r\n              if (res.data.code === 1) {\r\n                this.$message.success('分类添加成功！')\r\n                this.$refs.classData.resetFields()\r\n                if (!st) {\r\n                  this.classData.dialogVisible = false\r\n                }\r\n                this.init()\r\n              } else {\r\n                this.$message.error(res.data.desc || res.data.msg)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        }\r\n      })\r\n    } else {\r\n      this.$refs.classData.validate((value: boolean) => {\r\n        if (value) {\r\n          editCategory({\r\n            id: this.classData.id,\r\n            name: this.classData.name,\r\n            sort: this.classData.sort\r\n          })\r\n            .then(res => {\r\n              if (res.data.code === 1) {\r\n                this.$message.success('分类修改成功！')\r\n                this.classData.dialogVisible = false\r\n                this.$refs.classData.resetFields()\r\n                this.init()\r\n              } else {\r\n                this.$message.error(res.data.desc || res.data.msg)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  //分页\r\n  private handleSizeChange(val: any) {\r\n    this.pageSize = val\r\n    this.init()\r\n  }\r\n\r\n  private handleCurrentChange(val: any) {\r\n    this.page = val\r\n    this.init()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.dashboard {\r\n  &-container {\r\n    margin: 30px;\r\n\r\n    .container {\r\n      background: #fff;\r\n      position: relative;\r\n      z-index: 1;\r\n      padding: 30px 28px;\r\n      border-radius: 4px;\r\n\r\n      .tableBar {\r\n        display: flex;\r\n        margin-bottom: 20px;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .tableBox {\r\n        width: 100%;\r\n        border: 1px solid $gray-5;\r\n        border-bottom: 0;\r\n      }\r\n\r\n      .pageList {\r\n        text-align: center;\r\n        margin-top: 30px;\r\n      }\r\n      //查询黑色按钮样式\r\n      .normal-btn {\r\n        background: #333333;\r\n        color: white;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang='scss'>\r\n// .customClass {\r\n//   .el-button--primary {\r\n//     background-color: #ffc200 !important ;\r\n//   }\r\n// }\r\n</style>\r\n"]}]}