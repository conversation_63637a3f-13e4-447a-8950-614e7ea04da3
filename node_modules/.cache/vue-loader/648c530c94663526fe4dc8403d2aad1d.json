{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/index.vue?vue&type=template&id=106c86ed", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/index.vue", "mtime": 1655803193000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"dashboard-container home\">\n  <!-- 营业数据 -->\n  <Overview :overviewData=\"overviewData\" />\n  <!-- end -->\n  <!-- 订单管理 -->\n  <Orderview :orderviewData=\"orderviewData\" />\n  <!-- end -->\n  <div class=\"homeMain\">\n    <!-- 菜品总览 -->\n    <CuisineStatistics :dishesData=\"dishesData\" />\n    <!-- end -->\n    <!-- 套餐总览 -->\n    <SetMealStatistics :setMealData=\"setMealData\" />\n    <!-- end -->\n  </div>\n  <!-- 订单信息 -->\n  <OrderList\n    :order-statics=\"orderStatics\"\n    @getOrderListBy3Status=\"getOrderListBy3Status\"\n  />\n  <!-- end -->\n</div>\n", null]}