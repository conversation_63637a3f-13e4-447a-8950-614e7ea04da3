{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/addSetmeal.vue?vue&type=template&id=c313e5f0&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/addSetmeal.vue", "mtime": 1695192173000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"addBrand-container\">\n  <div class=\"container\">\n    <el-form ref=\"ruleForm\"\n             :model=\"ruleForm\"\n             :rules=\"rules\"\n             :inline=\"true\"\n             label-width=\"180px\"\n             class=\"demo-ruleForm\">\n      <div>\n        <el-form-item label=\"套餐名称:\"\n                      prop=\"name\">\n          <el-input v-model=\"ruleForm.name\"\n                    placeholder=\"请填写套餐名称\"\n                    maxlength=\"14\" />\n        </el-form-item>\n        <el-form-item label=\"套餐分类:\"\n                      prop=\"idType\">\n          <el-select v-model=\"ruleForm.idType\"\n                     placeholder=\"请选择套餐分类\"\n                     @change=\"$forceUpdate()\">\n            <el-option v-for=\"(item, index) in setMealList\"\n                       :key=\"index\"\n                       :label=\"item.name\"\n                       :value=\"item.id\" />\n          </el-select>\n        </el-form-item>\n      </div>\n      <div>\n        <el-form-item label=\"套餐价格:\"\n                      prop=\"price\">\n          <el-input v-model=\"ruleForm.price\"\n                    placeholder=\"请设置套餐价格\" />\n        </el-form-item>\n      </div>\n      <div>\n        <el-form-item label=\"套餐菜品:\"\n                      required>\n          <el-form-item>\n            <div class=\"addDish\">\n              <span v-if=\"dishTable.length == 0\"\n                    class=\"addBut\"\n                    @click=\"openAddDish('new')\">\n                + 添加菜品</span>\n              <div v-if=\"dishTable.length != 0\"\n                   class=\"content\">\n                <div class=\"addBut\"\n                     style=\"margin-bottom: 20px\"\n                     @click=\"openAddDish('change')\">\n                  + 添加菜品\n                </div>\n                <div class=\"table\">\n                  <el-table :data=\"dishTable\"\n                            style=\"width: 100%\">\n                    <el-table-column prop=\"name\"\n                                     label=\"名称\"\n                                     width=\"180\"\n                                     align=\"center\" />\n                    <el-table-column prop=\"price\"\n                                     label=\"原价\"\n                                     width=\"180\"\n                                     align=\"center\">\n                      <template slot-scope=\"scope\">\n                        {{ (Number(scope.row.price).toFixed(2) * 100) / 100 }}\n                      </template>\n                    </el-table-column>\n                    <el-table-column prop=\"address\"\n                                     label=\"份数\"\n                                     align=\"center\">\n                      <template slot-scope=\"scope\">\n                        <el-input-number v-model=\"scope.row.copies\"\n                                         size=\"small\"\n                                         :min=\"1\"\n                                         :max=\"99\"\n                                         label=\"描述文字\" />\n                      </template>\n                    </el-table-column>\n                    <el-table-column prop=\"address\"\n                                     label=\"操作\"\n                                     width=\"180px;\"\n                                     align=\"center\">\n                      <template slot-scope=\"scope\">\n                        <el-button type=\"text\"\n                                   size=\"small\"\n                                   class=\"delBut non\"\n                                   @click=\"delDishHandle(scope.$index)\">\n                          删除\n                        </el-button>\n                      </template>\n                    </el-table-column>\n                  </el-table>\n                </div>\n              </div>\n            </div>\n          </el-form-item>\n        </el-form-item>\n      </div>\n      <div>\n        <el-form-item label=\"套餐图片:\"\n                      required\n                      prop=\"image\">\n          <image-upload :prop-image-url=\"imageUrl\"\n                        @imageChange=\"imageChange\">\n            图片大小不超过2M<br>仅能上传 PNG JPEG JPG类型图片<br>建议上传200*200或300*300尺寸的图片\n          </image-upload>\n        </el-form-item>\n      </div>\n      <div class=\"address\">\n        <el-form-item label=\"套餐描述:\">\n          <el-input v-model=\"ruleForm.description\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    maxlength=\"200\"\n                    placeholder=\"套餐描述，最长200字\" />\n        </el-form-item>\n      </div>\n      <div class=\"subBox address\">\n        <el-form-item>\n          <el-button @click=\"() => $router.back()\">\n            取消\n          </el-button>\n          <el-button type=\"primary\"\n                     :class=\"{ continue: actionType === 'add' }\"\n                     @click=\"submitForm('ruleForm', false)\">\n            保存\n          </el-button>\n          <el-button v-if=\"actionType == 'add'\"\n                     type=\"primary\"\n                     @click=\"submitForm('ruleForm', true)\">\n            保存并继续添加\n          </el-button>\n        </el-form-item>\n      </div>\n    </el-form>\n  </div>\n  <el-dialog v-if=\"dialogVisible\"\n             title=\"添加菜品\"\n             class=\"addDishList\"\n             :visible.sync=\"dialogVisible\"\n             width=\"60%\"\n             :before-close=\"handleClose\">\n    <AddDish v-if=\"dialogVisible\"\n             ref=\"adddish\"\n             :check-list=\"checkList\"\n             :seach-key=\"seachKey\"\n             :dish-list=\"dishList\"\n             @checkList=\"getCheckList\" />\n    <span slot=\"footer\"\n          class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\"\n                 @click=\"addTableList\">添 加</el-button>\n    </span>\n  </el-dialog>\n</div>\n", null]}