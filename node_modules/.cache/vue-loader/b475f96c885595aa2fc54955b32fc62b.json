{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue?vue&type=template&id=2cc9af88&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue", "mtime": 1655712116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div>\n  <div class=\"container homecon\">\n    <h2 class=\"homeTitle homeTitleBtn\">\n      订单信息\n      <ul class=\"conTab\">\n        <li\n          v-for=\"(item, index) in tabList\"\n          :key=\"index\"\n          :class=\"activeIndex === index ? 'active' : ''\"\n          @click=\"handleClass(index)\"\n        >\n          <el-badge\n            class=\"item\"\n            :class=\"item.num >= 10 ? 'badgeW' : ''\"\n            :value=\"item.num > 99 ? '99+' : item.num\"\n            :hidden=\"!([2, 3].includes(item.value) && item.num)\"\n            >{{ item.label }}</el-badge\n          >\n        </li>\n      </ul>\n    </h2>\n    <div class=\"\">\n      <div v-if=\"orderData.length > 0\">\n        <el-table\n          :data=\"orderData\"\n          stripe\n          class=\"tableBox\"\n          style=\"width: 100%\"\n          @row-click=\"handleTable\"\n        >\n          <el-table-column prop=\"number\" label=\"订单号\"> </el-table-column>\n          <el-table-column label=\"订单菜品\">\n            <template slot-scope=\"scope\">\n              <div class=\"ellipsisHidden\">\n                <el-popover\n                  placement=\"top-start\"\n                  title=\"\"\n                  width=\"200\"\n                  trigger=\"hover\"\n                  :content=\"scope.row.orderDishes\"\n                >\n                  <span slot=\"reference\">{{ scope.row.orderDishes }}</span>\n                </el-popover>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"地址\"\n            :class-name=\"dialogOrderStatus === 2 ? 'address' : ''\"\n          >\n            <template slot-scope=\"scope\">\n              <div class=\"ellipsisHidden\">\n                <el-popover\n                  placement=\"top-start\"\n                  title=\"\"\n                  width=\"200\"\n                  trigger=\"hover\"\n                  :content=\"scope.row.address\"\n                >\n                  <span slot=\"reference\">{{ scope.row.address }}</span>\n                </el-popover>\n              </div>\n            </template>\n          </el-table-column>\n\n          <el-table-column\n            prop=\"estimatedDeliveryTime\"\n            label=\"预计送达时间\"\n            sortable\n            class-name=\"orderTime\"\n            min-width=\"130\"\n          >\n          </el-table-column>\n          <el-table-column prop=\"amount\" label=\"实收金额\"> </el-table-column>\n          <el-table-column label=\"备注\">\n            <template slot-scope=\"scope\">\n              <div class=\"ellipsisHidden\">\n                <el-popover\n                  placement=\"top-start\"\n                  title=\"\"\n                  width=\"200\"\n                  trigger=\"hover\"\n                  :content=\"scope.row.remark\"\n                >\n                  <span slot=\"reference\">{{ scope.row.remark }}</span>\n                </el-popover>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"tablewareNumber\"\n            label=\"餐具数量\"\n            min-width=\"80\"\n            align=\"center\"\n            v-if=\"status === 3\"\n          >\n          </el-table-column>\n          <el-table-column\n            label=\"操作\"\n            align=\"center\"\n            :class-name=\"dialogOrderStatus === 0 ? 'operate' : 'otherOperate'\"\n            :min-width=\"\n              [2, 3].includes(dialogOrderStatus)\n                ? 130\n                : [0].includes(dialogOrderStatus)\n                ? 140\n                : 'auto'\n            \"\n          >\n            <template slot-scope=\"{ row }\">\n              <!-- <el-divider direction=\"vertical\" /> -->\n              <div class=\"before\">\n                <el-button\n                  v-if=\"row.status === 2\"\n                  type=\"text\"\n                  class=\"blueBug\"\n                  @click=\"\n                    orderAccept(row, $event), (isTableOperateBtn = true)\n                  \"\n                >\n                  接单\n                </el-button>\n                <el-button\n                  v-if=\"row.status === 3\"\n                  type=\"text\"\n                  class=\"blueBug\"\n                  @click=\"cancelOrDeliveryOrComplete(3, row.id, $event)\"\n                >\n                  派送\n                </el-button>\n              </div>\n              <div class=\"middle\">\n                <el-button\n                  v-if=\"row.status === 2\"\n                  type=\"text\"\n                  class=\"delBut\"\n                  @click=\"\n                    orderReject(row, $event), (isTableOperateBtn = true)\n                  \"\n                >\n                  拒单\n                </el-button>\n                <el-button\n                  v-if=\"[1, 3, 4, 5].includes(row.status)\"\n                  type=\"text\"\n                  class=\"delBut\"\n                  @click=\"cancelOrder(row, $event)\"\n                >\n                  取消\n                </el-button>\n              </div>\n              <div class=\"after\">\n                <el-button\n                  type=\"text\"\n                  class=\"blueBug non\"\n                  @click=\"goDetail(row.id, row.status, row, $event)\"\n                >\n                  查看\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n      <Empty v-else :is-search=\"isSearch\" />\n      <el-pagination\n        v-if=\"counts > 10\"\n        class=\"pageList\"\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"counts\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n  </div>\n  <!-- 查看弹框部分 -->\n  <el-dialog\n    title=\"订单信息\"\n    :visible.sync=\"dialogVisible\"\n    width=\"53%\"\n    :before-close=\"handleClose\"\n    class=\"order-dialog\"\n  >\n    <el-scrollbar style=\"height: 100%\">\n      <div class=\"order-top\">\n        <div>\n          <div style=\"display: inline-block\">\n            <label style=\"font-size: 16px\">订单号：</label>\n            <div class=\"order-num\">\n              {{ diaForm.number }}\n            </div>\n          </div>\n          <div\n            style=\"display: inline-block\"\n            class=\"order-status\"\n            :class=\"{ status3: [3, 4].includes(dialogOrderStatus) }\"\n          >\n            {{\n              orderList.filter((item) => item.value === dialogOrderStatus)[0]\n                .label\n            }}\n          </div>\n        </div>\n        <p><label>下单时间：</label>{{ diaForm.orderTime }}</p>\n      </div>\n\n      <div class=\"order-middle\">\n        <div class=\"user-info\">\n          <div class=\"user-info-box\">\n            <div class=\"user-name\">\n              <label>用户名：</label>\n              <span>{{ diaForm.consignee }}</span>\n            </div>\n            <div class=\"user-phone\">\n              <label>手机号：</label>\n              <span>{{ diaForm.phone }}</span>\n            </div>\n            <div\n              v-if=\"[2, 3, 4, 5].includes(dialogOrderStatus)\"\n              class=\"user-getTime\"\n            >\n              <label>{{\n                dialogOrderStatus === 5 ? '送达时间：' : '预计送达时间：'\n              }}</label>\n              <span>{{\n                dialogOrderStatus === 5\n                  ? diaForm.deliveryTime\n                  : diaForm.estimatedDeliveryTime\n              }}</span>\n            </div>\n            <div class=\"user-address\">\n              <label>地址：</label>\n              <span>{{ diaForm.address }}</span>\n            </div>\n          </div>\n          <div\n            class=\"user-remark\"\n            :class=\"{ orderCancel: dialogOrderStatus === 6 }\"\n          >\n            <div>{{ dialogOrderStatus === 6 ? '取消原因' : '备注' }}</div>\n            <span>{{\n              dialogOrderStatus === 6\n                ? diaForm.cancelReason || diaForm.rejectionReason\n                : diaForm.remark\n            }}</span>\n          </div>\n        </div>\n\n        <div class=\"dish-info\">\n          <div class=\"dish-label\">菜品</div>\n          <div class=\"dish-list\">\n            <div\n              v-for=\"(item, index) in diaForm.orderDetailList\"\n              :key=\"index\"\n              class=\"dish-item\"\n            >\n              <span class=\"dish-name\">{{ item.name }}</span>\n              <span class=\"dish-num\">x{{ item.number }}</span>\n              <span class=\"dish-price\"\n                >￥{{ item.amount ? item.amount.toFixed(2) : '' }}</span\n              >\n            </div>\n          </div>\n          <div class=\"dish-all-amount\">\n            <label>菜品小计</label>\n            <span\n              >￥{{\n                (diaForm.amount - 6 - diaForm.packAmount).toFixed(2)\n              }}</span\n            >\n          </div>\n        </div>\n      </div>\n\n      <div class=\"order-bottom\">\n        <div class=\"amount-info\">\n          <div class=\"amount-label\">费用</div>\n          <div class=\"amount-list\">\n            <div class=\"dish-amount\">\n              <span class=\"amount-name\">菜品小计：</span>\n              <span class=\"amount-price\"\n                >￥{{\n                  ((diaForm.amount - 6 - diaForm.packAmount).toFixed(2) *\n                    100) /\n                  100\n                }}</span\n              >\n            </div>\n            <div class=\"send-amount\">\n              <span class=\"amount-name\">派送费：</span>\n              <span class=\"amount-price\">￥{{ 6 }}</span>\n            </div>\n            <div class=\"package-amount\">\n              <span class=\"amount-name\">打包费：</span>\n              <span class=\"amount-price\"\n                >￥{{\n                  diaForm.packAmount\n                    ? (diaForm.packAmount.toFixed(2) * 100) / 100\n                    : ''\n                }}</span\n              >\n            </div>\n            <div class=\"all-amount\">\n              <span class=\"amount-name\">合计：</span>\n              <span class=\"amount-price\"\n                >￥{{\n                  diaForm.amount\n                    ? (diaForm.amount.toFixed(2) * 100) / 100\n                    : ''\n                }}</span\n              >\n            </div>\n            <div class=\"pay-type\">\n              <span class=\"pay-name\">支付渠道：</span>\n              <span class=\"pay-value\">{{\n                diaForm.payMethod === 1 ? '微信支付' : '支付宝支付'\n              }}</span>\n            </div>\n            <div class=\"pay-time\">\n              <span class=\"pay-name\">支付时间：</span>\n              <span class=\"pay-value\">{{ diaForm.checkoutTime }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-scrollbar>\n    <span v-if=\"dialogOrderStatus !== 6\" slot=\"footer\" class=\"dialog-footer\">\n      <el-checkbox\n        v-if=\"dialogOrderStatus === 2 && status === 2\"\n        v-model=\"isAutoNext\"\n        >处理完自动跳转下一条</el-checkbox\n      >\n      <el-button\n        v-if=\"dialogOrderStatus === 2\"\n        @click=\"orderReject(row, $event), (isTableOperateBtn = false)\"\n        >拒 单</el-button\n      >\n      <el-button\n        v-if=\"dialogOrderStatus === 2\"\n        type=\"primary\"\n        @click=\"orderAccept(row, $event), (isTableOperateBtn = false)\"\n        >接 单</el-button\n      >\n\n      <el-button\n        v-if=\"[1, 3, 4, 5].includes(dialogOrderStatus)\"\n        @click=\"dialogVisible = false\"\n        >返 回</el-button\n      >\n      <el-button\n        v-if=\"dialogOrderStatus === 3\"\n        type=\"primary\"\n        @click=\"cancelOrDeliveryOrComplete(3, row.id, $event)\"\n        >派 送</el-button\n      >\n      <el-button\n        v-if=\"dialogOrderStatus === 4\"\n        type=\"primary\"\n        @click=\"cancelOrDeliveryOrComplete(4, row.id, $event)\"\n        >完 成</el-button\n      >\n      <el-button\n        v-if=\"[1].includes(dialogOrderStatus)\"\n        type=\"primary\"\n        @click=\"cancelOrder(row, $event)\"\n        >取消订单</el-button\n      >\n    </span>\n  </el-dialog>\n  <!-- end -->\n  <!-- 拒单，取消弹窗 -->\n  <el-dialog\n    :title=\"cancelDialogTitle + '原因'\"\n    :visible.sync=\"cancelDialogVisible\"\n    width=\"42%\"\n    :before-close=\"() => ((cancelDialogVisible = false), (cancelReason = ''))\"\n    class=\"cancelDialog\"\n  >\n    <el-form label-width=\"90px\">\n      <el-form-item :label=\"cancelDialogTitle + '原因：'\">\n        <el-select\n          v-model=\"cancelReason\"\n          :placeholder=\"'请选择' + cancelDialogTitle + '原因'\"\n        >\n          <el-option\n            v-for=\"(item, index) in cancelDialogTitle === '取消'\n              ? cancelrReasonList\n              : cancelOrderReasonList\"\n            :key=\"index\"\n            :label=\"item.label\"\n            :value=\"item.label\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item v-if=\"cancelReason === '自定义原因'\" label=\"原因：\">\n        <el-input\n          v-model.trim=\"remark\"\n          type=\"textarea\"\n          :placeholder=\"'请填写您' + cancelDialogTitle + '的原因（限20字内）'\"\n          maxlength=\"20\"\n        />\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\";(cancelDialogVisible = false), (cancelReason = '')\"\n        >取 消</el-button\n      >\n      <el-button type=\"primary\" @click=\"confirmCancel\">确 定</el-button>\n    </span>\n  </el-dialog>\n  <!-- end -->\n</div>\n", null]}