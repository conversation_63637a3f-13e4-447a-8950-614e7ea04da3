{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--12-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/orderStatistics.vue?vue&type=template&id=45742eda", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/orderStatistics.vue", "mtime": 1656314117000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"h2\", {\n    staticClass: \"homeTitle\"\n  }, [_vm._v(\"订单统计\")]), _c(\"div\", {\n    staticClass: \"charBox\"\n  }, [_c(\"div\", {\n    staticClass: \"orderProportion\"\n  }, [_c(\"div\", [_c(\"p\", [_vm._v(\"订单完成率\")]), _c(\"p\", [_vm._v(_vm._s((_vm.orderdata.orderCompletionRate * 100).toFixed(1)) + \"%\")])]), _c(\"div\", {\n    staticClass: \"symbol\"\n  }, [_vm._v(\"=\")]), _c(\"div\", [_c(\"p\", [_vm._v(\"有效订单\")]), _c(\"p\", [_vm._v(_vm._s(_vm.orderdata.validOrderCount))])]), _c(\"div\", {\n    staticClass: \"symbol\"\n  }, [_vm._v(\"/\")]), _c(\"div\", [_c(\"p\", [_vm._v(\"订单总数\")]), _c(\"p\", [_vm._v(_vm._s(_vm.orderdata.totalOrderCount))])])]), _c(\"div\", {\n    staticStyle: {\n      width: \"100%\",\n      height: \"300px\"\n    },\n    attrs: {\n      id: \"ordermain\"\n    }\n  }), _vm._m(0)])]);\n};\nvar staticRenderFns = exports.staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"ul\", {\n    staticClass: \"orderListLine\"\n  }, [_c(\"li\", {\n    staticClass: \"one\"\n  }, [_c(\"span\"), _vm._v(\"订单总数（个）\")]), _c(\"li\", {\n    staticClass: \"three\"\n  }, [_c(\"span\"), _vm._v(\"有效订单（个）\")])]);\n}];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "_v", "_s", "orderdata", "orderCompletionRate", "toFixed", "validOrderCount", "totalOrderCount", "staticStyle", "width", "height", "attrs", "id", "_m", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/orderStatistics.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\"div\", { staticClass: \"container\" }, [\n    _c(\"h2\", { staticClass: \"homeTitle\" }, [_vm._v(\"订单统计\")]),\n    _c(\"div\", { staticClass: \"charBox\" }, [\n      _c(\"div\", { staticClass: \"orderProportion\" }, [\n        _c(\"div\", [\n          _c(\"p\", [_vm._v(\"订单完成率\")]),\n          _c(\"p\", [\n            _vm._v(\n              _vm._s((_vm.orderdata.orderCompletionRate * 100).toFixed(1)) + \"%\"\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"symbol\" }, [_vm._v(\"=\")]),\n        _c(\"div\", [\n          _c(\"p\", [_vm._v(\"有效订单\")]),\n          _c(\"p\", [_vm._v(_vm._s(_vm.orderdata.validOrderCount))]),\n        ]),\n        _c(\"div\", { staticClass: \"symbol\" }, [_vm._v(\"/\")]),\n        _c(\"div\", [\n          _c(\"p\", [_vm._v(\"订单总数\")]),\n          _c(\"p\", [_vm._v(_vm._s(_vm.orderdata.totalOrderCount))]),\n        ]),\n      ]),\n      _c(\"div\", {\n        staticStyle: { width: \"100%\", height: \"300px\" },\n        attrs: { id: \"ordermain\" },\n      }),\n      _vm._m(0),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c,\n      _setup = _vm._self._setupProxy\n    return _c(\"ul\", { staticClass: \"orderListLine\" }, [\n      _c(\"li\", { staticClass: \"one\" }, [_c(\"span\"), _vm._v(\"订单总数（个）\")]),\n      _c(\"li\", { staticClass: \"three\" }, [\n        _c(\"span\"),\n        _vm._v(\"有效订单（个）\"),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CAC7CJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACxDL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CJ,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACM,EAAE,CACJN,GAAG,CAACO,EAAE,CAAC,CAACP,GAAG,CAACQ,SAAS,CAACC,mBAAmB,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GACjE,CAAC,CACF,CAAC,CACH,CAAC,EACFT,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACnDL,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACzBL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,SAAS,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACnDL,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACzBL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,SAAS,CAACI,eAAe,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,EACFX,EAAE,CAAC,KAAK,EAAE;IACRY,WAAW,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAQ,CAAC;IAC/CC,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAY;EAC3B,CAAC,CAAC,EACFjB,GAAG,CAACkB,EAAE,CAAC,CAAC,CAAC,CACV,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAApB,OAAA,CAAAoB,eAAA,GAAG,CACpB,YAAY;EACV,IAAInB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAgB,CAAC,EAAE,CAChDJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAACJ,EAAE,CAAC,MAAM,CAAC,EAAED,GAAG,CAACM,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EACjEL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CACjCJ,EAAE,CAAC,MAAM,CAAC,EACVD,GAAG,CAACM,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDR,MAAM,CAACsB,aAAa,GAAG,IAAI", "ignoreList": []}]}