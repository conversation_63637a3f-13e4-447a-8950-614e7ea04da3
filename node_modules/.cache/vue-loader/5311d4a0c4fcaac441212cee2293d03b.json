{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue", "mtime": 1655712116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./orderList.vue?vue&type=template&id=2cc9af88&scoped=true\"\nimport script from \"./orderList.vue?vue&type=script&lang=ts\"\nexport * from \"./orderList.vue?vue&type=script&lang=ts\"\nimport style0 from \"./orderList.vue?vue&type=style&index=0&id=2cc9af88&lang=scss&scoped=true\"\nimport style1 from \"./orderList.vue?vue&type=style&index=1&id=2cc9af88&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2cc9af88\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2cc9af88')) {\n      api.createRecord('2cc9af88', component.options)\n    } else {\n      api.reload('2cc9af88', component.options)\n    }\n    module.hot.accept(\"./orderList.vue?vue&type=template&id=2cc9af88&scoped=true\", function () {\n      api.rerender('2cc9af88', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/dashboard/components/orderList.vue\"\nexport default component.exports"]}