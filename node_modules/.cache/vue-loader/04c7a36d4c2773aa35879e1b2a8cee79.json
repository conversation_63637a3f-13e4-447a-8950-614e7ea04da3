{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue?vue&type=style&index=0&id=19442e4b&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue", "mtime": 1756349952451}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.disabled-text {\r\n  color: #bac0cd !important;\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAgHA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/employee", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"container\">\r\n      <div class=\"tableBar\">\r\n        <label style=\"margin-right: 5px\">员工姓名：</label>\r\n        <el-input\r\n          v-model=\"name\"\r\n          placeholder=\"请输入员工姓名\"\r\n          style=\"width: 15%\"\r\n        />\r\n        <el-button type=\"primary\" style=\"margin-left: 25px\" @click=\"pageQuery()\"\r\n          >查询</el-button\r\n        >\r\n        <el-button type=\"primary\" style=\"float: right\">+添加员工</el-button>\r\n      </div>\r\n      <el-table :data=\"records\" stripe style=\"width: 100%\">\r\n        <el-table-column prop=\"name\" label=\"员工姓名\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"username\" label=\"账号\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"手机号\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"账号状态\">\r\n          <template slot-scope=\"scope\">{{\r\n            scope.row.status === 0 ? '禁用' : '启用'\r\n          }}</template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateTime\" label=\"最后操作时间\">\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\">修改</el-button>\r\n            <el-button type=\"text\" @click=\"handleStartOrStop(scope.row)\">{{\r\n              scope.row.status === 1 ? '禁用' : '启用'\r\n            }}</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n      class=\"pageList\"\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n      :current-page=\"page\"\r\n      :page-sizes=\"[10, 20, 30, 40,50]\"\r\n      :page-size=\"pageSize\"\r\n      layout=\"total, sizes, prev, pager, next, jumper\"\r\n      :total=\"total\">\r\n    </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\">\r\nimport { getEmployeeList,enableOrDisableEmployee } from '@/api/employee'\r\nexport default {\r\n  //模型数据\r\n  data() {\r\n    return {\r\n      name: '', //员工姓名，对应输入框\r\n      page: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      records: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.pageQuery()\r\n  },\r\n  methods: {\r\n    //分页查询\r\n    pageQuery() {\r\n      //准备参数\r\n      const params = {\r\n        name: this.name,\r\n        page: this.page,\r\n        pageSize: this.pageSize,\r\n      }\r\n      //发送ajax请求访问后端\r\n      getEmployeeList(params)\r\n        .then((res) => {\r\n          if (res.data.code === 1) {\r\n            this.total = res.data.data.total\r\n            this.records = res.data.data.records\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          this.$message.error('请求出错: ' + err.message)\r\n        })\r\n    },\r\n    //pagesize发生变化触发\r\n    handleSizeChange(pageSize){\r\n      this.pageSize=pageSize\r\n      this.pageQuery()\r\n    },\r\n    handleCurrentChange(page){\r\n      this.page=page\r\n      this.pageQuery()\r\n    },\r\n    //启用禁用\r\n    handleStartOrStop(row){\r\n      // alert(`id=${row.id}, status=${row.status}`)\r\n      enableOrDisableEmployee(row).then(res=>{\r\n        if(res.data.code===1){\r\n          this.$message.success('操作成功')\r\n          this.pageQuery()\r\n        }\r\n      })\r\n    },\r\n\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.disabled-text {\r\n  color: #bac0cd !important;\r\n}\r\n</style>\r\n"]}]}