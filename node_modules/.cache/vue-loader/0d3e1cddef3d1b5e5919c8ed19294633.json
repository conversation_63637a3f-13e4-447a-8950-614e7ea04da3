{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/orderDetails/index.vue?vue&type=style&index=0&id=0681039e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/orderDetails/index.vue", "mtime": 1655712070000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n.dashboard {\n  &-container {\n    margin: 30px;\n    // height: 100%;\n    min-height: 700px;\n    .container {\n      background: #fff;\n      position: relative;\n      z-index: 1;\n      padding: 30px 28px;\n      border-radius: 4px;\n      // min-height: 650px;\n      height: calc(100% - 55px);\n\n      .tableBar {\n        // display: flex;\n        margin-bottom: 20px;\n        justify-content: space-between;\n\n        .tableLab {\n          span {\n            cursor: pointer;\n            display: inline-block;\n            font-size: 14px;\n            padding: 0 20px;\n            color: $gray-2;\n            border-right: solid 1px $gray-4;\n          }\n        }\n      }\n\n      .tableBox {\n        width: 100%;\n        border: 1px solid $gray-5;\n        border-bottom: 0;\n      }\n\n      .pageList {\n        text-align: center;\n        margin-top: 30px;\n      }\n      //查询黑色按钮样式\n      .normal-btn {\n        background: #333333;\n        color: white;\n        margin-left: 20px;\n      }\n    }\n    .hContainer {\n      height: auto !important;\n    }\n  }\n}\n\n.search-btn {\n  margin-left: 20px;\n}\n\n.info-box {\n  margin: -15px -44px 20px;\n  p {\n    display: flex;\n    height: 20px;\n    line-height: 20px;\n    font-size: 14px;\n    font-weight: 400;\n    color: #666666;\n    text-align: left;\n    margin-bottom: 14px;\n    &:last-child {\n      margin-bottom: 0;\n    }\n    label {\n      width: 100px;\n      display: inline-block;\n      color: #666;\n    }\n    .des {\n      flex: 1;\n      color: #333333;\n    }\n  }\n}\n\n.order-top {\n  // height: 80px;\n  border-bottom: 1px solid #e7e6e6;\n  padding-bottom: 26px;\n  padding-left: 22px;\n  padding-right: 22px;\n  // margin: 0 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  .order-status {\n    width: 57.25px;\n    height: 27px;\n    background: #333333;\n    border-radius: 13.5px;\n    color: white;\n    margin-left: 19px;\n    text-align: center;\n    line-height: 27px;\n  }\n  .status3 {\n    background: #f56c6c;\n  }\n  p {\n    color: #333;\n    label {\n      color: #666;\n    }\n  }\n  .order-num {\n    font-size: 16px;\n    color: #2a2929;\n    font-weight: bold;\n    display: inline-block;\n  }\n}\n\n.order-middle {\n  .user-info {\n    min-height: 140px;\n    background: #fbfbfa;\n    margin-top: 23px;\n\n    padding: 20px 43px;\n    color: #333;\n    .user-info-box {\n      min-height: 55px;\n      display: flex;\n      flex-wrap: wrap;\n      .user-name {\n        flex: 67%;\n      }\n      .user-phone {\n        flex: 33%;\n      }\n      .user-getTime {\n        margin-top: 14px;\n        flex: 80%;\n        label {\n          margin-right: 3px;\n        }\n      }\n      label {\n        margin-right: 17px;\n        color: #666;\n      }\n\n      .user-address {\n        margin-top: 14px;\n        flex: 80%;\n        label {\n          margin-right: 30px;\n        }\n      }\n    }\n    .user-remark {\n      min-height: 43px;\n      line-height: 43px;\n      background: #fffbf0;\n      border: 1px solid #fbe396;\n      border-radius: 4px;\n      margin-top: 10px;\n      padding: 6px;\n      display: flex;\n      align-items: center;\n      div {\n        display: inline-block;\n        min-width: 53px;\n        height: 32px;\n        background: #fbe396;\n        border-radius: 4px;\n        text-align: center;\n        line-height: 32px;\n        color: #333;\n        margin-right: 30px;\n        // padding: 12px 6px;\n      }\n      span {\n        color: #f2a402;\n        line-height: 1.15;\n      }\n    }\n    .orderCancel {\n      background: #ffffff;\n      border: 1px solid #b6b6b6;\n\n      div {\n        padding: 0 10px;\n        background-color: #e5e4e4;\n      }\n      span {\n        color: #f56c6c;\n      }\n    }\n  }\n  .dish-info {\n    // min-height: 180px;\n    display: flex;\n    flex-wrap: wrap;\n    padding: 20px 40px;\n    border-bottom: 1px solid #e7e6e6;\n    .dish-label {\n      color: #666;\n    }\n    .dish-list {\n      flex: 80%;\n      display: flex;\n      flex-wrap: wrap;\n      .dish-item {\n        flex: 50%;\n        margin-bottom: 14px;\n        color: #333;\n        .dish-num {\n        }\n        .dish-item-box {\n          display: inline-block;\n          width: 120px;\n        }\n      }\n    }\n    .dish-label {\n      margin-right: 65px;\n    }\n    .dish-all-amount {\n      flex: 1;\n      padding-left: 92px;\n      margin-top: 10px;\n      label {\n        color: #333333;\n        font-weight: bold;\n        margin-right: 5px;\n      }\n      span {\n        color: #f56c6c;\n      }\n    }\n  }\n}\n.order-bottom {\n  .amount-info {\n    // min-height: 180px;\n    display: flex;\n    flex-wrap: wrap;\n    padding: 20px 40px;\n    padding-bottom: 0px;\n    .amount-label {\n      color: #666;\n      margin-right: 65px;\n    }\n    .amount-list {\n      flex: 80%;\n      display: flex;\n      flex-wrap: wrap;\n      color: #333;\n      // height: 65px;\n      .dish-amount,\n      .package-amount,\n      .pay-type {\n        display: inline-block;\n        width: 300px;\n        margin-bottom: 14px;\n        flex: 50%;\n      }\n      .send-amount,\n      .all-amount,\n      .pay-time {\n        display: inline-block;\n        flex: 50%;\n        padding-left: 10%;\n      }\n      .package-amount {\n        .amount-name {\n          margin-right: 14px;\n        }\n      }\n      .all-amount {\n        .amount-name {\n          margin-right: 24px;\n        }\n        .amount-price {\n          color: #f56c6c;\n        }\n      }\n      .send-amount {\n        .amount-name {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAi0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/orderDetails", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <TabChange\n      :order-statics=\"orderStatics\"\n      :default-activity=\"defaultActivity\"\n      @tabChange=\"change\"\n    />\n    <div class=\"container\" :class=\"{ hContainer: tableData.length }\">\n      <!-- 搜索项 -->\n      <div class=\"tableBar\">\n        <label style=\"margin-right: 10px\">订单号：</label>\n        <el-input\n          v-model=\"input\"\n          placeholder=\"请填写订单号\"\n          style=\"width: 15%\"\n          clearable\n          @clear=\"init(orderStatus)\"\n          @keyup.enter.native=\"initFun(orderStatus)\"\n        />\n        <label style=\"margin-left: 20px\">手机号：</label>\n        <el-input\n          v-model=\"phone\"\n          placeholder=\"请填写手机号\"\n          style=\"width: 15%\"\n          clearable\n          @clear=\"init(orderStatus)\"\n          @keyup.enter.native=\"initFun(orderStatus)\"\n        />\n        <label style=\"margin-left: 20px\">下单时间：</label>\n        <el-date-picker\n          v-model=\"valueTime\"\n          clearable\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          range-separator=\"至\"\n          :default-time=\"['00:00:00', '23:59:59']\"\n          type=\"daterange\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          style=\"width: 25%; margin-left: 10px\"\n          @clear=\"init(orderStatus)\"\n        />\n        <el-button class=\"normal-btn continue\" @click=\"init(orderStatus, true)\">\n          查询\n        </el-button>\n      </div>\n      <el-table\n        v-if=\"tableData.length\"\n        :data=\"tableData\"\n        stripe\n        class=\"tableBox\"\n      >\n        <el-table-column key=\"number\" prop=\"number\" label=\"订单号\" />\n        <el-table-column\n          v-if=\"[2, 3, 4].includes(orderStatus)\"\n          key=\"orderDishes\"\n          prop=\"orderDishes\"\n          label=\"订单菜品\"\n        />\n        <el-table-column\n          v-if=\"[0].includes(orderStatus)\"\n          key=\"status\"\n          prop=\"订单状态\"\n          label=\"订单状态\"\n        >\n          <template slot-scope=\"{ row }\">\n            <span>{{ getOrderType(row) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          v-if=\"[0, 5, 6].includes(orderStatus)\"\n          key=\"consignee\"\n          prop=\"consignee\"\n          label=\"用户名\"\n          show-overflow-tooltip\n        />\n        <el-table-column\n          v-if=\"[0, 5, 6].includes(orderStatus)\"\n          key=\"phone\"\n          prop=\"phone\"\n          label=\"手机号\"\n        />\n        <el-table-column\n          v-if=\"[0, 2, 3, 4, 5, 6].includes(orderStatus)\"\n          key=\"address\"\n          prop=\"address\"\n          label=\"地址\"\n          :class-name=\"orderStatus === 6 ? 'address' : ''\"\n        />\n        <el-table-column\n          v-if=\"[0, 6].includes(orderStatus)\"\n          key=\"orderTime\"\n          prop=\"orderTime\"\n          label=\"下单时间\"\n          class-name=\"orderTime\"\n          min-width=\"110\"\n        />\n        <el-table-column\n          v-if=\"[6].includes(orderStatus)\"\n          key=\"cancelTime\"\n          prop=\"cancelTime\"\n          class-name=\"cancelTime\"\n          label=\"取消时间\"\n          min-width=\"110\"\n        />\n        <el-table-column\n          v-if=\"[6].includes(orderStatus)\"\n          key=\"cancelReason\"\n          prop=\"cancelReason\"\n          label=\"取消原因\"\n          class-name=\"cancelReason\"\n          :min-width=\"[6].includes(orderStatus) ? 80 : 'auto'\"\n        />\n        <el-table-column\n          v-if=\"[5].includes(orderStatus)\"\n          key=\"deliveryTime\"\n          prop=\"deliveryTime\"\n          label=\"送达时间\"\n        />\n        <el-table-column\n          v-if=\"[2, 3, 4].includes(orderStatus)\"\n          key=\"estimatedDeliveryTime\"\n          prop=\"estimatedDeliveryTime\"\n          label=\"预计送达时间\"\n          min-width=\"110\"\n        />\n        <el-table-column\n          v-if=\"[0, 2, 5].includes(orderStatus)\"\n          key=\"amount\"\n          prop=\"amount\"\n          label=\"实收金额\"\n          align=\"center\"\n        >\n          <template slot-scope=\"{ row }\">\n            <span>￥{{ (row.amount.toFixed(2) * 100) / 100 }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          v-if=\"[2, 3, 4, 5].includes(orderStatus)\"\n          key=\"remark\"\n          prop=\"remark\"\n          label=\"备注\"\n          align=\"center\"\n        />\n        <el-table-column\n          v-if=\"[2, 3, 4].includes(orderStatus)\"\n          key=\"tablewareNumber\"\n          prop=\"tablewareNumber\"\n          label=\"餐具数量\"\n          align=\"center\"\n          min-width=\"80\"\n        />\n        <el-table-column\n          prop=\"btn\"\n          label=\"操作\"\n          align=\"center\"\n          :class-name=\"orderStatus === 0 ? 'operate' : 'otherOperate'\"\n          :min-width=\"\n            [2, 3, 4].includes(orderStatus)\n              ? 130\n              : [0].includes(orderStatus)\n              ? 140\n              : 'auto'\n          \"\n        >\n          <template slot-scope=\"{ row }\">\n            <!-- <el-divider direction=\"vertical\" /> -->\n            <div class=\"before\">\n              <el-button\n                v-if=\"row.status === 2\"\n                type=\"text\"\n                class=\"blueBug\"\n                @click=\"orderAccept(row), (isTableOperateBtn = true)\"\n              >\n                接单\n              </el-button>\n              <el-button\n                v-if=\"row.status === 3\"\n                type=\"text\"\n                class=\"blueBug\"\n                @click=\"cancelOrDeliveryOrComplete(3, row.id)\"\n              >\n                派送\n              </el-button>\n              <el-button\n                v-if=\"row.status === 4\"\n                type=\"text\"\n                class=\"blueBug\"\n                @click=\"cancelOrDeliveryOrComplete(4, row.id)\"\n              >\n                完成\n              </el-button>\n            </div>\n            <div class=\"middle\">\n              <el-button\n                v-if=\"row.status === 2\"\n                type=\"text\"\n                class=\"delBut\"\n                @click=\"orderReject(row), (isTableOperateBtn = true)\"\n              >\n                拒单\n              </el-button>\n              <el-button\n                v-if=\"[1, 3, 4, 5].includes(row.status)\"\n                type=\"text\"\n                class=\"delBut\"\n                @click=\"cancelOrder(row)\"\n              >\n                取消\n              </el-button>\n            </div>\n            <div class=\"after\">\n              <el-button\n                type=\"text\"\n                class=\"blueBug non\"\n                @click=\"goDetail(row.id, row.status, row)\"\n              >\n                查看\n              </el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n      <Empty v-else :is-search=\"isSearch\" />\n      <el-pagination\n        v-if=\"counts > 10\"\n        class=\"pageList\"\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"counts\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n\n    <!-- 查看弹框部分 -->\n    <el-dialog\n      title=\"订单信息\"\n      :visible.sync=\"dialogVisible\"\n      width=\"53%\"\n      :before-close=\"handleClose\"\n      class=\"order-dialog\"\n    >\n      <el-scrollbar style=\"height: 100%\">\n        <div class=\"order-top\">\n          <div>\n            <div style=\"display: inline-block\">\n              <label style=\"font-size: 16px\">订单号：</label>\n              <div class=\"order-num\">\n                {{ diaForm.number }}\n              </div>\n            </div>\n            <div\n              style=\"display: inline-block\"\n              class=\"order-status\"\n              :class=\"{ status3: [3, 4].includes(dialogOrderStatus) }\"\n            >\n              {{\n                orderList.filter((item) => item.value === dialogOrderStatus)[0]\n                  .label\n              }}\n            </div>\n          </div>\n          <p><label>下单时间：</label>{{ diaForm.orderTime }}</p>\n        </div>\n\n        <div class=\"order-middle\">\n          <div class=\"user-info\">\n            <div class=\"user-info-box\">\n              <div class=\"user-name\">\n                <label>用户名：</label>\n                <span>{{ diaForm.consignee }}</span>\n              </div>\n              <div class=\"user-phone\">\n                <label>手机号：</label>\n                <span>{{ diaForm.phone }}</span>\n              </div>\n              <div\n                v-if=\"[2, 3, 4, 5].includes(dialogOrderStatus)\"\n                class=\"user-getTime\"\n              >\n                <label>{{\n                  dialogOrderStatus === 5 ? '送达时间：' : '预计送达时间：'\n                }}</label>\n                <span>{{\n                  dialogOrderStatus === 5\n                    ? diaForm.deliveryTime\n                    : diaForm.estimatedDeliveryTime\n                }}</span>\n              </div>\n              <div class=\"user-address\">\n                <label>地址：</label>\n                <span>{{ diaForm.address }}</span>\n              </div>\n            </div>\n            <div\n              class=\"user-remark\"\n              :class=\"{ orderCancel: dialogOrderStatus === 6 }\"\n            >\n              <div>{{ dialogOrderStatus === 6 ? '取消原因' : '备注' }}</div>\n              <span>{{\n                dialogOrderStatus === 6\n                  ? diaForm.cancelReason || diaForm.rejectionReason\n                  : diaForm.remark\n              }}</span>\n            </div>\n          </div>\n\n          <div class=\"dish-info\">\n            <div class=\"dish-label\">菜品</div>\n            <div class=\"dish-list\">\n              <div\n                v-for=\"(item, index) in diaForm.orderDetailList\"\n                :key=\"index\"\n                class=\"dish-item\"\n              >\n                <div class=\"dish-item-box\">\n                  <span class=\"dish-name\">{{ item.name }}</span>\n                  <span class=\"dish-num\">x{{ item.number }}</span>\n                </div>\n                <span class=\"dish-price\"\n                  >￥{{ item.amount ? item.amount.toFixed(2) : '' }}</span\n                >\n              </div>\n            </div>\n            <div class=\"dish-all-amount\">\n              <label>菜品小计</label>\n              <span\n                >￥{{\n                  (diaForm.amount - 6 - diaForm.packAmount).toFixed(2)\n                }}</span\n              >\n            </div>\n          </div>\n        </div>\n\n        <div class=\"order-bottom\">\n          <div class=\"amount-info\">\n            <div class=\"amount-label\">费用</div>\n            <div class=\"amount-list\">\n              <div class=\"dish-amount\">\n                <span class=\"amount-name\">菜品小计：</span>\n                <span class=\"amount-price\"\n                  >￥{{\n                    ((diaForm.amount - 6 - diaForm.packAmount).toFixed(2) *\n                      100) /\n                    100\n                  }}</span\n                >\n              </div>\n              <div class=\"send-amount\">\n                <span class=\"amount-name\">派送费：</span>\n                <span class=\"amount-price\">￥{{ 6 }}</span>\n              </div>\n              <div class=\"package-amount\">\n                <span class=\"amount-name\">打包费：</span>\n                <span class=\"amount-price\"\n                  >￥{{\n                    diaForm.packAmount\n                      ? (diaForm.packAmount.toFixed(2) * 100) / 100\n                      : ''\n                  }}</span\n                >\n              </div>\n              <div class=\"all-amount\">\n                <span class=\"amount-name\">合计：</span>\n                <span class=\"amount-price\"\n                  >￥{{\n                    diaForm.amount\n                      ? (diaForm.amount.toFixed(2) * 100) / 100\n                      : ''\n                  }}</span\n                >\n              </div>\n              <div class=\"pay-type\">\n                <span class=\"pay-name\">支付渠道：</span>\n                <span class=\"pay-value\">{{\n                  diaForm.payMethod === 1 ? '微信支付' : '支付宝支付'\n                }}</span>\n              </div>\n              <div class=\"pay-time\">\n                <span class=\"pay-name\">支付时间：</span>\n                <span class=\"pay-value\">{{ diaForm.checkoutTime }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-scrollbar>\n      <span v-if=\"dialogOrderStatus !== 6\" slot=\"footer\" class=\"dialog-footer\">\n        <el-checkbox\n          v-if=\"dialogOrderStatus === 2 && orderStatus === 2\"\n          v-model=\"isAutoNext\"\n          >处理完自动跳转下一条</el-checkbox\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 2\"\n          @click=\"orderReject(row), (isTableOperateBtn = false)\"\n          >拒 单</el-button\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 2\"\n          type=\"primary\"\n          @click=\"orderAccept(row), (isTableOperateBtn = false)\"\n          >接 单</el-button\n        >\n\n        <el-button\n          v-if=\"[1, 3, 4, 5].includes(dialogOrderStatus)\"\n          @click=\"dialogVisible = false\"\n          >返 回</el-button\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 3\"\n          type=\"primary\"\n          @click=\"cancelOrDeliveryOrComplete(3, row.id)\"\n          >派 送</el-button\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 4\"\n          type=\"primary\"\n          @click=\"cancelOrDeliveryOrComplete(4, row.id)\"\n          >完 成</el-button\n        >\n        <el-button\n          v-if=\"[1].includes(dialogOrderStatus)\"\n          type=\"primary\"\n          @click=\"cancelOrder(row)\"\n          >取消订单</el-button\n        >\n      </span>\n    </el-dialog>\n    <!-- 拒单，取消弹窗 -->\n    <el-dialog\n      :title=\"cancelDialogTitle + '原因'\"\n      :visible.sync=\"cancelDialogVisible\"\n      width=\"42%\"\n      :before-close=\"() => ((cancelDialogVisible = false), (cancelReason = ''))\"\n      class=\"cancelDialog\"\n    >\n      <el-form label-width=\"90px\">\n        <el-form-item :label=\"cancelDialogTitle + '原因：'\">\n          <el-select\n            v-model=\"cancelReason\"\n            :placeholder=\"'请选择' + cancelDialogTitle + '原因'\"\n          >\n            <el-option\n              v-for=\"(item, index) in cancelDialogTitle === '取消'\n                ? cancelrReasonList\n                : cancelOrderReasonList\"\n              :key=\"index\"\n              :label=\"item.label\"\n              :value=\"item.label\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"cancelReason === '自定义原因'\" label=\"原因：\">\n          <el-input\n            v-model.trim=\"remark\"\n            type=\"textarea\"\n            :placeholder=\"'请填写您' + cancelDialogTitle + '的原因（限20字内）'\"\n            maxlength=\"20\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\";(cancelDialogVisible = false), (cancelReason = '')\"\n          >取 消</el-button\n        >\n        <el-button type=\"primary\" @click=\"confirmCancel\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue } from 'vue-property-decorator'\nimport HeadLable from '@/components/HeadLable/index.vue'\nimport InputAutoComplete from '@/components/InputAutoComplete/index.vue'\nimport TabChange from './tabChange.vue'\nimport Empty from '@/components/Empty/index.vue'\nimport {\n  getOrderDetailPage,\n  queryOrderDetailById,\n  completeOrder,\n  deliveryOrder,\n  orderCancel,\n  orderReject,\n  orderAccept,\n  getOrderListBy,\n} from '@/api/order'\n\n@Component({\n  components: {\n    HeadLable,\n    InputAutoComplete,\n    TabChange,\n    Empty,\n  },\n})\nexport default class extends Vue {\n  private defaultActivity: any = 0\n  private orderStatics = {}\n  private row = {}\n  private isAutoNext = true\n  private isTableOperateBtn = true\n  private currentPageIndex = 0 //记录查看详情数据的index\n  private orderId = '' //订单号\n  private input = '' //搜索条件的订单号\n  private phone = '' //搜索条件的手机号\n  private valueTime = []\n  private dialogVisible = false //详情弹窗\n  private cancelDialogVisible = false //取消，拒单弹窗\n  private cancelDialogTitle = '' //取消，拒绝弹窗标题\n  private cancelReason = ''\n  private remark = '' //自定义原因\n  private counts: number = 0\n  private page: number = 1\n  private pageSize: number = 10\n  private tableData = []\n  private diaForm = []\n  private isSearch: boolean = false\n  private orderStatus = 0 //列表字段展示所需订单状态,用于分页请求数据\n  private dialogOrderStatus = 0 //弹窗所需订单状态，用于详情展示字段\n  private cancelOrderReasonList = [\n    {\n      value: 1,\n      label: '订单量较多，暂时无法接单',\n    },\n    {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单',\n    },\n    {\n      value: 3,\n      label: '餐厅已打烊，暂时无法接单',\n    },\n    {\n      value: 0,\n      label: '自定义原因',\n    },\n  ]\n\n  private cancelrReasonList = [\n    {\n      value: 1,\n      label: '订单量较多，暂时无法接单',\n    },\n    {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单',\n    },\n    {\n      value: 3,\n      label: '骑手不足无法配送',\n    },\n    {\n      value: 4,\n      label: '客户电话取消',\n    },\n    {\n      value: 0,\n      label: '自定义原因',\n    },\n  ]\n  private orderList = [\n    {\n      label: '全部订单',\n      value: 0,\n    },\n    {\n      label: '待付款',\n      value: 1,\n    },\n    {\n      label: '待接单',\n      value: 2,\n    },\n    {\n      label: '待派送',\n      value: 3,\n    },\n    {\n      label: '派送中',\n      value: 4,\n    },\n    {\n      label: '已完成',\n      value: 5,\n    },\n    {\n      label: '已取消',\n      value: 6,\n    },\n  ]\n\n  created() {\n    this.init(Number(this.$route.query.status) || 0)\n  }\n\n  mounted() {\n    //如果有值说明是消息通知点击进来的\n    if (\n      this.$route.query.orderId &&\n      this.$route.query.orderId !== 'undefined'\n    ) {\n      this.goDetail(this.$route.query.orderId, 2)\n    }\n    if (this.$route.query.status) {\n      this.defaultActivity = this.$route.query.status\n    }\n    // console.log(this.$route.query, 'this.$route')\n  }\n\n  initFun(orderStatus) {\n    this.page = 1\n    this.init(orderStatus)\n  }\n\n  change(activeIndex) {\n    if (activeIndex === this.orderStatus) return\n    this.init(activeIndex)\n    this.input = ''\n    this.phone = ''\n    this.valueTime = []\n    this.dialogOrderStatus = 0\n    this.$router.push('/order')\n    console.log(activeIndex, '接收到了子组件的index')\n  }\n\n  //获取待处理，待派送，派送中数量\n  getOrderListBy3Status() {\n    getOrderListBy({})\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.orderStatics = res.data.data\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  init(activeIndex: number = 0, isSearch?) {\n    this.isSearch = isSearch\n    const params = {\n      page: this.page,\n      pageSize: this.pageSize,\n      number: this.input || undefined,\n      phone: this.phone || undefined,\n      beginTime:\n        this.valueTime && this.valueTime.length > 0\n          ? this.valueTime[0]\n          : undefined,\n      endTime:\n        this.valueTime && this.valueTime.length > 0\n          ? this.valueTime[1]\n          : undefined,\n      status: activeIndex || undefined,\n    }\n    getOrderDetailPage({ ...params })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.tableData = res.data.data.records\n          this.orderStatus = activeIndex\n          this.counts = Number(res.data.data.total)\n          this.getOrderListBy3Status()\n          if (\n            this.dialogOrderStatus === 2 &&\n            this.orderStatus === 2 &&\n            this.isAutoNext &&\n            !this.isTableOperateBtn &&\n            res.data.data.records.length > 1\n          ) {\n            const row = res.data.data.records[0]\n            this.goDetail(row.id, row.status, row)\n          } else {\n            return null\n          }\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  getOrderType(row: any) {\n    if (row.status === 1) {\n      return '待付款'\n    } else if (row.status === 2) {\n      return '待接单'\n    } else if (row.status === 3) {\n      return '待派送'\n    } else if (row.status === 4) {\n      return '派送中'\n    } else if (row.status === 5) {\n      return '已完成'\n    } else if (row.status === 6) {\n      return '已取消'\n    } else {\n      return '退款'\n    }\n  }\n\n  // 查看详情\n  async goDetail(id: any, status: number, row?: any) {\n    // console.log(111, index, row)\n    this.diaForm = []\n    this.dialogVisible = true\n    this.dialogOrderStatus = status\n    this.orderId = id\n    const { data } = await queryOrderDetailById({ orderId: id })\n    this.diaForm = data.data\n    this.row = row || { id: this.$route.query.orderId, status: status }\n    if (this.$route.query.orderId) {\n      this.$router.push('/order')\n    }\n  }\n\n  //打开拒单弹窗\n  orderReject(row: any) {\n    this.cancelDialogVisible = true\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    this.cancelDialogTitle = '拒绝'\n    this.dialogVisible = false\n    this.cancelReason = ''\n  }\n\n  //接单\n  orderAccept(row: any) {\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    orderAccept({ id: this.orderId })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.dialogVisible = false\n          this.init(this.orderStatus)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  //打开取消订单弹窗\n  cancelOrder(row: any) {\n    this.cancelDialogVisible = true\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    this.cancelDialogTitle = '取消'\n    this.dialogVisible = false\n    this.cancelReason = ''\n  }\n\n  //确认取消或拒绝订单并填写原因\n  confirmCancel(type) {\n    if (!this.cancelReason) {\n      return this.$message.error(`请选择${this.cancelDialogTitle}原因`)\n    } else if (this.cancelReason === '自定义原因' && !this.remark) {\n      return this.$message.error(`请输入${this.cancelDialogTitle}原因`)\n    }\n\n    ;(this.cancelDialogTitle === '取消' ? orderCancel : orderReject)({\n      id: this.orderId,\n      // eslint-disable-next-line standard/computed-property-even-spacing\n      [this.cancelDialogTitle === '取消' ? 'cancelReason' : 'rejectionReason']:\n        this.cancelReason === '自定义原因' ? this.remark : this.cancelReason,\n    })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.cancelDialogVisible = false\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.init(this.orderStatus)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  // 派送，完成\n  cancelOrDeliveryOrComplete(status: number, id: string) {\n    const params = {\n      status,\n      id,\n    }\n    ;(status === 3 ? deliveryOrder : completeOrder)(params)\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.dialogVisible = false\n          this.init(this.orderStatus)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  handleClose() {\n    this.dialogVisible = false\n  }\n\n  private handleSizeChange(val: any) {\n    this.pageSize = val\n    this.init(this.orderStatus)\n  }\n\n  private handleCurrentChange(val: any) {\n    this.page = val\n    this.init(this.orderStatus)\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard {\n  &-container {\n    margin: 30px;\n    // height: 100%;\n    min-height: 700px;\n    .container {\n      background: #fff;\n      position: relative;\n      z-index: 1;\n      padding: 30px 28px;\n      border-radius: 4px;\n      // min-height: 650px;\n      height: calc(100% - 55px);\n\n      .tableBar {\n        // display: flex;\n        margin-bottom: 20px;\n        justify-content: space-between;\n\n        .tableLab {\n          span {\n            cursor: pointer;\n            display: inline-block;\n            font-size: 14px;\n            padding: 0 20px;\n            color: $gray-2;\n            border-right: solid 1px $gray-4;\n          }\n        }\n      }\n\n      .tableBox {\n        width: 100%;\n        border: 1px solid $gray-5;\n        border-bottom: 0;\n      }\n\n      .pageList {\n        text-align: center;\n        margin-top: 30px;\n      }\n      //查询黑色按钮样式\n      .normal-btn {\n        background: #333333;\n        color: white;\n        margin-left: 20px;\n      }\n    }\n    .hContainer {\n      height: auto !important;\n    }\n  }\n}\n\n.search-btn {\n  margin-left: 20px;\n}\n\n.info-box {\n  margin: -15px -44px 20px;\n  p {\n    display: flex;\n    height: 20px;\n    line-height: 20px;\n    font-size: 14px;\n    font-weight: 400;\n    color: #666666;\n    text-align: left;\n    margin-bottom: 14px;\n    &:last-child {\n      margin-bottom: 0;\n    }\n    label {\n      width: 100px;\n      display: inline-block;\n      color: #666;\n    }\n    .des {\n      flex: 1;\n      color: #333333;\n    }\n  }\n}\n\n.order-top {\n  // height: 80px;\n  border-bottom: 1px solid #e7e6e6;\n  padding-bottom: 26px;\n  padding-left: 22px;\n  padding-right: 22px;\n  // margin: 0 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  .order-status {\n    width: 57.25px;\n    height: 27px;\n    background: #333333;\n    border-radius: 13.5px;\n    color: white;\n    margin-left: 19px;\n    text-align: center;\n    line-height: 27px;\n  }\n  .status3 {\n    background: #f56c6c;\n  }\n  p {\n    color: #333;\n    label {\n      color: #666;\n    }\n  }\n  .order-num {\n    font-size: 16px;\n    color: #2a2929;\n    font-weight: bold;\n    display: inline-block;\n  }\n}\n\n.order-middle {\n  .user-info {\n    min-height: 140px;\n    background: #fbfbfa;\n    margin-top: 23px;\n\n    padding: 20px 43px;\n    color: #333;\n    .user-info-box {\n      min-height: 55px;\n      display: flex;\n      flex-wrap: wrap;\n      .user-name {\n        flex: 67%;\n      }\n      .user-phone {\n        flex: 33%;\n      }\n      .user-getTime {\n        margin-top: 14px;\n        flex: 80%;\n        label {\n          margin-right: 3px;\n        }\n      }\n      label {\n        margin-right: 17px;\n        color: #666;\n      }\n\n      .user-address {\n        margin-top: 14px;\n        flex: 80%;\n        label {\n          margin-right: 30px;\n        }\n      }\n    }\n    .user-remark {\n      min-height: 43px;\n      line-height: 43px;\n      background: #fffbf0;\n      border: 1px solid #fbe396;\n      border-radius: 4px;\n      margin-top: 10px;\n      padding: 6px;\n      display: flex;\n      align-items: center;\n      div {\n        display: inline-block;\n        min-width: 53px;\n        height: 32px;\n        background: #fbe396;\n        border-radius: 4px;\n        text-align: center;\n        line-height: 32px;\n        color: #333;\n        margin-right: 30px;\n        // padding: 12px 6px;\n      }\n      span {\n        color: #f2a402;\n        line-height: 1.15;\n      }\n    }\n    .orderCancel {\n      background: #ffffff;\n      border: 1px solid #b6b6b6;\n\n      div {\n        padding: 0 10px;\n        background-color: #e5e4e4;\n      }\n      span {\n        color: #f56c6c;\n      }\n    }\n  }\n  .dish-info {\n    // min-height: 180px;\n    display: flex;\n    flex-wrap: wrap;\n    padding: 20px 40px;\n    border-bottom: 1px solid #e7e6e6;\n    .dish-label {\n      color: #666;\n    }\n    .dish-list {\n      flex: 80%;\n      display: flex;\n      flex-wrap: wrap;\n      .dish-item {\n        flex: 50%;\n        margin-bottom: 14px;\n        color: #333;\n        .dish-num {\n        }\n        .dish-item-box {\n          display: inline-block;\n          width: 120px;\n        }\n      }\n    }\n    .dish-label {\n      margin-right: 65px;\n    }\n    .dish-all-amount {\n      flex: 1;\n      padding-left: 92px;\n      margin-top: 10px;\n      label {\n        color: #333333;\n        font-weight: bold;\n        margin-right: 5px;\n      }\n      span {\n        color: #f56c6c;\n      }\n    }\n  }\n}\n.order-bottom {\n  .amount-info {\n    // min-height: 180px;\n    display: flex;\n    flex-wrap: wrap;\n    padding: 20px 40px;\n    padding-bottom: 0px;\n    .amount-label {\n      color: #666;\n      margin-right: 65px;\n    }\n    .amount-list {\n      flex: 80%;\n      display: flex;\n      flex-wrap: wrap;\n      color: #333;\n      // height: 65px;\n      .dish-amount,\n      .package-amount,\n      .pay-type {\n        display: inline-block;\n        width: 300px;\n        margin-bottom: 14px;\n        flex: 50%;\n      }\n      .send-amount,\n      .all-amount,\n      .pay-time {\n        display: inline-block;\n        flex: 50%;\n        padding-left: 10%;\n      }\n      .package-amount {\n        .amount-name {\n          margin-right: 14px;\n        }\n      }\n      .all-amount {\n        .amount-name {\n          margin-right: 24px;\n        }\n        .amount-price {\n          color: #f56c6c;\n        }\n      }\n      .send-amount {\n        .amount-name {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n.dashboard-container {\n  .cancelReason {\n    padding-left: 40px;\n  }\n  .cancelTime {\n    padding-left: 50px;\n  }\n  .orderTime {\n    padding-left: 50px;\n  }\n  td.operate .cell {\n    .before,\n    .middle,\n    .after {\n      height: 39px;\n      width: 48px;\n    }\n  }\n  td.operate .cell,\n  td.otherOperate .cell {\n    display: flex;\n    flex-wrap: nowrap;\n    justify-content: center;\n  }\n  .order-dialog {\n    .el-dialog {\n      max-height: 764px !important;\n      display: flex;\n      flex-direction: column;\n      margin: 0 !important;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      max-height: calc(100% - 30px);\n      max-width: calc(100% - 30px);\n    }\n    .el-dialog__body {\n      height: 520px !important;\n    }\n  }\n}\n.el-dialog__body {\n  padding-top: 34px;\n  padding-left: 30px;\n  padding-right: 30px;\n}\n.cancelDialog {\n  .el-dialog__body {\n    padding-left: 64px;\n  }\n  .el-select,\n  .el-textarea {\n    width: 293px;\n  }\n  .el-textarea textarea {\n    height: 114px;\n  }\n}\n.el-dialog__footer {\n  .el-checkbox {\n    float: left;\n    margin-left: 40px;\n  }\n  .el-checkbox__label {\n    color: #333333 !important;\n  }\n}\n.empty-box {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  img {\n    margin-top: 0 !important;\n  }\n}\n</style>\n"]}]}