{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/addDishtype.vue?vue&type=template&id=d1f3ea8c&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/addDishtype.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div :key=\"vueRest\"\n     class=\"addBrand-container\">\n  <div :key=\"restKey\"\n       class=\"container\">\n    <el-form ref=\"ruleForm\"\n             :model=\"ruleForm\"\n             :rules=\"rules\"\n             :inline=\"true\"\n             label-width=\"180px\"\n             class=\"demo-ruleForm\">\n      <div>\n        <el-form-item label=\"菜品名称:\"\n                      prop=\"name\">\n          <el-input v-model=\"ruleForm.name\"\n                    placeholder=\"请填写菜品名称\"\n                    maxlength=\"20\" />\n        </el-form-item>\n        <el-form-item label=\"菜品分类:\"\n                      prop=\"categoryId\">\n          <el-select v-model=\"ruleForm.categoryId\"\n                     placeholder=\"请选择菜品分类\">\n            <el-option v-for=\"(item, index) in dishList\"\n                       :key=\"index\"\n                       :label=\"item.name\"\n                       :value=\"item.id\" />\n          </el-select>\n        </el-form-item>\n      </div>\n      <div>\n        <el-form-item label=\"菜品价格:\"\n                      prop=\"price\">\n          <el-input v-model=\"ruleForm.price\"\n                    placeholder=\"请设置菜品价格\" />\n        </el-form-item>\n      </div>\n      <el-form-item label=\"口味做法配置:\">\n        <el-form-item>\n          <div class=\"flavorBox\">\n            <span v-if=\"dishFlavors.length == 0\"\n                  class=\"addBut\"\n                  @click=\"addFlavore\">\n              + 添加口味</span>\n            <div v-if=\"dishFlavors.length != 0\"\n                 class=\"flavor\">\n              <div class=\"title\">\n                <span>口味名（3个字内）</span>\n                <!-- <span class=\"des-box\">口味标签（输入标签回车添加）</span> -->\n              </div>\n              <div class=\"cont\">\n                <div v-for=\"(item, index) in dishFlavors\"\n                     :key=\"index\"\n                     class=\"items\">\n                  <div class=\"itTit\">\n                    <!-- :dish-flavors-data=\"filterDishFlavorsData()\" -->\n                    <SelectInput :dish-flavors-data=\"leftDishFlavors\"\n                                 :index=\"index\"\n                                 :value=\"item.name\"\n                                 @select=\"selectHandle\" />\n                  </div>\n                  <div class=\"labItems\"\n                       style=\"display: flex\">\n                    <span v-for=\"(it, ind) in item.value\"\n                          :key=\"ind\">{{ it }}\n                      <i @click=\"delFlavorLabel(index, ind)\">X</i></span>\n                    <div class=\"inputBox\"\n                         :style=\"inputStyle\" />\n                  </div>\n                  <span class=\"delFlavor delBut non\"\n                        @click=\"delFlavor(item.name)\">删除</span>\n                </div>\n              </div>\n              <div v-if=\"\n                     !!this.leftDishFlavors.length &&\n                       this.dishFlavors.length < this.dishFlavorsData.length\n                   \"\n                   class=\"addBut\"\n                   @click=\"addFlavore\">\n                添加口味\n              </div>\n            </div>\n          </div>\n        </el-form-item>\n      </el-form-item>\n      <div>\n        <el-form-item label=\"菜品图片:\"\n                      prop=\"image\">\n          <image-upload :prop-image-url=\"imageUrl\"\n                        @imageChange=\"imageChange\">\n            图片大小不超过2M<br>仅能上传 PNG JPEG JPG类型图片<br>建议上传200*200或300*300尺寸的图片\n          </image-upload>\n        </el-form-item>\n      </div>\n      <div class=\"address\">\n        <el-form-item label=\"菜品描述:\"\n                      prop=\"region\">\n          <el-input v-model=\"ruleForm.description\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    maxlength=\"200\"\n                    placeholder=\"菜品描述，最长200字\" />\n        </el-form-item>\n      </div>\n      <div class=\"subBox address\">\n        <el-button @click=\"() => $router.back()\">\n          取消\n        </el-button>\n        <el-button type=\"primary\"\n                   :class=\"{ continue: actionType === 'add' }\"\n                   @click=\"submitForm('ruleForm')\">\n          保存\n        </el-button>\n        <el-button v-if=\"actionType == 'add'\"\n                   type=\"primary\"\n                   @click=\"submitForm('ruleForm', 'goAnd')\">\n          保存并继续添加\n        </el-button>\n      </div>\n    </el-form>\n  </div>\n</div>\n", null]}