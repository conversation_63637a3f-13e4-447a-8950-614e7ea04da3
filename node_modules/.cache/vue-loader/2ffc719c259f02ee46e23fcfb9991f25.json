{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue?vue&type=style&index=1&id=2cc9af88&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderList.vue", "mtime": 1655712116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n.dashboard-container {\n  .cancelTime {\n    padding-left: 30px;\n  }\n  .orderTime {\n    padding-left: 30px;\n  }\n  td.operate .cell {\n    .before,\n    .middle,\n    .after {\n      height: 39px;\n      width: 48px;\n    }\n  }\n  td.operate .cell,\n  td.otherOperate .cell {\n    display: flex;\n    flex-wrap: nowrap;\n    justify-content: center;\n  }\n}\n", {"version": 3, "sources": ["orderList.vue"], "names": [], "mappings": ";AA+5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "orderList.vue", "sourceRoot": "src/views/dashboard/components", "sourcesContent": ["<template>\n  <div>\n    <div class=\"container homecon\">\n      <h2 class=\"homeTitle homeTitleBtn\">\n        订单信息\n        <ul class=\"conTab\">\n          <li\n            v-for=\"(item, index) in tabList\"\n            :key=\"index\"\n            :class=\"activeIndex === index ? 'active' : ''\"\n            @click=\"handleClass(index)\"\n          >\n            <el-badge\n              class=\"item\"\n              :class=\"item.num >= 10 ? 'badgeW' : ''\"\n              :value=\"item.num > 99 ? '99+' : item.num\"\n              :hidden=\"!([2, 3].includes(item.value) && item.num)\"\n              >{{ item.label }}</el-badge\n            >\n          </li>\n        </ul>\n      </h2>\n      <div class=\"\">\n        <div v-if=\"orderData.length > 0\">\n          <el-table\n            :data=\"orderData\"\n            stripe\n            class=\"tableBox\"\n            style=\"width: 100%\"\n            @row-click=\"handleTable\"\n          >\n            <el-table-column prop=\"number\" label=\"订单号\"> </el-table-column>\n            <el-table-column label=\"订单菜品\">\n              <template slot-scope=\"scope\">\n                <div class=\"ellipsisHidden\">\n                  <el-popover\n                    placement=\"top-start\"\n                    title=\"\"\n                    width=\"200\"\n                    trigger=\"hover\"\n                    :content=\"scope.row.orderDishes\"\n                  >\n                    <span slot=\"reference\">{{ scope.row.orderDishes }}</span>\n                  </el-popover>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"地址\"\n              :class-name=\"dialogOrderStatus === 2 ? 'address' : ''\"\n            >\n              <template slot-scope=\"scope\">\n                <div class=\"ellipsisHidden\">\n                  <el-popover\n                    placement=\"top-start\"\n                    title=\"\"\n                    width=\"200\"\n                    trigger=\"hover\"\n                    :content=\"scope.row.address\"\n                  >\n                    <span slot=\"reference\">{{ scope.row.address }}</span>\n                  </el-popover>\n                </div>\n              </template>\n            </el-table-column>\n\n            <el-table-column\n              prop=\"estimatedDeliveryTime\"\n              label=\"预计送达时间\"\n              sortable\n              class-name=\"orderTime\"\n              min-width=\"130\"\n            >\n            </el-table-column>\n            <el-table-column prop=\"amount\" label=\"实收金额\"> </el-table-column>\n            <el-table-column label=\"备注\">\n              <template slot-scope=\"scope\">\n                <div class=\"ellipsisHidden\">\n                  <el-popover\n                    placement=\"top-start\"\n                    title=\"\"\n                    width=\"200\"\n                    trigger=\"hover\"\n                    :content=\"scope.row.remark\"\n                  >\n                    <span slot=\"reference\">{{ scope.row.remark }}</span>\n                  </el-popover>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column\n              prop=\"tablewareNumber\"\n              label=\"餐具数量\"\n              min-width=\"80\"\n              align=\"center\"\n              v-if=\"status === 3\"\n            >\n            </el-table-column>\n            <el-table-column\n              label=\"操作\"\n              align=\"center\"\n              :class-name=\"dialogOrderStatus === 0 ? 'operate' : 'otherOperate'\"\n              :min-width=\"\n                [2, 3].includes(dialogOrderStatus)\n                  ? 130\n                  : [0].includes(dialogOrderStatus)\n                  ? 140\n                  : 'auto'\n              \"\n            >\n              <template slot-scope=\"{ row }\">\n                <!-- <el-divider direction=\"vertical\" /> -->\n                <div class=\"before\">\n                  <el-button\n                    v-if=\"row.status === 2\"\n                    type=\"text\"\n                    class=\"blueBug\"\n                    @click=\"\n                      orderAccept(row, $event), (isTableOperateBtn = true)\n                    \"\n                  >\n                    接单\n                  </el-button>\n                  <el-button\n                    v-if=\"row.status === 3\"\n                    type=\"text\"\n                    class=\"blueBug\"\n                    @click=\"cancelOrDeliveryOrComplete(3, row.id, $event)\"\n                  >\n                    派送\n                  </el-button>\n                </div>\n                <div class=\"middle\">\n                  <el-button\n                    v-if=\"row.status === 2\"\n                    type=\"text\"\n                    class=\"delBut\"\n                    @click=\"\n                      orderReject(row, $event), (isTableOperateBtn = true)\n                    \"\n                  >\n                    拒单\n                  </el-button>\n                  <el-button\n                    v-if=\"[1, 3, 4, 5].includes(row.status)\"\n                    type=\"text\"\n                    class=\"delBut\"\n                    @click=\"cancelOrder(row, $event)\"\n                  >\n                    取消\n                  </el-button>\n                </div>\n                <div class=\"after\">\n                  <el-button\n                    type=\"text\"\n                    class=\"blueBug non\"\n                    @click=\"goDetail(row.id, row.status, row, $event)\"\n                  >\n                    查看\n                  </el-button>\n                </div>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <Empty v-else :is-search=\"isSearch\" />\n        <el-pagination\n          v-if=\"counts > 10\"\n          class=\"pageList\"\n          :page-sizes=\"[10, 20, 30, 40]\"\n          :page-size=\"pageSize\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"counts\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </div>\n    <!-- 查看弹框部分 -->\n    <el-dialog\n      title=\"订单信息\"\n      :visible.sync=\"dialogVisible\"\n      width=\"53%\"\n      :before-close=\"handleClose\"\n      class=\"order-dialog\"\n    >\n      <el-scrollbar style=\"height: 100%\">\n        <div class=\"order-top\">\n          <div>\n            <div style=\"display: inline-block\">\n              <label style=\"font-size: 16px\">订单号：</label>\n              <div class=\"order-num\">\n                {{ diaForm.number }}\n              </div>\n            </div>\n            <div\n              style=\"display: inline-block\"\n              class=\"order-status\"\n              :class=\"{ status3: [3, 4].includes(dialogOrderStatus) }\"\n            >\n              {{\n                orderList.filter((item) => item.value === dialogOrderStatus)[0]\n                  .label\n              }}\n            </div>\n          </div>\n          <p><label>下单时间：</label>{{ diaForm.orderTime }}</p>\n        </div>\n\n        <div class=\"order-middle\">\n          <div class=\"user-info\">\n            <div class=\"user-info-box\">\n              <div class=\"user-name\">\n                <label>用户名：</label>\n                <span>{{ diaForm.consignee }}</span>\n              </div>\n              <div class=\"user-phone\">\n                <label>手机号：</label>\n                <span>{{ diaForm.phone }}</span>\n              </div>\n              <div\n                v-if=\"[2, 3, 4, 5].includes(dialogOrderStatus)\"\n                class=\"user-getTime\"\n              >\n                <label>{{\n                  dialogOrderStatus === 5 ? '送达时间：' : '预计送达时间：'\n                }}</label>\n                <span>{{\n                  dialogOrderStatus === 5\n                    ? diaForm.deliveryTime\n                    : diaForm.estimatedDeliveryTime\n                }}</span>\n              </div>\n              <div class=\"user-address\">\n                <label>地址：</label>\n                <span>{{ diaForm.address }}</span>\n              </div>\n            </div>\n            <div\n              class=\"user-remark\"\n              :class=\"{ orderCancel: dialogOrderStatus === 6 }\"\n            >\n              <div>{{ dialogOrderStatus === 6 ? '取消原因' : '备注' }}</div>\n              <span>{{\n                dialogOrderStatus === 6\n                  ? diaForm.cancelReason || diaForm.rejectionReason\n                  : diaForm.remark\n              }}</span>\n            </div>\n          </div>\n\n          <div class=\"dish-info\">\n            <div class=\"dish-label\">菜品</div>\n            <div class=\"dish-list\">\n              <div\n                v-for=\"(item, index) in diaForm.orderDetailList\"\n                :key=\"index\"\n                class=\"dish-item\"\n              >\n                <span class=\"dish-name\">{{ item.name }}</span>\n                <span class=\"dish-num\">x{{ item.number }}</span>\n                <span class=\"dish-price\"\n                  >￥{{ item.amount ? item.amount.toFixed(2) : '' }}</span\n                >\n              </div>\n            </div>\n            <div class=\"dish-all-amount\">\n              <label>菜品小计</label>\n              <span\n                >￥{{\n                  (diaForm.amount - 6 - diaForm.packAmount).toFixed(2)\n                }}</span\n              >\n            </div>\n          </div>\n        </div>\n\n        <div class=\"order-bottom\">\n          <div class=\"amount-info\">\n            <div class=\"amount-label\">费用</div>\n            <div class=\"amount-list\">\n              <div class=\"dish-amount\">\n                <span class=\"amount-name\">菜品小计：</span>\n                <span class=\"amount-price\"\n                  >￥{{\n                    ((diaForm.amount - 6 - diaForm.packAmount).toFixed(2) *\n                      100) /\n                    100\n                  }}</span\n                >\n              </div>\n              <div class=\"send-amount\">\n                <span class=\"amount-name\">派送费：</span>\n                <span class=\"amount-price\">￥{{ 6 }}</span>\n              </div>\n              <div class=\"package-amount\">\n                <span class=\"amount-name\">打包费：</span>\n                <span class=\"amount-price\"\n                  >￥{{\n                    diaForm.packAmount\n                      ? (diaForm.packAmount.toFixed(2) * 100) / 100\n                      : ''\n                  }}</span\n                >\n              </div>\n              <div class=\"all-amount\">\n                <span class=\"amount-name\">合计：</span>\n                <span class=\"amount-price\"\n                  >￥{{\n                    diaForm.amount\n                      ? (diaForm.amount.toFixed(2) * 100) / 100\n                      : ''\n                  }}</span\n                >\n              </div>\n              <div class=\"pay-type\">\n                <span class=\"pay-name\">支付渠道：</span>\n                <span class=\"pay-value\">{{\n                  diaForm.payMethod === 1 ? '微信支付' : '支付宝支付'\n                }}</span>\n              </div>\n              <div class=\"pay-time\">\n                <span class=\"pay-name\">支付时间：</span>\n                <span class=\"pay-value\">{{ diaForm.checkoutTime }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-scrollbar>\n      <span v-if=\"dialogOrderStatus !== 6\" slot=\"footer\" class=\"dialog-footer\">\n        <el-checkbox\n          v-if=\"dialogOrderStatus === 2 && status === 2\"\n          v-model=\"isAutoNext\"\n          >处理完自动跳转下一条</el-checkbox\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 2\"\n          @click=\"orderReject(row, $event), (isTableOperateBtn = false)\"\n          >拒 单</el-button\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 2\"\n          type=\"primary\"\n          @click=\"orderAccept(row, $event), (isTableOperateBtn = false)\"\n          >接 单</el-button\n        >\n\n        <el-button\n          v-if=\"[1, 3, 4, 5].includes(dialogOrderStatus)\"\n          @click=\"dialogVisible = false\"\n          >返 回</el-button\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 3\"\n          type=\"primary\"\n          @click=\"cancelOrDeliveryOrComplete(3, row.id, $event)\"\n          >派 送</el-button\n        >\n        <el-button\n          v-if=\"dialogOrderStatus === 4\"\n          type=\"primary\"\n          @click=\"cancelOrDeliveryOrComplete(4, row.id, $event)\"\n          >完 成</el-button\n        >\n        <el-button\n          v-if=\"[1].includes(dialogOrderStatus)\"\n          type=\"primary\"\n          @click=\"cancelOrder(row, $event)\"\n          >取消订单</el-button\n        >\n      </span>\n    </el-dialog>\n    <!-- end -->\n    <!-- 拒单，取消弹窗 -->\n    <el-dialog\n      :title=\"cancelDialogTitle + '原因'\"\n      :visible.sync=\"cancelDialogVisible\"\n      width=\"42%\"\n      :before-close=\"() => ((cancelDialogVisible = false), (cancelReason = ''))\"\n      class=\"cancelDialog\"\n    >\n      <el-form label-width=\"90px\">\n        <el-form-item :label=\"cancelDialogTitle + '原因：'\">\n          <el-select\n            v-model=\"cancelReason\"\n            :placeholder=\"'请选择' + cancelDialogTitle + '原因'\"\n          >\n            <el-option\n              v-for=\"(item, index) in cancelDialogTitle === '取消'\n                ? cancelrReasonList\n                : cancelOrderReasonList\"\n              :key=\"index\"\n              :label=\"item.label\"\n              :value=\"item.label\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"cancelReason === '自定义原因'\" label=\"原因：\">\n          <el-input\n            v-model.trim=\"remark\"\n            type=\"textarea\"\n            :placeholder=\"'请填写您' + cancelDialogTitle + '的原因（限20字内）'\"\n            maxlength=\"20\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\";(cancelDialogVisible = false), (cancelReason = '')\"\n          >取 消</el-button\n        >\n        <el-button type=\"primary\" @click=\"confirmCancel\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <!-- end -->\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Prop } from 'vue-property-decorator'\nimport Empty from '@/components/Empty/index.vue'\nimport {\n  getOrderDetailPage,\n  queryOrderDetailById,\n  completeOrder,\n  deliveryOrder,\n  orderCancel,\n  orderReject,\n  orderAccept,\n  getOrderListBy,\n} from '@/api/order'\n@Component({\n  name: 'Orderview',\n  components: {\n    Empty,\n  },\n})\nexport default class extends Vue {\n  @Prop({ default: '' }) orderStatics!: any\n\n  private orderId = '' //订单号\n  private dialogOrderStatus = 0 //弹窗所需订单状态，用于详情展示字段\n  private activeIndex = 0\n\n  private dialogVisible = false //详情弹窗\n  private cancelDialogVisible = false //取消，拒单弹窗\n  private cancelDialogTitle = '' //取消，拒绝弹窗标题\n  private cancelReason = ''\n  private remark = '' //自定义原因\n  private diaForm = []\n  private row = {}\n  private isAutoNext = true\n  private isSearch: boolean = false\n  private counts = 0\n  private page: number = 1\n  private pageSize: number = 10\n  private status = 2\n  private orderData = []\n  private isTableOperateBtn = true\n  private cancelOrderReasonList = [\n    {\n      value: 1,\n      label: '订单量较多，暂时无法接单',\n    },\n    {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单',\n    },\n    {\n      value: 3,\n      label: '餐厅已打烊，暂时无法接单',\n    },\n    {\n      value: 0,\n      label: '自定义原因',\n    },\n  ]\n\n  private cancelrReasonList = [\n    {\n      value: 1,\n      label: '订单量较多，暂时无法接单',\n    },\n    {\n      value: 2,\n      label: '菜品已销售完，暂时无法接单',\n    },\n    {\n      value: 3,\n      label: '骑手不足无法配送',\n    },\n    {\n      value: 4,\n      label: '客户电话取消',\n    },\n    {\n      value: 0,\n      label: '自定义原因',\n    },\n  ]\n  private orderList = [\n    {\n      label: '全部订单',\n      value: 0,\n    },\n    {\n      label: '待付款',\n      value: 1,\n    },\n    {\n      label: '待接单',\n      value: 2,\n    },\n    {\n      label: '待派送',\n      value: 3,\n    },\n    {\n      label: '派送中',\n      value: 4,\n    },\n    {\n      label: '已完成',\n      value: 5,\n    },\n    {\n      label: '已取消',\n      value: 6,\n    },\n  ]\n  get tabList() {\n    return [\n      {\n        label: '待接单',\n        value: 2,\n        num: this.orderStatics.toBeConfirmed,\n      },\n      {\n        label: '待派送',\n        value: 3,\n        num: this.orderStatics.confirmed,\n      },\n    ]\n  }\n  created() {\n    this.getOrderListData(this.status)\n  }\n  // // 获取订单数据\n  async getOrderListData(status) {\n    const params = {\n      page: this.page,\n      pageSize: this.pageSize,\n      status: status,\n    }\n    const data = await getOrderDetailPage(params)\n    this.orderData = data.data.data.records\n    this.counts = data.data.data.total\n    this.$emit('getOrderListBy3Status')\n    if (\n      this.dialogOrderStatus === 2 &&\n      this.status === 2 &&\n      this.isAutoNext &&\n      !this.isTableOperateBtn &&\n      data.data.records.length > 1\n    ) {\n      const row = data.data.records[0]\n      this.goDetail(row.id, row.status, row, row)\n    } else {\n      return null\n    }\n  }\n\n  //接单\n  orderAccept(row: any, event) {\n    event.stopPropagation()\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    orderAccept({ id: this.orderId })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.dialogVisible = false\n          this.getOrderListData(this.status)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n  //打开取消订单弹窗\n  cancelOrder(row: any, event) {\n    event.stopPropagation()\n    this.cancelDialogVisible = true\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    this.cancelDialogTitle = '取消'\n    this.dialogVisible = false\n    this.cancelReason = ''\n  }\n  //打开拒单弹窗\n  orderReject(row: any, event) {\n    event.stopPropagation()\n    this.cancelDialogVisible = true\n    this.orderId = row.id\n    this.dialogOrderStatus = row.status\n    this.cancelDialogTitle = '拒绝'\n    this.dialogVisible = false\n    this.cancelReason = ''\n  }\n  //确认取消或拒绝订单并填写原因\n  confirmCancel(type) {\n    if (!this.cancelReason) {\n      return this.$message.error(`请选择${this.cancelDialogTitle}原因`)\n    } else if (this.cancelReason === '自定义原因' && !this.remark) {\n      return this.$message.error(`请输入${this.cancelDialogTitle}原因`)\n    }\n\n    ;(this.cancelDialogTitle === '取消' ? orderCancel : orderReject)({\n      id: this.orderId,\n      // eslint-disable-next-line standard/computed-property-even-spacing\n      [this.cancelDialogTitle === '取消' ? 'cancelReason' : 'rejectionReason']:\n        this.cancelReason === '自定义原因' ? this.remark : this.cancelReason,\n    })\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.cancelDialogVisible = false\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.getOrderListData(this.status)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n\n  // 派送，完成\n  cancelOrDeliveryOrComplete(status: number, id: string, event) {\n    event.stopPropagation()\n    const params = {\n      status,\n      id,\n    }\n    ;(status === 3 ? deliveryOrder : completeOrder)(params)\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.$message.success('操作成功')\n          this.orderId = ''\n          // this.dialogOrderStatus = 0\n          this.dialogVisible = false\n          this.getOrderListData(this.status)\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n  // 查看详情\n  async goDetail(id: any, status: number, row: any, event) {\n    event.stopPropagation()\n    // console.log(111, index, row)\n    this.diaForm = []\n    this.dialogVisible = true\n    this.dialogOrderStatus = status\n    const { data } = await queryOrderDetailById({ orderId: id })\n    this.diaForm = data.data\n    this.row = row\n  }\n  // 关闭弹层\n  handleClose() {\n    this.dialogVisible = false\n  }\n  // tab切换\n  handleClass(index) {\n    this.activeIndex = index\n    if (index === 0) {\n      this.status = 2\n      this.getOrderListData(2)\n    } else {\n      this.status = 3\n      this.getOrderListData(3)\n    }\n  }\n  // 触发table某一行\n  handleTable(row, column, event) {\n    event.stopPropagation()\n    this.goDetail(row.id, row.status, row, event)\n  }\n  // 分页\n  private handleSizeChange(val: any) {\n    this.pageSize = val\n    this.getOrderListData(this.status)\n  }\n\n  private handleCurrentChange(val: any) {\n    this.page = val\n    this.getOrderListData(this.status)\n  }\n}\n</script>\n<style  lang=\"scss\" scoped >\n.dashboard-container.home .homecon {\n  margin-bottom: 0;\n}\n.order-top {\n  // height: 80px;\n  border-bottom: 1px solid #e7e6e6;\n  padding-bottom: 26px;\n  padding-left: 22px;\n  padding-right: 22px;\n  // margin: 0 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  .order-status {\n    width: 57.25px;\n    height: 27px;\n    background: #333333;\n    border-radius: 13.5px;\n    color: white;\n    margin-left: 19px;\n    text-align: center;\n    line-height: 27px;\n  }\n  .status3 {\n    background: #f56c6c;\n  }\n  p {\n    color: #333;\n    label {\n      color: #666;\n    }\n  }\n  .order-num {\n    font-size: 16px;\n    color: #2a2929;\n    font-weight: bold;\n    display: inline-block;\n  }\n}\n\n.order-middle {\n  .user-info {\n    min-height: 140px;\n    background: #fbfbfa;\n    margin-top: 23px;\n\n    padding: 20px 43px;\n    color: #333;\n    .user-info-box {\n      min-height: 55px;\n      display: flex;\n      flex-wrap: wrap;\n      .user-name {\n        flex: 67%;\n      }\n      .user-phone {\n        flex: 33%;\n      }\n      .user-getTime {\n        margin-top: 14px;\n        flex: 80%;\n        label {\n          margin-right: 3px;\n        }\n      }\n      label {\n        margin-right: 17px;\n        color: #666;\n      }\n\n      .user-address {\n        margin-top: 14px;\n        flex: 80%;\n        label {\n          margin-right: 30px;\n        }\n      }\n    }\n    .user-remark {\n      height: 43px;\n      line-height: 43px;\n      background: #fffbf0;\n      border: 1px solid #fbe396;\n      border-radius: 4px;\n      margin-top: 10px;\n      padding: 6px;\n      display: flex;\n      align-items: center;\n      div {\n        display: inline-block;\n        min-width: 53px;\n        height: 32px;\n        background: #fbe396;\n        border-radius: 4px;\n        text-align: center;\n        line-height: 32px;\n        color: #333;\n        margin-right: 30px;\n        // padding: 12px 6px;\n      }\n      span {\n        color: #f2a402;\n      }\n    }\n    .orderCancel {\n      background: #ffffff;\n      border: 1px solid #b6b6b6;\n\n      div {\n        padding: 0 10px;\n        background-color: #e5e4e4;\n      }\n      span {\n        color: #f56c6c;\n      }\n    }\n  }\n  .dish-info {\n    // min-height: 180px;\n    display: flex;\n    flex-wrap: wrap;\n    padding: 20px 40px;\n    border-bottom: 1px solid #e7e6e6;\n    .dish-label {\n      color: #666;\n    }\n    .dish-list {\n      flex: 80%;\n      display: flex;\n      flex-wrap: wrap;\n      .dish-item {\n        flex: 50%;\n        margin-bottom: 14px;\n        color: #333;\n        .dish-num {\n          margin-right: 51px;\n        }\n      }\n      // .dish-item:nth-child(odd) {\n      //   flex: 60%;\n      // }\n      // .dish-item:nth-child(even) {\n      //   flex: 40%;\n      // }\n    }\n    .dish-label {\n      margin-right: 65px;\n    }\n    .dish-all-amount {\n      flex: 1;\n      padding-left: 92px;\n      margin-top: 10px;\n      label {\n        color: #333333;\n        font-weight: bold;\n        margin-right: 5px;\n      }\n      span {\n        color: #f56c6c;\n      }\n    }\n  }\n}\n.order-bottom {\n  .amount-info {\n    // min-height: 180px;\n    display: flex;\n    flex-wrap: wrap;\n    padding: 20px 40px;\n    padding-bottom: 0px;\n    .amount-label {\n      color: #666;\n      margin-right: 65px;\n    }\n    .amount-list {\n      flex: 80%;\n      display: flex;\n      flex-wrap: wrap;\n      color: #333;\n      // height: 65px;\n      .dish-amount,\n      .package-amount,\n      .pay-type {\n        display: inline-block;\n        width: 300px;\n        margin-bottom: 14px;\n        flex: 50%;\n      }\n      .send-amount,\n      .all-amount,\n      .pay-time {\n        display: inline-block;\n        flex: 50%;\n        padding-left: 10%;\n      }\n      .package-amount {\n        .amount-name {\n          margin-right: 14px;\n        }\n      }\n      .all-amount {\n        .amount-name {\n          margin-right: 24px;\n        }\n        .amount-price {\n          color: #f56c6c;\n        }\n      }\n      .send-amount {\n        .amount-name {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n}\n</style>\n<style  lang=\"scss\">\n.dashboard-container {\n  .cancelTime {\n    padding-left: 30px;\n  }\n  .orderTime {\n    padding-left: 30px;\n  }\n  td.operate .cell {\n    .before,\n    .middle,\n    .after {\n      height: 39px;\n      width: 48px;\n    }\n  }\n  td.operate .cell,\n  td.otherOperate .cell {\n    display: flex;\n    flex-wrap: nowrap;\n    justify-content: center;\n  }\n}\n</style>\n"]}]}