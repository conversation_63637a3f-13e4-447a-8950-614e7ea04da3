{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/addDishtype.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/addDishtype.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport HeadLable from '@/components/HeadLable/index.vue'\r\nimport SelectInput from './components/SelectInput.vue'\r\nimport ImageUpload from '@/components/ImgUpload/index.vue'\r\n// getFlavorList口味列表暂时不做 getDishTypeList\r\nimport {\r\n  queryDishById,\r\n  addDish,\r\n  editDish,\r\n  getCategoryList,\r\n  commonDownload\r\n} from '@/api/dish'\r\nimport { baseUrl } from '@/config.json'\r\nimport { getToken } from '@/utils/cookies'\r\n@Component({\r\n  name: 'addShop',\r\n  components: {\r\n    HeadLable,\r\n    SelectInput,\r\n    ImageUpload\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private restKey: number = 0\r\n  private textarea: string = ''\r\n  private value: string = ''\r\n  private imageUrl: string = ''\r\n  private actionType: string = ''\r\n  private dishList: string[] = []\r\n  private dishFlavorsData: any[] = [] //原始口味数据\r\n  private dishFlavors: any[] = [] //待上传口味的数据\r\n  private leftDishFlavors: any[] = [] //下拉框剩余可选择的口味数据\r\n  private vueRest = '1'\r\n  private index = 0\r\n  private inputStyle = { flex: 1 }\r\n  private headers = {\r\n    token: getToken()\r\n  }\r\n  private ruleForm = {\r\n    name: '',\r\n    id: '',\r\n    price: '',\r\n    code: '',\r\n    image: '',\r\n    description: '',\r\n    dishFlavors: [],\r\n    status: true,\r\n    categoryId: ''\r\n  }\r\n\r\n  get rules() {\r\n    return {\r\n      name: [\r\n        {\r\n          required: true,\r\n          validator: (rule: any, value: string, callback: Function) => {\r\n            if (!value) {\r\n              callback(new Error('请输入菜品名称'))\r\n            } else {\r\n              const reg = /^([A-Za-z0-9\\u4e00-\\u9fa5]){2,20}$/\r\n              if (!reg.test(value)) {\r\n                callback(new Error('菜品名称输入不符，请输入2-20个字符'))\r\n              } else {\r\n                callback()\r\n              }\r\n            }\r\n          },\r\n          trigger: 'blur'\r\n        }\r\n      ],\r\n      categoryId: [\r\n        { required: true, message: '请选择菜品分类', trigger: 'change' }\r\n      ],\r\n      image: {\r\n        required: true,\r\n        message: '菜品图片不能为空'\r\n      },\r\n      price: [\r\n        {\r\n          required: true,\r\n          // 'message': '请填写菜品价格',\r\n          validator: (rules: any, value: string, callback: Function) => {\r\n            const reg = /^([1-9]\\d{0,5}|0)(\\.\\d{1,2})?$/\r\n            if (!reg.test(value) || Number(value) <= 0) {\r\n              callback(\r\n                new Error(\r\n                  '菜品价格格式有误，请输入大于零且最多保留两位小数的金额'\r\n                )\r\n              )\r\n            } else {\r\n              callback()\r\n            }\r\n          },\r\n          trigger: 'blur'\r\n        }\r\n      ],\r\n      code: [{ required: true, message: '请填写商品码', trigger: 'blur' }]\r\n    }\r\n  }\r\n\r\n  created() {\r\n    this.getDishList()\r\n    // 口味临时数据\r\n    this.getFlavorListHand()\r\n    this.actionType = this.$route.query.id ? 'edit' : 'add'\r\n    if (this.$route.query.id) {\r\n      this.init()\r\n    }\r\n  }\r\n\r\n  mounted() {}\r\n  @Watch('dishFlavors')\r\n  changeDishFlavors() {\r\n    this.getLeftDishFlavors()\r\n  }\r\n\r\n  //过滤已选择的口味下拉框无法再次选择\r\n  getLeftDishFlavors() {\r\n    let arr = []\r\n    this.dishFlavorsData.map(item => {\r\n      if (\r\n        this.dishFlavors.findIndex(item1 => item.name === item1.name) === -1\r\n      ) {\r\n        arr.push(item)\r\n      }\r\n    })\r\n    this.leftDishFlavors = arr\r\n  }\r\n\r\n  private selectHandle(val: any, key: any, ind: any) {\r\n    const arrDate = [...this.dishFlavors]\r\n    const index = this.dishFlavorsData.findIndex(item => item.name === val)\r\n    arrDate[key] = JSON.parse(JSON.stringify(this.dishFlavorsData[index]))\r\n    this.dishFlavors = arrDate\r\n  }\r\n\r\n  private async init() {\r\n    queryDishById(this.$route.query.id).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.ruleForm = { ...res.data.data }\r\n        this.ruleForm.price = String(res.data.data.price)\r\n        this.ruleForm.status = res.data.data.status == '1'\r\n        this.dishFlavors =\r\n          res.data.data.flavors &&\r\n          res.data.data.flavors.map(obj => ({\r\n            ...obj,\r\n            value: JSON.parse(obj.value)\r\n          }))\r\n        let arr = []\r\n        this.getLeftDishFlavors()\r\n        this.imageUrl = res.data.data.image\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n\r\n  // 按钮 - 添加口味\r\n  private addFlavore() {\r\n    this.dishFlavors.push({ name: '', value: [] }) // JSON.parse(JSON.stringify(this.dishFlavorsData))\r\n  }\r\n\r\n  // 按钮 - 删除口味\r\n  private delFlavor(name: string) {\r\n    let ind = this.dishFlavors.findIndex(item => item.name === name)\r\n    this.dishFlavors.splice(ind, 1)\r\n  }\r\n\r\n  // 按钮 - 删除口味标签\r\n  private delFlavorLabel(index: number, ind: number) {\r\n    this.dishFlavors[index].value.splice(ind, 1)\r\n  }\r\n\r\n  //口味位置记录\r\n  private flavorPosition(index: number) {\r\n    this.index = index\r\n  }\r\n\r\n  // 添加口味标签\r\n  private keyDownHandle(val: any) {\r\n    if (event) {\r\n      event.cancelBubble = true\r\n      event.preventDefault()\r\n      event.stopPropagation()\r\n    }\r\n\r\n    if (val.target.innerText.trim() != '') {\r\n      this.dishFlavors[this.index].flavorData.push(val.target.innerText)\r\n      val.target.innerText = ''\r\n    }\r\n  }\r\n\r\n  // 获取菜品分类\r\n  private getDishList() {\r\n    getCategoryList({ type: 1 }).then(res => {\r\n      if (res.data.code === 1) {\r\n        this.dishList = res && res.data && res.data.data\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n      // if (res.data.code == 200) {\r\n      //   const {data} = res.data\r\n      //   this.dishList = data\r\n      // } else {\r\n      //   this.$message.error(res.data.desc)\r\n      // }\r\n    })\r\n  }\r\n\r\n  // 获取口味列表\r\n  private getFlavorListHand() {\r\n    // flavor flavorData\r\n    this.dishFlavorsData = [\r\n      { name: '甜味', value: ['无糖', '少糖', '半糖', '多糖', '全糖'] },\r\n      { name: '温度', value: ['热饮', '常温', '去冰', '少冰', '多冰'] },\r\n      { name: '忌口', value: ['不要葱', '不要蒜', '不要香菜', '不要辣'] },\r\n      { name: '辣度', value: ['不辣', '微辣', '中辣', '重辣'] }\r\n    ]\r\n  }\r\n\r\n  private submitForm(formName: any, st: any) {\r\n    ;(this.$refs[formName] as any).validate((valid: any) => {\r\n      console.log(valid, 'valid')\r\n      if (valid) {\r\n        if (!this.ruleForm.image) return this.$message.error('菜品图片不能为空')\r\n        let params: any = { ...this.ruleForm }\r\n        // params.flavors = this.dishFlavors\r\n        params.status =\r\n          this.actionType === 'add' ? 0 : this.ruleForm.status ? 1 : 0\r\n        // params.price *= 100\r\n        params.categoryId = this.ruleForm.categoryId\r\n        params.flavors = this.dishFlavors.map(obj => ({\r\n          ...obj,\r\n          value: JSON.stringify(obj.value)\r\n        }))\r\n        delete params.dishFlavors\r\n        if (this.actionType == 'add') {\r\n          delete params.id\r\n          addDish(params)\r\n            .then(res => {\r\n              if (res.data.code === 1) {\r\n                this.$message.success('菜品添加成功！')\r\n                if (!st) {\r\n                  this.$router.push({ path: '/dish' })\r\n                } else {\r\n                  this.dishFlavors = []\r\n                  // this.dishFlavorsData = []\r\n                  this.imageUrl = ''\r\n                  this.ruleForm = {\r\n                    name: '',\r\n                    id: '',\r\n                    price: '',\r\n                    code: '',\r\n                    image: '',\r\n                    description: '',\r\n                    dishFlavors: [],\r\n                    status: true,\r\n                    categoryId: ''\r\n                  }\r\n                  this.restKey++\r\n                }\r\n              } else {\r\n                this.$message.error(res.data.desc || res.data.msg)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        } else {\r\n          delete params.createTime\r\n          delete params.updateTime\r\n          editDish(params)\r\n            .then(res => {\r\n              if (res && res.data && res.data.code === 1) {\r\n                this.$router.push({ path: '/dish' })\r\n                this.$message.success('菜品修改成功！')\r\n              } else {\r\n                this.$message.error(res.data.desc || res.data.msg)\r\n              }\r\n              // if (res.data.code == 200) {\r\n              //   this.$router.push({'path': '/dish'})\r\n              //   this.$message.success('菜品修改成功！')\r\n              // } else {\r\n              //   this.$message.error(res.data.desc || res.data.message)\r\n              // }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        }\r\n      } else {\r\n        return false\r\n      }\r\n    })\r\n  }\r\n\r\n  imageChange(value: any) {\r\n    this.ruleForm.image = value\r\n  }\r\n}\r\n", {"version": 3, "sources": ["addDishtype.vue"], "names": [], "mappings": ";AA4HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "addDishtype.vue", "sourceRoot": "src/views/dish", "sourcesContent": ["<template>\r\n  <div :key=\"vueRest\"\r\n       class=\"addBrand-container\">\r\n    <div :key=\"restKey\"\r\n         class=\"container\">\r\n      <el-form ref=\"ruleForm\"\r\n               :model=\"ruleForm\"\r\n               :rules=\"rules\"\r\n               :inline=\"true\"\r\n               label-width=\"180px\"\r\n               class=\"demo-ruleForm\">\r\n        <div>\r\n          <el-form-item label=\"菜品名称:\"\r\n                        prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"\r\n                      placeholder=\"请填写菜品名称\"\r\n                      maxlength=\"20\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"菜品分类:\"\r\n                        prop=\"categoryId\">\r\n            <el-select v-model=\"ruleForm.categoryId\"\r\n                       placeholder=\"请选择菜品分类\">\r\n              <el-option v-for=\"(item, index) in dishList\"\r\n                         :key=\"index\"\r\n                         :label=\"item.name\"\r\n                         :value=\"item.id\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </div>\r\n        <div>\r\n          <el-form-item label=\"菜品价格:\"\r\n                        prop=\"price\">\r\n            <el-input v-model=\"ruleForm.price\"\r\n                      placeholder=\"请设置菜品价格\" />\r\n          </el-form-item>\r\n        </div>\r\n        <el-form-item label=\"口味做法配置:\">\r\n          <el-form-item>\r\n            <div class=\"flavorBox\">\r\n              <span v-if=\"dishFlavors.length == 0\"\r\n                    class=\"addBut\"\r\n                    @click=\"addFlavore\">\r\n                + 添加口味</span>\r\n              <div v-if=\"dishFlavors.length != 0\"\r\n                   class=\"flavor\">\r\n                <div class=\"title\">\r\n                  <span>口味名（3个字内）</span>\r\n                  <!-- <span class=\"des-box\">口味标签（输入标签回车添加）</span> -->\r\n                </div>\r\n                <div class=\"cont\">\r\n                  <div v-for=\"(item, index) in dishFlavors\"\r\n                       :key=\"index\"\r\n                       class=\"items\">\r\n                    <div class=\"itTit\">\r\n                      <!-- :dish-flavors-data=\"filterDishFlavorsData()\" -->\r\n                      <SelectInput :dish-flavors-data=\"leftDishFlavors\"\r\n                                   :index=\"index\"\r\n                                   :value=\"item.name\"\r\n                                   @select=\"selectHandle\" />\r\n                    </div>\r\n                    <div class=\"labItems\"\r\n                         style=\"display: flex\">\r\n                      <span v-for=\"(it, ind) in item.value\"\r\n                            :key=\"ind\">{{ it }}\r\n                        <i @click=\"delFlavorLabel(index, ind)\">X</i></span>\r\n                      <div class=\"inputBox\"\r\n                           :style=\"inputStyle\" />\r\n                    </div>\r\n                    <span class=\"delFlavor delBut non\"\r\n                          @click=\"delFlavor(item.name)\">删除</span>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"\r\n                       !!this.leftDishFlavors.length &&\r\n                         this.dishFlavors.length < this.dishFlavorsData.length\r\n                     \"\r\n                     class=\"addBut\"\r\n                     @click=\"addFlavore\">\r\n                  添加口味\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form-item>\r\n        </el-form-item>\r\n        <div>\r\n          <el-form-item label=\"菜品图片:\"\r\n                        prop=\"image\">\r\n            <image-upload :prop-image-url=\"imageUrl\"\r\n                          @imageChange=\"imageChange\">\r\n              图片大小不超过2M<br>仅能上传 PNG JPEG JPG类型图片<br>建议上传200*200或300*300尺寸的图片\r\n            </image-upload>\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"address\">\r\n          <el-form-item label=\"菜品描述:\"\r\n                        prop=\"region\">\r\n            <el-input v-model=\"ruleForm.description\"\r\n                      type=\"textarea\"\r\n                      :rows=\"3\"\r\n                      maxlength=\"200\"\r\n                      placeholder=\"菜品描述，最长200字\" />\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"subBox address\">\r\n          <el-button @click=\"() => $router.back()\">\r\n            取消\r\n          </el-button>\r\n          <el-button type=\"primary\"\r\n                     :class=\"{ continue: actionType === 'add' }\"\r\n                     @click=\"submitForm('ruleForm')\">\r\n            保存\r\n          </el-button>\r\n          <el-button v-if=\"actionType == 'add'\"\r\n                     type=\"primary\"\r\n                     @click=\"submitForm('ruleForm', 'goAnd')\">\r\n            保存并继续添加\r\n          </el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport HeadLable from '@/components/HeadLable/index.vue'\r\nimport SelectInput from './components/SelectInput.vue'\r\nimport ImageUpload from '@/components/ImgUpload/index.vue'\r\n// getFlavorList口味列表暂时不做 getDishTypeList\r\nimport {\r\n  queryDishById,\r\n  addDish,\r\n  editDish,\r\n  getCategoryList,\r\n  commonDownload\r\n} from '@/api/dish'\r\nimport { baseUrl } from '@/config.json'\r\nimport { getToken } from '@/utils/cookies'\r\n@Component({\r\n  name: 'addShop',\r\n  components: {\r\n    HeadLable,\r\n    SelectInput,\r\n    ImageUpload\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private restKey: number = 0\r\n  private textarea: string = ''\r\n  private value: string = ''\r\n  private imageUrl: string = ''\r\n  private actionType: string = ''\r\n  private dishList: string[] = []\r\n  private dishFlavorsData: any[] = [] //原始口味数据\r\n  private dishFlavors: any[] = [] //待上传口味的数据\r\n  private leftDishFlavors: any[] = [] //下拉框剩余可选择的口味数据\r\n  private vueRest = '1'\r\n  private index = 0\r\n  private inputStyle = { flex: 1 }\r\n  private headers = {\r\n    token: getToken()\r\n  }\r\n  private ruleForm = {\r\n    name: '',\r\n    id: '',\r\n    price: '',\r\n    code: '',\r\n    image: '',\r\n    description: '',\r\n    dishFlavors: [],\r\n    status: true,\r\n    categoryId: ''\r\n  }\r\n\r\n  get rules() {\r\n    return {\r\n      name: [\r\n        {\r\n          required: true,\r\n          validator: (rule: any, value: string, callback: Function) => {\r\n            if (!value) {\r\n              callback(new Error('请输入菜品名称'))\r\n            } else {\r\n              const reg = /^([A-Za-z0-9\\u4e00-\\u9fa5]){2,20}$/\r\n              if (!reg.test(value)) {\r\n                callback(new Error('菜品名称输入不符，请输入2-20个字符'))\r\n              } else {\r\n                callback()\r\n              }\r\n            }\r\n          },\r\n          trigger: 'blur'\r\n        }\r\n      ],\r\n      categoryId: [\r\n        { required: true, message: '请选择菜品分类', trigger: 'change' }\r\n      ],\r\n      image: {\r\n        required: true,\r\n        message: '菜品图片不能为空'\r\n      },\r\n      price: [\r\n        {\r\n          required: true,\r\n          // 'message': '请填写菜品价格',\r\n          validator: (rules: any, value: string, callback: Function) => {\r\n            const reg = /^([1-9]\\d{0,5}|0)(\\.\\d{1,2})?$/\r\n            if (!reg.test(value) || Number(value) <= 0) {\r\n              callback(\r\n                new Error(\r\n                  '菜品价格格式有误，请输入大于零且最多保留两位小数的金额'\r\n                )\r\n              )\r\n            } else {\r\n              callback()\r\n            }\r\n          },\r\n          trigger: 'blur'\r\n        }\r\n      ],\r\n      code: [{ required: true, message: '请填写商品码', trigger: 'blur' }]\r\n    }\r\n  }\r\n\r\n  created() {\r\n    this.getDishList()\r\n    // 口味临时数据\r\n    this.getFlavorListHand()\r\n    this.actionType = this.$route.query.id ? 'edit' : 'add'\r\n    if (this.$route.query.id) {\r\n      this.init()\r\n    }\r\n  }\r\n\r\n  mounted() {}\r\n  @Watch('dishFlavors')\r\n  changeDishFlavors() {\r\n    this.getLeftDishFlavors()\r\n  }\r\n\r\n  //过滤已选择的口味下拉框无法再次选择\r\n  getLeftDishFlavors() {\r\n    let arr = []\r\n    this.dishFlavorsData.map(item => {\r\n      if (\r\n        this.dishFlavors.findIndex(item1 => item.name === item1.name) === -1\r\n      ) {\r\n        arr.push(item)\r\n      }\r\n    })\r\n    this.leftDishFlavors = arr\r\n  }\r\n\r\n  private selectHandle(val: any, key: any, ind: any) {\r\n    const arrDate = [...this.dishFlavors]\r\n    const index = this.dishFlavorsData.findIndex(item => item.name === val)\r\n    arrDate[key] = JSON.parse(JSON.stringify(this.dishFlavorsData[index]))\r\n    this.dishFlavors = arrDate\r\n  }\r\n\r\n  private async init() {\r\n    queryDishById(this.$route.query.id).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.ruleForm = { ...res.data.data }\r\n        this.ruleForm.price = String(res.data.data.price)\r\n        this.ruleForm.status = res.data.data.status == '1'\r\n        this.dishFlavors =\r\n          res.data.data.flavors &&\r\n          res.data.data.flavors.map(obj => ({\r\n            ...obj,\r\n            value: JSON.parse(obj.value)\r\n          }))\r\n        let arr = []\r\n        this.getLeftDishFlavors()\r\n        this.imageUrl = res.data.data.image\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n\r\n  // 按钮 - 添加口味\r\n  private addFlavore() {\r\n    this.dishFlavors.push({ name: '', value: [] }) // JSON.parse(JSON.stringify(this.dishFlavorsData))\r\n  }\r\n\r\n  // 按钮 - 删除口味\r\n  private delFlavor(name: string) {\r\n    let ind = this.dishFlavors.findIndex(item => item.name === name)\r\n    this.dishFlavors.splice(ind, 1)\r\n  }\r\n\r\n  // 按钮 - 删除口味标签\r\n  private delFlavorLabel(index: number, ind: number) {\r\n    this.dishFlavors[index].value.splice(ind, 1)\r\n  }\r\n\r\n  //口味位置记录\r\n  private flavorPosition(index: number) {\r\n    this.index = index\r\n  }\r\n\r\n  // 添加口味标签\r\n  private keyDownHandle(val: any) {\r\n    if (event) {\r\n      event.cancelBubble = true\r\n      event.preventDefault()\r\n      event.stopPropagation()\r\n    }\r\n\r\n    if (val.target.innerText.trim() != '') {\r\n      this.dishFlavors[this.index].flavorData.push(val.target.innerText)\r\n      val.target.innerText = ''\r\n    }\r\n  }\r\n\r\n  // 获取菜品分类\r\n  private getDishList() {\r\n    getCategoryList({ type: 1 }).then(res => {\r\n      if (res.data.code === 1) {\r\n        this.dishList = res && res.data && res.data.data\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n      // if (res.data.code == 200) {\r\n      //   const {data} = res.data\r\n      //   this.dishList = data\r\n      // } else {\r\n      //   this.$message.error(res.data.desc)\r\n      // }\r\n    })\r\n  }\r\n\r\n  // 获取口味列表\r\n  private getFlavorListHand() {\r\n    // flavor flavorData\r\n    this.dishFlavorsData = [\r\n      { name: '甜味', value: ['无糖', '少糖', '半糖', '多糖', '全糖'] },\r\n      { name: '温度', value: ['热饮', '常温', '去冰', '少冰', '多冰'] },\r\n      { name: '忌口', value: ['不要葱', '不要蒜', '不要香菜', '不要辣'] },\r\n      { name: '辣度', value: ['不辣', '微辣', '中辣', '重辣'] }\r\n    ]\r\n  }\r\n\r\n  private submitForm(formName: any, st: any) {\r\n    ;(this.$refs[formName] as any).validate((valid: any) => {\r\n      console.log(valid, 'valid')\r\n      if (valid) {\r\n        if (!this.ruleForm.image) return this.$message.error('菜品图片不能为空')\r\n        let params: any = { ...this.ruleForm }\r\n        // params.flavors = this.dishFlavors\r\n        params.status =\r\n          this.actionType === 'add' ? 0 : this.ruleForm.status ? 1 : 0\r\n        // params.price *= 100\r\n        params.categoryId = this.ruleForm.categoryId\r\n        params.flavors = this.dishFlavors.map(obj => ({\r\n          ...obj,\r\n          value: JSON.stringify(obj.value)\r\n        }))\r\n        delete params.dishFlavors\r\n        if (this.actionType == 'add') {\r\n          delete params.id\r\n          addDish(params)\r\n            .then(res => {\r\n              if (res.data.code === 1) {\r\n                this.$message.success('菜品添加成功！')\r\n                if (!st) {\r\n                  this.$router.push({ path: '/dish' })\r\n                } else {\r\n                  this.dishFlavors = []\r\n                  // this.dishFlavorsData = []\r\n                  this.imageUrl = ''\r\n                  this.ruleForm = {\r\n                    name: '',\r\n                    id: '',\r\n                    price: '',\r\n                    code: '',\r\n                    image: '',\r\n                    description: '',\r\n                    dishFlavors: [],\r\n                    status: true,\r\n                    categoryId: ''\r\n                  }\r\n                  this.restKey++\r\n                }\r\n              } else {\r\n                this.$message.error(res.data.desc || res.data.msg)\r\n              }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        } else {\r\n          delete params.createTime\r\n          delete params.updateTime\r\n          editDish(params)\r\n            .then(res => {\r\n              if (res && res.data && res.data.code === 1) {\r\n                this.$router.push({ path: '/dish' })\r\n                this.$message.success('菜品修改成功！')\r\n              } else {\r\n                this.$message.error(res.data.desc || res.data.msg)\r\n              }\r\n              // if (res.data.code == 200) {\r\n              //   this.$router.push({'path': '/dish'})\r\n              //   this.$message.success('菜品修改成功！')\r\n              // } else {\r\n              //   this.$message.error(res.data.desc || res.data.message)\r\n              // }\r\n            })\r\n            .catch(err => {\r\n              this.$message.error('请求出错了：' + err.message)\r\n            })\r\n        }\r\n      } else {\r\n        return false\r\n      }\r\n    })\r\n  }\r\n\r\n  imageChange(value: any) {\r\n    this.ruleForm.image = value\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.addBrand-container {\r\n  .el-form--inline .el-form-item__content {\r\n    width: 293px;\r\n  }\r\n\r\n  .el-input {\r\n    width: 350px;\r\n  }\r\n\r\n  .address {\r\n    .el-form-item__content {\r\n      width: 777px !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.addBrand {\r\n  &-container {\r\n    margin: 30px;\r\n\r\n    .container {\r\n      position: relative;\r\n      z-index: 1;\r\n      background: #fff;\r\n      padding: 30px;\r\n      border-radius: 4px;\r\n      min-height: 500px;\r\n\r\n      .subBox {\r\n        padding-top: 30px;\r\n        text-align: center;\r\n        border-top: solid 1px $gray-5;\r\n      }\r\n      .upload-item {\r\n        .el-form-item__error {\r\n          top: 90%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.flavorBox {\r\n  width: 777px;\r\n\r\n  .addBut {\r\n    background: #ffc200;\r\n    display: inline-block;\r\n    padding: 0px 20px;\r\n    border-radius: 3px;\r\n    line-height: 40px;\r\n    cursor: pointer;\r\n    border-radius: 4px;\r\n    color: #333333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .flavor {\r\n    border: solid 1px #dfe2e8;\r\n    border-radius: 3px;\r\n    padding: 15px;\r\n    background: #fafafb;\r\n\r\n    .title {\r\n      color: #606168;\r\n      .des-box {\r\n        padding-left: 44px;\r\n      }\r\n    }\r\n\r\n    .cont {\r\n      .items {\r\n        display: flex;\r\n        margin: 10px 0;\r\n\r\n        .itTit {\r\n          width: 150px;\r\n          margin-right: 15px;\r\n\r\n          input {\r\n            width: 100%;\r\n            // line-height: 40px;\r\n            // border-radius: 3px;\r\n            // padding: 0 10px;\r\n          }\r\n        }\r\n\r\n        .labItems {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          border-radius: 3px;\r\n          min-height: 39px;\r\n          border: solid 1px #d8dde3;\r\n          background: #fff;\r\n          padding: 0 5px;\r\n\r\n          span {\r\n            display: inline-block;\r\n            color: #ffc200;\r\n            margin: 5px;\r\n            line-height: 26px;\r\n            padding: 0 10px;\r\n            background: #fffbf0;\r\n            border: 1px solid #fbe396;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n\r\n            i {\r\n              cursor: pointer;\r\n              font-style: normal;\r\n            }\r\n          }\r\n\r\n          .inputBox {\r\n            display: inline-block;\r\n            width: 100%;\r\n            height: 36px;\r\n            line-height: 36px;\r\n            overflow: hidden;\r\n          }\r\n        }\r\n\r\n        .delFlavor {\r\n          display: inline-block;\r\n          padding: 0 10px;\r\n          color: #f19c59;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}