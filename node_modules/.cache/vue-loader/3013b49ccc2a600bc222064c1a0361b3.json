{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderview.vue?vue&type=template&id=200179ef", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderview.vue", "mtime": 1655868615000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"container\">\n  <h2 class=\"homeTitle\">\n    订单管理<i>{{ days[1] }}</i\n    ><span><router-link to=\"/order\">订单明细</router-link></span>\n  </h2>\n  <div class=\"orderviewBox\">\n    <ul>\n      <li>\n        <span class=\"status\"\n          ><i class=\"iconfont icon-waiting\"></i>待接单</span\n        >\n        <span class=\"num tip\"\n          ><router-link to=\"/order?status=2\">{{\n            orderviewData.waitingOrders\n          }}</router-link></span\n        >\n      </li>\n      <li>\n        <span class=\"status\"\n          ><i class=\"iconfont icon-staySway\"></i>待派送</span\n        >\n        <span class=\"num tip\"\n          ><router-link to=\"/order?status=3\">{{\n            orderviewData.deliveredOrders\n          }}</router-link></span\n        >\n      </li>\n      <li>\n        <span class=\"status\"\n          ><i class=\"iconfont icon-complete\"></i>已完成</span\n        >\n        <span class=\"num\"\n          ><router-link to=\"/order?status=5\">{{\n            orderviewData.completedOrders\n          }}</router-link></span\n        >\n      </li>\n      <li>\n        <span class=\"status\"><i class=\"iconfont icon-cancel\"></i>已取消</span>\n        <span class=\"num\"\n          ><router-link to=\"/order?status=6\">{{\n            orderviewData.cancelledOrders\n          }}</router-link></span\n        >\n      </li>\n      <li>\n        <span class=\"status\"><i class=\"iconfont icon-all\"></i>全部订单</span>\n        <span class=\"num\"\n          ><router-link to=\"/order\">{{\n            orderviewData.allOrders\n          }}</router-link></span\n        >\n      </li>\n    </ul>\n  </div>\n</div>\n", null]}