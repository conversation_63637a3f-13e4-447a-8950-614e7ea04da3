{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItemLink.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItemLink.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { isExternal } from '@/utils/validate'\r\n\r\n@Component({\r\n  'name': 'SidebarItemLink'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ 'required': true }) private to!: string\r\n\r\n  private isExternal = isExternal\r\n}\r\n", {"version": 3, "sources": ["SidebarItemLink.vue"], "names": [], "mappings": ";AAUA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "SidebarItemLink.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <a v-if=\"isExternal(to)\" :href=\"to\" target=\"_blank\" rel=\"noopener\">\r\n    <slot />\r\n  </a>\r\n  <router-link v-else :to=\"to\">\r\n    <slot />\r\n  </router-link>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { isExternal } from '@/utils/validate'\r\n\r\n@Component({\r\n  'name': 'SidebarItemLink'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ 'required': true }) private to!: string\r\n\r\n  private isExternal = isExternal\r\n}\r\n</script>\r\n"]}]}