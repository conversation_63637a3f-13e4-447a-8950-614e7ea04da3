{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/titleIndex.vue", "mtime": 1656301911000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport { exportInfor } from '@/api/index'\n@Component({\n  name: 'TitleIndex',\n})\nexport default class extends Vue {\n  @Prop() private flag!: any\n  @Prop() private tateData!: any\n  @Prop() private turnoverData!: any\n\n  nowIndex = 2 - 1\n  value = []\n  tabsParam = ['昨日', '近7日', '近30日', '本周', '本月']\n  @Watch('flag')\n  getNowIndex(val) {\n    this.nowIndex = val\n  }\n  // tab切换\n  toggleTabs(index: number) {\n    this.nowIndex = index\n    this.value = []\n    this.$emit('sendTitleInd', index + 1)\n  }\n  //  数据导出\n  /** 导出按钮操作 */\n  handleExport() {\n    this.$confirm('是否确认导出最近30天运营数据?', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning',\n    })\n      .then(async function () {\n        const { data } = await exportInfor()\n        let url = window.URL.createObjectURL(data)\n        var a = document.createElement('a')\n        document.body.appendChild(a)\n        a.href = url\n        a.download = '运营数据统计报表.xlsx'\n        a.click()\n        window.URL.revokeObjectURL(url)\n      })\n      .then((response) => {})\n  }\n}\n", {"version": 3, "sources": ["titleIndex.vue"], "names": [], "mappings": ";AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "titleIndex.vue", "sourceRoot": "src/views/statistics/components", "sourcesContent": ["<template>\n  <div class=\"title-index\">\n    <div class=\"month\">\n      <ul class=\"tabs\">\n        <li\n          class=\"li-tab\"\n          v-for=\"(item, index) in tabsParam\"\n          @click=\"toggleTabs(index)\"\n          :class=\"{ active: index === nowIndex }\"\n          :key=\"index\"\n        >\n          {{ item }}\n          <span></span>\n        </li>\n      </ul>\n    </div>\n    <div class=\"get-time\">\n      <p>\n        已选时间：{{ tateData[0] }} 至\n        {{ tateData[tateData.length - 1] }}\n      </p>\n    </div>\n    <el-button\n      icon=\"iconfont icon-download\"\n      class=\"right-el-button\"\n      @click=\"handleExport\"\n      >数据导出</el-button\n    >\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport { exportInfor } from '@/api/index'\n@Component({\n  name: 'TitleIndex',\n})\nexport default class extends Vue {\n  @Prop() private flag!: any\n  @Prop() private tateData!: any\n  @Prop() private turnoverData!: any\n\n  nowIndex = 2 - 1\n  value = []\n  tabsParam = ['昨日', '近7日', '近30日', '本周', '本月']\n  @Watch('flag')\n  getNowIndex(val) {\n    this.nowIndex = val\n  }\n  // tab切换\n  toggleTabs(index: number) {\n    this.nowIndex = index\n    this.value = []\n    this.$emit('sendTitleInd', index + 1)\n  }\n  //  数据导出\n  /** 导出按钮操作 */\n  handleExport() {\n    this.$confirm('是否确认导出最近30天运营数据?', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning',\n    })\n      .then(async function () {\n        const { data } = await exportInfor()\n        let url = window.URL.createObjectURL(data)\n        var a = document.createElement('a')\n        document.body.appendChild(a)\n        a.href = url\n        a.download = '运营数据统计报表.xlsx'\n        a.click()\n        window.URL.revokeObjectURL(url)\n      })\n      .then((response) => {})\n  }\n}\n</script>\n"]}]}