{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/addSetmeal.vue", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/addSetmeal.vue", "mtime": 1695192173000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./addSetmeal.vue?vue&type=template&id=c313e5f0&scoped=true\"\nimport script from \"./addSetmeal.vue?vue&type=script&lang=ts\"\nexport * from \"./addSetmeal.vue?vue&type=script&lang=ts\"\nimport style0 from \"./addSetmeal.vue?vue&type=style&index=0&id=c313e5f0&lang=css\"\nimport style1 from \"./addSetmeal.vue?vue&type=style&index=1&id=c313e5f0&lang=scss\"\nimport style2 from \"./addSetmeal.vue?vue&type=style&index=2&id=c313e5f0&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c313e5f0\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('c313e5f0')) {\n      api.createRecord('c313e5f0', component.options)\n    } else {\n      api.reload('c313e5f0', component.options)\n    }\n    module.hot.accept(\"./addSetmeal.vue?vue&type=template&id=c313e5f0&scoped=true\", function () {\n      api.rerender('c313e5f0', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/setmeal/addSetmeal.vue\"\nexport default component.exports"]}