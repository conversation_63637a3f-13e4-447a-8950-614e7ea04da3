{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/components/SelectInput.vue?vue&type=template&id=61f5ad80&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/components/SelectInput.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"selectInput\">\n  <el-input\n    v-model=\"value\"\n    type=\"text\"\n    style=\"width: 100%\"\n    placeholder=\"请选择口味\"\n    clearable\n    readonly\n    @focus=\"selectFlavor(true)\"\n    @blur=\"outSelect(false)\"\n  />\n  <div v-if=\"mak && dishFlavorsData.length\" class=\"flavorSelect\">\n    <span\n      v-for=\"(it, ind) in dishFlavorsData\"\n      :key=\"ind\"\n      class=\"items\"\n      @click=\"checkOption(it, ind)\"\n      >{{ it.name }}</span\n    >\n    <span v-if=\"dishFlavorsData == []\" class=\"none\">无数据</span>\n  </div>\n</div>\n", null]}