{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/category/index.vue?vue&type=template&id=7f48609b&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/category/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"dashboard-container\">\n  <div class=\"container\">\n    <div class=\"tableBar\"\n         style=\"display: inline-block; width: 100%\">\n      <label style=\"margin-right: 10px\">分类名称：</label>\n      <el-input v-model=\"name\"\n                placeholder=\"请填写分类名称\"\n                style=\"width: 15%\"\n                clearable\n                @clear=\"init\"\n                @keyup.enter.native=\"init\" />\n\n      <label style=\"margin-right: 5px; margin-left: 20px\">分类类型：</label>\n      <el-select v-model=\"categoryType\"\n                 placeholder=\"请选择\"\n                 clearable\n                 style=\"width: 15%\"\n                 @clear=\"init\">\n        <el-option v-for=\"item in options\"\n                   :key=\"item.value\"\n                   :label=\"item.label\"\n                   :value=\"item.value\" />\n      </el-select>\n\n      <div style=\"float: right\">\n        <el-button type=\"primary\"\n                   class=\"continue\"\n                   @click=\"addClass('class')\">\n          + 新增菜品分类\n        </el-button>\n        <el-button type=\"primary\"\n                   style=\"margin-left:20px\"\n                   @click=\"addClass('meal')\">\n          + 新增套餐分类\n        </el-button>\n      </div>\n\n      <el-button class=\"normal-btn continue\"\n                 @click=\"init(true)\">\n        查询\n      </el-button>\n    </div>\n    <el-table v-if=\"tableData.length\"\n              :data=\"tableData\"\n              stripe\n              class=\"tableBox\">\n      <el-table-column prop=\"name\"\n                       label=\"分类名称\" />\n      <el-table-column prop=\"type\"\n                       label=\"分类类型\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.type == '1' ? '菜品分类' : '套餐分类' }}</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"sort\"\n                       label=\"排序\" />\n      <el-table-column label=\"状态\">\n        <template slot-scope=\"scope\">\n          <div class=\"tableColumn-status\"\n               :class=\"{ 'stop-use': String(scope.row.status) === '0' }\">\n            {{ String(scope.row.status) === '0' ? '禁用' : '启用' }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"updateTime\"\n                       label=\"操作时间\" />\n      <el-table-column label=\"操作\"\n                       width=\"200\"\n                       align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\"\n                     size=\"small\"\n                     class=\"blueBug\"\n                     @click=\"editHandle(scope.row)\">\n            修改\n          </el-button>\n          <el-button type=\"text\"\n                     size=\"small\"\n                     class=\"delBut\"\n                     @click=\"deleteHandle(scope.row.id)\">\n            删除\n          </el-button>\n          <el-button type=\"text\"\n                     size=\"small\"\n                     class=\"non\"\n                     :class=\"{\n                       blueBug: scope.row.status == '0',\n                       delBut: scope.row.status != '0'\n                     }\"\n                     @click=\"statusHandle(scope.row)\">\n            {{ scope.row.status == '1' ? '禁用' : '启用' }}\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <Empty v-else\n           :is-search=\"isSearch\" />\n    <el-pagination v-if=\"counts > 10\"\n                   class=\"pageList\"\n                   :page-sizes=\"[10, 20, 30, 40]\"\n                   :page-size=\"pageSize\"\n                   layout=\"total, sizes, prev, pager, next, jumper\"\n                   :total=\"counts\"\n                   @size-change=\"handleSizeChange\"\n                   @current-change=\"handleCurrentChange\" />\n  </div>\n  <el-dialog :title=\"classData.title\"\n             :visible.sync=\"classData.dialogVisible\"\n             width=\"30%\"\n             :before-close=\"handleClose\">\n    <el-form ref=\"classData\"\n             :model=\"classData\"\n             class=\"demo-form-inline\"\n             :rules=\"rules\"\n             label-width=\"100px\">\n      <el-form-item label=\"分类名称：\"\n                    prop=\"name\">\n        <el-input v-model=\"classData.name\"\n                  placeholder=\"请输入分类名称\"\n                  maxlength=\"20\" />\n      </el-form-item>\n      <el-form-item label=\"排序：\"\n                    prop=\"sort\">\n        <el-input v-model=\"classData.sort\"\n                  placeholder=\"请输入排序\" />\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\"\n          class=\"dialog-footer\">\n      <el-button size=\"medium\"\n                 @click=\"\n          ;(classData.dialogVisible = false), $refs.classData.resetFields()\n                 \">取 消</el-button>\n      <el-button type=\"primary\"\n                 :class=\"{ continue: actionType === 'add' }\"\n                 size=\"medium\"\n                 @click=\"submitForm()\">确 定</el-button>\n      <el-button v-if=\"action != 'edit'\"\n                 type=\"primary\"\n                 size=\"medium\"\n                 @click=\"submitForm('go')\">\n        保存并继续添加\n      </el-button>\n    </span>\n  </el-dialog>\n</div>\n", null]}