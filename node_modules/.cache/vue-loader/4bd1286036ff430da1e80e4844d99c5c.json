{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue", "mtime": 1691720868000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { Route } from 'vue-router'\r\nimport { Form as ElForm, Input } from 'element-ui'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport { isValidUsername } from '@/utils/validate'\r\n\r\n@Component({\r\n  name: 'Login',\r\n})\r\nexport default class extends Vue {\r\n  private validateUsername = (rule: any, value: string, callback: Function) => {\r\n    if (!value) {\r\n      callback(new Error('请输入用户名'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private validatePassword = (rule: any, value: string, callback: Function) => {\r\n    if (value.length < 6) {\r\n      callback(new Error('密码必须在6位以上'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private loginForm = {\r\n    username: 'admin',\r\n    password: '123456',\r\n  } as {\r\n    username: String\r\n    password: String\r\n  }\r\n\r\n  loginRules = {\r\n    username: [{ validator: this.validateUsername, trigger: 'blur' }],\r\n    password: [{ validator: this.validatePassword, trigger: 'blur' }],\r\n  }\r\n  private loading = false\r\n  private redirect?: string\r\n\r\n  @Watch('$route', { immediate: true })\r\n  private onRouteChange(route: Route) {}\r\n\r\n  // 登录\r\n  private handleLogin() {\r\n    (this.$refs.loginForm as ElForm).validate(async (valid: boolean) => {\r\n      if (valid) {\r\n        this.loading = true\r\n        await UserModule.Login(this.loginForm as any)\r\n          .then((res: any) => {\r\n            if (String(res.code) === '1') {\r\n              //登录成功，跳转到系统首页\r\n              this.$router.push('/')\r\n            } else {\r\n              // this.$message.error(res.msg)\r\n              this.loading = false\r\n            }\r\n          })\r\n          .catch(() => {\r\n            // this.$message.error('用户名或密码错误！')\r\n            this.loading = false\r\n          })\r\n      } else {\r\n        return false\r\n      }\r\n    })\r\n  }\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoDA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/login", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <div class=\"login-box\">\r\n      <img src=\"@/assets/login/login-l.png\" alt=\"\" />\r\n      <div class=\"login-form\">\r\n        <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\">\r\n          <div class=\"login-form-title\">\r\n            <img\r\n              src=\"@/assets/login/icon_logo.png\"\r\n              style=\"width: 149px; height: 38px\"\r\n              alt=\"\"\r\n            />\r\n            <!-- <span class=\"title-label\">苍穹外卖</span> -->\r\n          </div>\r\n          <el-form-item prop=\"username\">\r\n            <el-input\r\n              v-model=\"loginForm.username\"\r\n              type=\"text\"\r\n              auto-complete=\"off\"\r\n              placeholder=\"账号\"\r\n              prefix-icon=\"iconfont icon-user\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item prop=\"password\">\r\n            <el-input\r\n              v-model=\"loginForm.password\"\r\n              type=\"password\"\r\n              placeholder=\"密码\"\r\n              prefix-icon=\"iconfont icon-lock\"\r\n              @keyup.enter.native=\"handleLogin\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item style=\"width: 100%\">\r\n            <el-button\r\n              :loading=\"loading\"\r\n              class=\"login-btn\"\r\n              size=\"medium\"\r\n              type=\"primary\"\r\n              style=\"width: 100%\"\r\n              @click.native.prevent=\"handleLogin\"\r\n            >\r\n              <span v-if=\"!loading\">登录</span>\r\n              <span v-else>登录中...</span>\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { Route } from 'vue-router'\r\nimport { Form as ElForm, Input } from 'element-ui'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport { isValidUsername } from '@/utils/validate'\r\n\r\n@Component({\r\n  name: 'Login',\r\n})\r\nexport default class extends Vue {\r\n  private validateUsername = (rule: any, value: string, callback: Function) => {\r\n    if (!value) {\r\n      callback(new Error('请输入用户名'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private validatePassword = (rule: any, value: string, callback: Function) => {\r\n    if (value.length < 6) {\r\n      callback(new Error('密码必须在6位以上'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n  private loginForm = {\r\n    username: 'admin',\r\n    password: '123456',\r\n  } as {\r\n    username: String\r\n    password: String\r\n  }\r\n\r\n  loginRules = {\r\n    username: [{ validator: this.validateUsername, trigger: 'blur' }],\r\n    password: [{ validator: this.validatePassword, trigger: 'blur' }],\r\n  }\r\n  private loading = false\r\n  private redirect?: string\r\n\r\n  @Watch('$route', { immediate: true })\r\n  private onRouteChange(route: Route) {}\r\n\r\n  // 登录\r\n  private handleLogin() {\r\n    (this.$refs.loginForm as ElForm).validate(async (valid: boolean) => {\r\n      if (valid) {\r\n        this.loading = true\r\n        await UserModule.Login(this.loginForm as any)\r\n          .then((res: any) => {\r\n            if (String(res.code) === '1') {\r\n              //登录成功，跳转到系统首页\r\n              this.$router.push('/')\r\n            } else {\r\n              // this.$message.error(res.msg)\r\n              this.loading = false\r\n            }\r\n          })\r\n          .catch(() => {\r\n            // this.$message.error('用户名或密码错误！')\r\n            this.loading = false\r\n          })\r\n      } else {\r\n        return false\r\n      }\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  // background: #476dbe;\r\n  background-color: #333;\r\n}\r\n\r\n.login-box {\r\n  width: 1000px;\r\n  height: 474.38px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  img {\r\n    width: 60%;\r\n    height: auto;\r\n  }\r\n}\r\n\r\n.title {\r\n  margin: 0px auto 10px auto;\r\n  text-align: left;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  background: #ffffff;\r\n  width: 40%;\r\n  border-radius: 0px 8px 8px 0px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  .el-form {\r\n    width: 214px;\r\n    height: 307px;\r\n  }\r\n  .el-form-item {\r\n    margin-bottom: 30px;\r\n  }\r\n  .el-form-item.is-error .el-input__inner {\r\n    border: 0 !important;\r\n    border-bottom: 1px solid #fd7065 !important;\r\n    background: #fff !important;\r\n  }\r\n  .input-icon {\r\n    height: 32px;\r\n    width: 18px;\r\n    margin-left: -2px;\r\n  }\r\n  .el-input__inner {\r\n    border: 0;\r\n    border-bottom: 1px solid #e9e9e8;\r\n    border-radius: 0;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333333;\r\n    height: 32px;\r\n    line-height: 32px;\r\n  }\r\n  .el-input__prefix {\r\n    left: 0;\r\n  }\r\n  .el-input--prefix .el-input__inner {\r\n    padding-left: 26px;\r\n  }\r\n  .el-input__inner::placeholder {\r\n    color: #aeb5c4;\r\n  }\r\n  .el-form-item--medium .el-form-item__content {\r\n    line-height: 32px;\r\n  }\r\n  .el-input--medium .el-input__icon {\r\n    line-height: 32px;\r\n  }\r\n}\r\n\r\n.login-btn {\r\n  border-radius: 17px;\r\n  padding: 11px 20px !important;\r\n  margin-top: 10px;\r\n  font-weight: 500;\r\n  font-size: 12px;\r\n  border: 0;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  // background: #09a57a;\r\n  background-color: #ffc200;\r\n  &:hover,\r\n  &:focus {\r\n    // background: #09a57a;\r\n    background-color: #ffc200;\r\n    color: #ffffff;\r\n  }\r\n}\r\n.login-form-title {\r\n  height: 36px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-bottom: 40px;\r\n  .title-label {\r\n    font-weight: 500;\r\n    font-size: 20px;\r\n    color: #333333;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n</style>\r\n"]}]}