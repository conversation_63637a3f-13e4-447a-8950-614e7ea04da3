{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/index.vue?vue&type=template&id=27a2ef37&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"dashboard-container\">\n  <div class=\"container\">\n    <div class=\"tableBar\">\n      <label style=\"margin-right: 10px\">菜品名称：</label>\n      <el-input v-model=\"input\"\n                placeholder=\"请填写菜品名称\"\n                style=\"width: 14%\"\n                clearable\n                @clear=\"init\"\n                @keyup.enter.native=\"initFun\" />\n\n      <label style=\"margin-right: 10px; margin-left: 20px\">菜品分类：</label>\n      <el-select v-model=\"categoryId\"\n                 style=\"width: 14%\"\n                 placeholder=\"请选择\"\n                 clearable\n                 @clear=\"init\">\n        <el-option v-for=\"item in dishCategoryList\"\n                   :key=\"item.value\"\n                   :label=\"item.label\"\n                   :value=\"item.value\" />\n      </el-select>\n\n      <label style=\"margin-right: 10px; margin-left: 20px\">售卖状态：</label>\n      <el-select v-model=\"dishStatus\"\n                 style=\"width: 14%\"\n                 placeholder=\"请选择\"\n                 clearable\n                 @clear=\"init\">\n        <el-option v-for=\"item in saleStatus\"\n                   :key=\"item.value\"\n                   :label=\"item.label\"\n                   :value=\"item.value\" />\n      </el-select>\n      <el-button class=\"normal-btn continue\"\n                 @click=\"init(true)\">\n        查询\n      </el-button>\n\n      <div class=\"tableLab\">\n        <span class=\"delBut non\"\n              @click=\"deleteHandle('批量', null)\">批量删除</span>\n        <!-- <span class=\"blueBug non\" @click=\"statusHandle('1')\">批量启售</span>\n        <span\n          style=\"border: none\"\n          class=\"delBut non\"\n          @click=\"statusHandle('0')\"\n          >批量停售</span\n        > -->\n        <el-button type=\"primary\"\n                   style=\"margin-left: 15px\"\n                   @click=\"addDishtype('add')\">\n          + 新建菜品\n        </el-button>\n      </div>\n    </div>\n    <el-table v-if=\"tableData.length\"\n              :data=\"tableData\"\n              stripe\n              class=\"tableBox\"\n              @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\"\n                       width=\"25\" />\n      <el-table-column prop=\"name\"\n                       label=\"菜品名称\" />\n      <el-table-column prop=\"image\"\n                       label=\"图片\">\n        <template slot-scope=\"{ row }\">\n          <el-image style=\"width: 80px; height: 40px; border: none; cursor: pointer\"\n                    :src=\"row.image\">\n            <div slot=\"error\"\n                 class=\"image-slot\">\n              <img src=\"./../../assets/noImg.png\"\n                   style=\"width: auto; height: 40px; border: none\">\n            </div>\n          </el-image>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"categoryName\"\n                       label=\"菜品分类\" />\n      <el-table-column label=\"售价\">\n        <template slot-scope=\"scope\">\n          <span style=\"margin-right: 10px\">￥{{ (scope.row.price ).toFixed(2)*100/100 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"售卖状态\">\n        <template slot-scope=\"scope\">\n          <div class=\"tableColumn-status\"\n               :class=\"{ 'stop-use': String(scope.row.status) === '0' }\">\n            {{ String(scope.row.status) === '0' ? '停售' : '启售' }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"updateTime\"\n                       label=\"最后操作时间\" />\n      <el-table-column label=\"操作\"\n                       width=\"250\"\n                       align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\"\n                     size=\"small\"\n                     class=\"blueBug\"\n                     @click=\"addDishtype(scope.row.id)\">\n            修改\n          </el-button>\n          <el-button type=\"text\"\n                     size=\"small\"\n                     class=\"delBut\"\n                     @click=\"deleteHandle('单删', scope.row.id)\">\n            删除\n          </el-button>\n          <el-button type=\"text\"\n                     size=\"small\"\n                     class=\"non\"\n                     :class=\"{\n                       blueBug: scope.row.status == '0',\n                       delBut: scope.row.status != '0'\n                     }\"\n                     @click=\"statusHandle(scope.row)\">\n            {{ scope.row.status == '0' ? '启售' : '停售' }}\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <Empty v-else\n           :is-search=\"isSearch\" />\n    <el-pagination v-if=\"counts > 10\"\n                   class=\"pageList\"\n                   :page-sizes=\"[10, 20, 30, 40]\"\n                   :page-size=\"pageSize\"\n                   layout=\"total, sizes, prev, pager, next, jumper\"\n                   :total=\"counts\"\n                   @size-change=\"handleSizeChange\"\n                   @current-change=\"handleCurrentChange\" />\n  </div>\n</div>\n", null]}