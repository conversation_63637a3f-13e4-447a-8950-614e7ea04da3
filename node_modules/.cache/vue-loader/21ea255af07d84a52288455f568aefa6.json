{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Navbar/index.vue?vue&type=template&id=4ace4340&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Navbar/index.vue", "mtime": 1689143899000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"navbar\">\n  <div class=\"statusBox\">\n    <hamburger id=\"hamburger-container\"\n               :is-active=\"sidebar.opened\"\n               class=\"hamburger-container\"\n               @toggleClick=\"toggleSideBar\" />\n    <span v-if=\"status===1\"\n          class=\"businessBtn\">营业中</span>\n    <span v-else\n          class=\"businessBtn closing\">打烊中</span>\n  </div>\n\n  <div :key=\"restKey\"\n       class=\"right-menu\">\n    <div class=\"rightStatus\">\n      <audio ref=\"audioVo\"\n             hidden>\n        <source src=\"./../../../assets/preview.mp3\" type=\"audio/mp3\" />\n      </audio>\n      <audio ref=\"audioVo2\"\n             hidden>\n        <source src=\"./../../../assets/reminder.mp3\" type=\"audio/mp3\" />\n      </audio>\n      <span class=\"navicon operatingState\" @click=\"handleStatus\"><i />营业状态设置</span>\n    </div>\n    <div class=\"avatar-wrapper\">\n      <div :class=\"shopShow?'userInfo':''\"\n           @mouseenter=\"toggleShow\"\n           @mouseleave=\"mouseLeaves\">\n        <el-button type=\"primary\"\n                   :class=\"shopShow?'active':''\">\n          {{ name }}<i class=\"el-icon-arrow-down\" />\n        </el-button>\n        <div v-if=\"shopShow\"\n             class=\"userList\">\n          <p class=\"amendPwdIcon\"\n             @click=\"handlePwd\">\n            修改密码<i />\n          </p>\n          <p class=\"outLogin\"\n             @click=\"logout\">\n            退出登录<i />\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n  <!-- 营业状态弹层 -->\n  <el-dialog title=\"营业状态设置\"\n             :visible.sync=\"dialogVisible\"\n             width=\"25%\"\n             :show-close=\"false\">\n    <el-radio-group v-model=\"setStatus\">\n      <el-radio :label=\"1\">\n        营业中\n        <span>当前餐厅处于营业状态，自动接收任何订单，可点击打烊进入店铺打烊状态。</span>\n      </el-radio>\n      <el-radio :label=\"0\">\n        打烊中\n        <span>当前餐厅处于打烊状态，仅接受营业时间内的预定订单，可点击营业中手动恢复营业状态。</span>\n      </el-radio>\n    </el-radio-group>\n    <span slot=\"footer\"\n          class=\"dialog-footer\">\n      <el-button @click=\"dialogVisible = false\">取 消</el-button>\n      <el-button type=\"primary\"\n                 @click=\"handleSave\">确 定</el-button>\n    </span>\n  </el-dialog>\n  <!-- end -->\n  <!-- 修改密码 -->\n  <Password :dialog-form-visible=\"dialogFormVisible\"\n            @handleclose=\"handlePwdClose\" />\n  <!-- end -->\n</div>\n", null]}