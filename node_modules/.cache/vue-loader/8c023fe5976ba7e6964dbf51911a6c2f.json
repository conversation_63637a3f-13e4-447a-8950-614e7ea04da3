{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/userStatistics.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/userStatistics.vue", "mtime": 1656314104000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'UserStatistics',\n})\nexport default class extends Vue {\n  @Prop() private userdata!: any\n  @Watch('userdata')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('usermain') as any\n    const myChart = echarts.init(chartDom)\n    var option: any\n    option = {\n      // legend: {\n      //   itemHeight: 3, //图例高\n      //   itemWidth: 12, //图例宽\n      //   icon: 'rect', //图例\n      //   show: true,\n      //   top: 'bottom',\n      //   data: ['用户总量', '新增用户'],\n      // },\n      tooltip: {\n        trigger: 'axis',\n        backgroundColor: '#fff', //背景颜色（此时为默认色）\n        borderRadius: 2, //边框圆角\n        textStyle: {\n          color: '#333', //字体颜色\n          fontSize: 12, //字体大小\n          fontWeight: 300,\n        },\n      },\n      grid: {\n        top: '5%',\n        left: '20',\n        right: '50',\n        bottom: '12%',\n        containLabel: true,\n      },\n      xAxis: {\n        type: 'category',\n        boundaryGap: false,\n        axisLabel: {\n          //X轴字体颜色\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n        },\n        axisLine: {\n          //X轴线颜色\n          lineStyle: {\n            color: '#E5E4E4',\n            width: 1, //x轴线的宽度\n          },\n        },\n        data: this.userdata.dateList, //后端传来的动态数据\n      },\n      yAxis: [\n        {\n          type: 'value',\n          min: 0,\n          //max: 500,\n          //interval: 100,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px',\n            },\n            // formatter: \"{value} ml\",//单位\n          },\n        }, //左侧值\n      ],\n      series: [\n        {\n          name: '用户总量',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10,\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#FFD000',\n              lineStyle: {\n                color: '#FFD000',\n              },\n            },\n            emphasis: {\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FFC100',\n            },\n          },\n\n          data: this.userdata.totalUserList,\n        },\n        {\n          name: '新增用户',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10, //圆点大小\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#FD7F7F',\n              fontWeigth: 300,\n              lineStyle: {\n                color: '#FD7F7F',\n              },\n            },\n            emphasis: {\n              // 圆点颜色\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FD7F7F',\n            },\n          },\n\n          data: this.userdata.newUserList,\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n", {"version": 3, "sources": ["userStatistics.vue"], "names": [], "mappings": ";AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userStatistics.vue", "sourceRoot": "src/views/statistics/components", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"homeTitle\">用户统计</h2>\n    <div class=\"charBox\">\n      <div id=\"usermain\" style=\"width: 100%; height: 320px\"></div>\n      <ul class=\"orderListLine user\">\n        <li class=\"one\"><span></span>用户总量（个）</li>\n        <li class=\"three\"><span></span>新增用户（个）</li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'UserStatistics',\n})\nexport default class extends Vue {\n  @Prop() private userdata!: any\n  @Watch('userdata')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('usermain') as any\n    const myChart = echarts.init(chartDom)\n    var option: any\n    option = {\n      // legend: {\n      //   itemHeight: 3, //图例高\n      //   itemWidth: 12, //图例宽\n      //   icon: 'rect', //图例\n      //   show: true,\n      //   top: 'bottom',\n      //   data: ['用户总量', '新增用户'],\n      // },\n      tooltip: {\n        trigger: 'axis',\n        backgroundColor: '#fff', //背景颜色（此时为默认色）\n        borderRadius: 2, //边框圆角\n        textStyle: {\n          color: '#333', //字体颜色\n          fontSize: 12, //字体大小\n          fontWeight: 300,\n        },\n      },\n      grid: {\n        top: '5%',\n        left: '20',\n        right: '50',\n        bottom: '12%',\n        containLabel: true,\n      },\n      xAxis: {\n        type: 'category',\n        boundaryGap: false,\n        axisLabel: {\n          //X轴字体颜色\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n        },\n        axisLine: {\n          //X轴线颜色\n          lineStyle: {\n            color: '#E5E4E4',\n            width: 1, //x轴线的宽度\n          },\n        },\n        data: this.userdata.dateList, //后端传来的动态数据\n      },\n      yAxis: [\n        {\n          type: 'value',\n          min: 0,\n          //max: 500,\n          //interval: 100,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px',\n            },\n            // formatter: \"{value} ml\",//单位\n          },\n        }, //左侧值\n      ],\n      series: [\n        {\n          name: '用户总量',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10,\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#FFD000',\n              lineStyle: {\n                color: '#FFD000',\n              },\n            },\n            emphasis: {\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FFC100',\n            },\n          },\n\n          data: this.userdata.totalUserList,\n        },\n        {\n          name: '新增用户',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10, //圆点大小\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#FD7F7F',\n              fontWeigth: 300,\n              lineStyle: {\n                color: '#FD7F7F',\n              },\n            },\n            emphasis: {\n              // 圆点颜色\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FD7F7F',\n            },\n          },\n\n          data: this.userdata.newUserList,\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n</script>\n<style scoped>\n</style>\n"]}]}