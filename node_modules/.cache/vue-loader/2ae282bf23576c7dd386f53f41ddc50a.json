{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { RouteRecord, Route } from 'vue-router'\r\n\r\n@Component({\r\n  'name': 'Breadcrumb'\r\n})\r\n\r\nexport default class extends Vue {\r\n  private breadcrumbs: RouteRecord[] = []\r\n  @Watch('$route')\r\n  private onRouteChange(route: Route) {\r\n    // if you go to the redirect page, do not update the breadcrumbs\r\n    if (route.path.startsWith('/redirect/')) {\r\n      return\r\n    }\r\n\r\n    this.getBreadcrumb()\r\n  }\r\n\r\n  created () {\r\n    this.getBreadcrumb()\r\n  }\r\n\r\n  private getBreadcrumb () {\r\n    let matched = this.$route.matched.filter(\r\n      item => item.meta && item.meta.title\r\n    )\r\n    const first = matched[0]\r\n    // if (!this.isDashboard(first)) {\r\n    //   matched = [\r\n    //     { path: '/', meta: { title: '集团管理' } } as RouteRecord\r\n    //   ].concat(matched)\r\n    // }\r\n    this.breadcrumbs = matched.filter(item => {\r\n      return item.meta && item.meta.title && item.meta.breadcrumb !== false\r\n    })\r\n  }\r\n\r\n  private isDashboard (route: RouteRecord) {\r\n    const name = route && route.meta && route.meta.title\r\n    return name === '集团管理'\r\n  }\r\n\r\n  private pathCompile (path: string) {\r\n    // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n    const { params } = this.$route\r\n    const toPath = pathToRegexp.compile(path)\r\n    return toPath(params)\r\n  }\r\n\r\n  private handleLink (item: any) {\r\n    const { redirect, path } = item\r\n    if (redirect) {\r\n      this.$router.push(redirect)\r\n      return\r\n    }\r\n    this.$router.push(this.pathCompile(path))\r\n  }\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb\r\n    class=\"app-breadcrumb\"\r\n    separator=\"/\"\r\n  >\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item\r\n        v-for=\"(item, index) in breadcrumbs\"\r\n        :key=\"item.path\"\r\n      >\r\n        <span\r\n          v-if=\"item.redirect === 'noredirect' || index === breadcrumbs.length-1\"\r\n          class=\"no-redirect\"\r\n        >{{ item.meta.title }}</span>\r\n        <a\r\n          v-else\r\n          @click.prevent=\"handleLink(item)\"\r\n        >{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { Component, Vue, Watch } from 'vue-property-decorator'\r\nimport { RouteRecord, Route } from 'vue-router'\r\n\r\n@Component({\r\n  'name': 'Breadcrumb'\r\n})\r\n\r\nexport default class extends Vue {\r\n  private breadcrumbs: RouteRecord[] = []\r\n  @Watch('$route')\r\n  private onRouteChange(route: Route) {\r\n    // if you go to the redirect page, do not update the breadcrumbs\r\n    if (route.path.startsWith('/redirect/')) {\r\n      return\r\n    }\r\n\r\n    this.getBreadcrumb()\r\n  }\r\n\r\n  created () {\r\n    this.getBreadcrumb()\r\n  }\r\n\r\n  private getBreadcrumb () {\r\n    let matched = this.$route.matched.filter(\r\n      item => item.meta && item.meta.title\r\n    )\r\n    const first = matched[0]\r\n    // if (!this.isDashboard(first)) {\r\n    //   matched = [\r\n    //     { path: '/', meta: { title: '集团管理' } } as RouteRecord\r\n    //   ].concat(matched)\r\n    // }\r\n    this.breadcrumbs = matched.filter(item => {\r\n      return item.meta && item.meta.title && item.meta.breadcrumb !== false\r\n    })\r\n  }\r\n\r\n  private isDashboard (route: RouteRecord) {\r\n    const name = route && route.meta && route.meta.title\r\n    return name === '集团管理'\r\n  }\r\n\r\n  private pathCompile (path: string) {\r\n    // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n    const { params } = this.$route\r\n    const toPath = pathToRegexp.compile(path)\r\n    return toPath(params)\r\n  }\r\n\r\n  private handleLink (item: any) {\r\n    const { redirect, path } = item\r\n    if (redirect) {\r\n      this.$router.push(redirect)\r\n      return\r\n    }\r\n    this.$router.push(this.pathCompile(path))\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-breadcrumb__inner,\r\n.el-breadcrumb__inner a {\r\n  font-weight: 400 !important;\r\n}\r\n\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}