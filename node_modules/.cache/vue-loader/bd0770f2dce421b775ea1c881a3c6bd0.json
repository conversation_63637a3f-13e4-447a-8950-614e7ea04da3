{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue?vue&type=template&id=19442e4b&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/employee/index.vue", "mtime": 1756349952451}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"dashboard-container\">\n  <div class=\"container\">\n    <div class=\"tableBar\">\n      <label style=\"margin-right: 5px\">员工姓名：</label>\n      <el-input\n        v-model=\"name\"\n        placeholder=\"请输入员工姓名\"\n        style=\"width: 15%\"\n      />\n      <el-button type=\"primary\" style=\"margin-left: 25px\" @click=\"pageQuery()\"\n        >查询</el-button\n      >\n      <el-button type=\"primary\" style=\"float: right\">+添加员工</el-button>\n    </div>\n    <el-table :data=\"records\" stripe style=\"width: 100%\">\n      <el-table-column prop=\"name\" label=\"员工姓名\" width=\"180\">\n      </el-table-column>\n      <el-table-column prop=\"username\" label=\"账号\" width=\"180\">\n      </el-table-column>\n      <el-table-column prop=\"phone\" label=\"手机号\"> </el-table-column>\n      <el-table-column prop=\"status\" label=\"账号状态\">\n        <template slot-scope=\"scope\">{{\n          scope.row.status === 0 ? '禁用' : '启用'\n        }}</template>\n      </el-table-column>\n      <el-table-column prop=\"updateTime\" label=\"最后操作时间\">\n      </el-table-column>\n      <el-table-column label=\"操作\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\">修改</el-button>\n          <el-button type=\"text\" @click=\"handleStartOrStop(scope.row)\">{{\n            scope.row.status === 1 ? '禁用' : '启用'\n          }}</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n    class=\"pageList\"\n    @size-change=\"handleSizeChange\"\n    @current-change=\"handleCurrentChange\"\n    :current-page=\"page\"\n    :page-sizes=\"[10, 20, 30, 40,50]\"\n    :page-size=\"pageSize\"\n    layout=\"total, sizes, prev, pager, next, jumper\"\n    :total=\"total\">\n  </el-pagination>\n  </div>\n</div>\n", null]}