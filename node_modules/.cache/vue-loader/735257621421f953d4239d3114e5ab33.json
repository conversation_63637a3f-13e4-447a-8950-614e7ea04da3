{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/AppMain.vue", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/AppMain.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./AppMain.vue?vue&type=template&id=078753dd&scoped=true\"\nimport script from \"./AppMain.vue?vue&type=script&lang=ts\"\nexport * from \"./AppMain.vue?vue&type=script&lang=ts\"\nimport style0 from \"./AppMain.vue?vue&type=style&index=0&id=078753dd&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"078753dd\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('078753dd')) {\n      api.createRecord('078753dd', component.options)\n    } else {\n      api.reload('078753dd', component.options)\n    }\n    module.hot.accept(\"./AppMain.vue?vue&type=template&id=078753dd&scoped=true\", function () {\n      api.rerender('078753dd', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/layout/components/AppMain.vue\"\nexport default component.exports"]}