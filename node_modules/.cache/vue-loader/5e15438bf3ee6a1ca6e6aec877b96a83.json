{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/components/AddDish.vue", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/components/AddDish.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./AddDish.vue?vue&type=template&id=d17ba2f4&scoped=true\"\nimport script from \"./AddDish.vue?vue&type=script&lang=ts\"\nexport * from \"./AddDish.vue?vue&type=script&lang=ts\"\nimport style0 from \"./AddDish.vue?vue&type=style&index=0&id=d17ba2f4&lang=scss\"\nimport style1 from \"./AddDish.vue?vue&type=style&index=1&id=d17ba2f4&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d17ba2f4\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('d17ba2f4')) {\n      api.createRecord('d17ba2f4', component.options)\n    } else {\n      api.reload('d17ba2f4', component.options)\n    }\n    module.hot.accept(\"./AddDish.vue?vue&type=template&id=d17ba2f4&scoped=true\", function () {\n      api.rerender('d17ba2f4', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/setmeal/components/AddDish.vue\"\nexport default component.exports"]}