{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue?vue&type=template&id=1ee0b527&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"upload-item\">\n  <el-upload ref=\"uploadfiles\"\n             :accept=\"type\"\n             :class=\"{ borderNone: imageUrl }\"\n             class=\"avatar-uploader\"\n             action=\"/api/common/upload\"\n             :show-file-list=\"false\"\n             :on-success=\"handleAvatarSuccess\"\n             :on-remove=\"handleRemove\"\n             :on-error=\"handleError\"\n             :before-upload=\"beforeAvatarUpload\"\n             :headers=\"headers\">\n    <img v-if=\"imageUrl\"\n         :src=\"imageUrl\"\n         class=\"avatar\">\n\n    <i v-else\n       class=\"el-icon-plus avatar-uploader-icon\" />\n    <span v-if=\"imageUrl\"\n          class=\"el-upload-list__item-actions\">\n      <span class=\"el-upload-span\"\n            @click.stop=\"oploadImgDel\">\n        删除图片\n      </span>\n      <span class=\"el-upload-span\"> 重新上传 </span>\n    </span>\n  </el-upload>\n  <p class=\"upload-tips\">\n    <slot />\n  </p>\n</div>\n", null]}