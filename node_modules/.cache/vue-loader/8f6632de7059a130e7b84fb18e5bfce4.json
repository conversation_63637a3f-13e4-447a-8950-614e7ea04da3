{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderview.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/orderview.vue", "mtime": 1655868615000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue, Prop } from 'vue-property-decorator'\nimport { getday } from '@/utils/formValidate'\n@Component({\n  name: 'Orderview',\n})\nexport default class extends Vue {\n  @Prop() private orderviewData!: any\n  get days() {\n    return getday()\n  }\n}\n", {"version": 3, "sources": ["orderview.vue"], "names": [], "mappings": ";AA4DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "orderview.vue", "sourceRoot": "src/views/dashboard/components", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"homeTitle\">\n      订单管理<i>{{ days[1] }}</i\n      ><span><router-link to=\"/order\">订单明细</router-link></span>\n    </h2>\n    <div class=\"orderviewBox\">\n      <ul>\n        <li>\n          <span class=\"status\"\n            ><i class=\"iconfont icon-waiting\"></i>待接单</span\n          >\n          <span class=\"num tip\"\n            ><router-link to=\"/order?status=2\">{{\n              orderviewData.waitingOrders\n            }}</router-link></span\n          >\n        </li>\n        <li>\n          <span class=\"status\"\n            ><i class=\"iconfont icon-staySway\"></i>待派送</span\n          >\n          <span class=\"num tip\"\n            ><router-link to=\"/order?status=3\">{{\n              orderviewData.deliveredOrders\n            }}</router-link></span\n          >\n        </li>\n        <li>\n          <span class=\"status\"\n            ><i class=\"iconfont icon-complete\"></i>已完成</span\n          >\n          <span class=\"num\"\n            ><router-link to=\"/order?status=5\">{{\n              orderviewData.completedOrders\n            }}</router-link></span\n          >\n        </li>\n        <li>\n          <span class=\"status\"><i class=\"iconfont icon-cancel\"></i>已取消</span>\n          <span class=\"num\"\n            ><router-link to=\"/order?status=6\">{{\n              orderviewData.cancelledOrders\n            }}</router-link></span\n          >\n        </li>\n        <li>\n          <span class=\"status\"><i class=\"iconfont icon-all\"></i>全部订单</span>\n          <span class=\"num\"\n            ><router-link to=\"/order\">{{\n              orderviewData.allOrders\n            }}</router-link></span\n          >\n        </li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Prop } from 'vue-property-decorator'\nimport { getday } from '@/utils/formValidate'\n@Component({\n  name: 'Orderview',\n})\nexport default class extends Vue {\n  @Prop() private orderviewData!: any\n  get days() {\n    return getday()\n  }\n}\n</script>\n"]}]}