{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component } from 'vue-property-decorator'\r\nimport { mixins } from 'vue-class-component'\r\nimport { DeviceType, AppModule } from '@/store/modules/app'\r\nimport { AppMain, Navbar, Sidebar } from './components'\r\nimport ResizeMixin from './mixin/resize'\r\n\r\n@Component({\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    Sidebar,\r\n  },\r\n})\r\nexport default class extends mixins(ResizeMixin) {\r\n  get classObj() {\r\n    return {\r\n      hideSidebar: !this.sidebar.opened,\r\n      openSidebar: this.sidebar.opened,\r\n      withoutAnimation: this.sidebar.withoutAnimation,\r\n      mobile: this.device === DeviceType.Mobile,\r\n    }\r\n  }\r\n\r\n  private handleClickOutside() {\r\n    AppModule.CloseSideBar(false)\r\n  }\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAgBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\">\r\n    <div\r\n      v-if=\"classObj.mobile && sidebar.opened\"\r\n      class=\"drawer-bg\"\r\n      @click=\"handleClickOutside\"\r\n    />\r\n    <sidebar class=\"sidebar-container\" />\r\n    <div class=\"main-container\">\r\n      <navbar />\r\n      <app-main />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component } from 'vue-property-decorator'\r\nimport { mixins } from 'vue-class-component'\r\nimport { DeviceType, AppModule } from '@/store/modules/app'\r\nimport { AppMain, Navbar, Sidebar } from './components'\r\nimport ResizeMixin from './mixin/resize'\r\n\r\n@Component({\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    Sidebar,\r\n  },\r\n})\r\nexport default class extends mixins(ResizeMixin) {\r\n  get classObj() {\r\n    return {\r\n      hideSidebar: !this.sidebar.opened,\r\n      openSidebar: this.sidebar.opened,\r\n      withoutAnimation: this.sidebar.withoutAnimation,\r\n      mobile: this.device === DeviceType.Mobile,\r\n    }\r\n  }\r\n\r\n  private handleClickOutside() {\r\n    AppModule.CloseSideBar(false)\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-wrapper {\r\n  @include clearfix;\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  min-width: 1366px;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n}\r\n\r\n.main-container {\r\n  height: 100%;\r\n  background: #f3f4f7;\r\n  position: relative;\r\n  width: calc(100% - 190px);\r\n}\r\n\r\n.drawer-bg {\r\n  background: #000;\r\n  opacity: 0.3;\r\n  width: 100%;\r\n  top: 0;\r\n  height: 100%;\r\n  position: absolute;\r\n  z-index: 999;\r\n}\r\n\r\n.main-container {\r\n  min-height: 100%;\r\n  transition: margin-left 0.28s;\r\n  margin-left: $sideBarWidth;\r\n  background: $gray-5;\r\n  position: relative;\r\n}\r\n\r\n.sidebar-container {\r\n  transition: width 0.28s;\r\n  width: $sideBarWidth !important;\r\n  height: 100%;\r\n  position: fixed;\r\n  // font-size: 0px;\r\n  top: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: 1001;\r\n  overflow: hidden;\r\n}\r\n\r\n.hideSidebar {\r\n  .main-container {\r\n    margin-left: 80px;\r\n    width: calc(100% - 80px);\r\n  }\r\n\r\n  .sidebar-container {\r\n    width: 80px !important;\r\n  }\r\n}\r\n\r\n/* for mobile response 适配移动端 */\r\n.mobile {\r\n  .main-container {\r\n    margin-left: 0px;\r\n  }\r\n\r\n  .sidebar-container {\r\n    transition: transform 0.28s;\r\n    width: $sideBarWidth !important;\r\n  }\r\n\r\n  &.openSidebar {\r\n    position: fixed;\r\n    top: 0;\r\n  }\r\n\r\n  &.hideSidebar {\r\n    .sidebar-container {\r\n      pointer-events: none;\r\n      transition-duration: 0.3s;\r\n      transform: translate3d(-$sideBarWidth, 0, 0);\r\n    }\r\n  }\r\n}\r\n\r\n.withoutAnimation {\r\n  .main-container,\r\n  .sidebar-container {\r\n    transition: none;\r\n  }\r\n}\r\n</style>\r\n"]}]}