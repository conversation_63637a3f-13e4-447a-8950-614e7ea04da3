{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js??ref--13-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/index.vue", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/index.vue", "mtime": 1694682430000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/eslint-loader/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=23b821be&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=ts\"\nexport * from \"./index.vue?vue&type=script&lang=ts\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=23b821be&lang=scss\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=23b821be&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23b821be\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('23b821be')) {\n      api.createRecord('23b821be', component.options)\n    } else {\n      api.reload('23b821be', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=23b821be&scoped=true\", function () {\n      api.rerender('23b821be', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/setmeal/index.vue\"\nexport default component.exports"]}