{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/orderDetails/tabChange.vue?vue&type=template&id=662a7404", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/orderDetails/tabChange.vue", "mtime": 1655711738000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"tab-change\">\n  <div v-for=\"item in changedOrderList\"\n       :key=\"item.value\"\n       class=\"tab-item\"\n       :class=\"{ active: item.value === activeIndex }\"\n       @click=\"tabChange(item.value)\">\n    <el-badge :class=\"{'special-item':item.num<10}\"\n              class=\"item\"\n              :value=\"item.num > 99 ? '99+' : item.num\"\n              :hidden=\"!([2, 3, 4].includes(item.value) && item.num)\">\n      {{ item.label }}\n    </el-badge>\n  </div>\n</div>\n", null]}