{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue?vue&type=template&id=ac903da6", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue", "mtime": 1655864776000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"container\">\n  <h2 class=\"homeTitle\">\n    今日数据<i>{{ days[1] }}</i\n    ><span><router-link to=\"statistics\">详细数据</router-link></span>\n  </h2>\n  <div class=\"overviewBox\">\n    <ul>\n      <li>\n        <p class=\"tit\">营业额</p>\n        <p class=\"num\">¥ {{ overviewData.turnover }}</p>\n      </li>\n      <li>\n        <p class=\"tit\">有效订单</p>\n        <p class=\"num\">{{ overviewData.validOrderCount }}</p>\n      </li>\n      <li>\n        <p class=\"tit\">订单完成率</p>\n        <p class=\"num\">\n          {{ (overviewData.orderCompletionRate * 100).toFixed(0) }}%\n        </p>\n      </li>\n      <li>\n        <p class=\"tit\">平均客单价</p>\n        <p class=\"num\">¥ {{ overviewData.unitPrice }}</p>\n      </li>\n\n      <li>\n        <p class=\"tit\">新增用户</p>\n        <p class=\"num\">{{ overviewData.newUsers }}</p>\n      </li>\n    </ul>\n  </div>\n</div>\n", null]}