{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/components/AddDish.vue?vue&type=template&id=d17ba2f4&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/components/AddDish.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"addDish\">\n  <div class=\"leftCont\">\n    <div v-show=\"seachKey.trim() == ''\"\n         class=\"tabBut\">\n      <span v-for=\"(item, index) in dishType\"\n            :key=\"index\"\n            :class=\"{ act: index == keyInd }\"\n            @click=\"checkTypeHandle(index, item.id)\">{{ item.name }}</span>\n    </div>\n    <div class=\"tabList\">\n      <div class=\"table\"\n           :class=\"{ borderNone: !dishList.length }\">\n        <div v-if=\"dishList.length == 0\"\n             style=\"padding-left: 10px\">\n          <Empty />\n        </div>\n        <el-checkbox-group v-if=\"dishList.length > 0\"\n                           v-model=\"checkedList\"\n                           @change=\"checkedListHandle\">\n          <div v-for=\"(item, index) in dishList\"\n               :key=\"item.name + item.id\"\n               class=\"items\">\n            <el-checkbox :key=\"index\"\n                         :label=\"item.name\">\n              <div class=\"item\">\n                <span style=\"flex: 3; text-align: left\">{{\n                  item.dishName\n                }}</span>\n                <span>{{ item.status == 0 ? '停售' : '在售' }}</span>\n                <span>{{ (Number(item.price) ).toFixed(2)*100/100 }}</span>\n              </div>\n            </el-checkbox>\n          </div>\n        </el-checkbox-group>\n      </div>\n    </div>\n  </div>\n  <div class=\"ritCont\">\n    <div class=\"tit\">\n      已选菜品({{ checkedListAll.length }})\n    </div>\n    <div class=\"items\">\n      <div v-for=\"(item, ind) in checkedListAll\"\n           :key=\"ind\"\n           class=\"item\">\n        <span>{{ item.dishName || item.name }}</span>\n        <span class=\"price\">￥ {{ (Number(item.price) ).toFixed(2)*100/100 }} </span>\n        <span class=\"del\"\n              @click=\"delCheck(item.name)\">\n          <img src=\"./../../../assets/icons/<EMAIL>\"\n               alt=\"\">\n        </span>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}