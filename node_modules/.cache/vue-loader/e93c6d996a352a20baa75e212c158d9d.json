{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/orderDetails/tabChange.vue?vue&type=style&index=0&id=662a7404&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/orderDetails/tabChange.vue", "mtime": 1655711738000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n.tab-change {\n  display: flex;\n  border-radius: 4px;\n  margin-bottom: 20px;\n\n  .tab-item {\n    width: 120px;\n    height: 40px;\n    text-align: center;\n    line-height: 40px;\n    color: #333;\n    border: 1px solid #e5e4e4;\n    background-color: white;\n    border-left: none;\n    cursor: pointer;\n    .special-item {\n      .el-badge__content {\n        width: 20px;\n        padding: 0 5px;\n      }\n    }\n    .item {\n      .el-badge__content {\n        background-color: #fd3333 !important;\n        line-height: 18px;\n        height: auto;\n        min-width: 18px;\n        min-height: 18px;\n        // border-radius: 50%;\n      }\n      .el-badge__content.is-fixed {\n        top: 14px;\n        right: 2px;\n      }\n    }\n  }\n  .active {\n    background-color: #ffc200;\n    font-weight: bold;\n  }\n  .tab-item:first-child {\n    border-left: 1px solid #e5e4e4;\n  }\n}\n", {"version": 3, "sources": ["tabChange.vue"], "names": [], "mappings": ";AA0EA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "tabChange.vue", "sourceRoot": "src/views/orderDetails", "sourcesContent": ["<!--  -->\n<template>\n  <div class=\"tab-change\">\n    <div v-for=\"item in changedOrderList\"\n         :key=\"item.value\"\n         class=\"tab-item\"\n         :class=\"{ active: item.value === activeIndex }\"\n         @click=\"tabChange(item.value)\">\n      <el-badge :class=\"{'special-item':item.num<10}\"\n                class=\"item\"\n                :value=\"item.num > 99 ? '99+' : item.num\"\n                :hidden=\"!([2, 3, 4].includes(item.value) && item.num)\">\n        {{ item.label }}\n      </el-badge>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Vue, Component, Prop, Watch } from 'vue-property-decorator'\nimport { getOrderDetailPage } from '@/api/order'\n\n@Component({\n  name: 'TabChange'\n})\nexport default class extends Vue {\n  @Prop({ default: '' }) orderStatics: any\n  @Prop({ default: '' }) defaultActivity: any\n  private activeIndex: number = this.defaultActivity || 0\n\n  @Watch('defaultActivity')\n  private onChange(val) {\n    this.activeIndex = Number(val)\n  }\n\n  get changedOrderList() {\n    return [\n      {\n        label: '全部订单',\n        value: 0\n      },\n      {\n        label: '待接单',\n        value: 2,\n        num: this.orderStatics.toBeConfirmed\n      },\n      {\n        label: '待派送',\n        value: 3,\n        num: this.orderStatics.confirmed\n      },\n      {\n        label: '派送中',\n        value: 4,\n        num: this.orderStatics.deliveryInProgress\n      },\n      {\n        label: '已完成',\n        value: 5\n      },\n      {\n        label: '已取消',\n        value: 6\n      }\n    ]\n  }\n\n  private tabChange(activeIndex) {\n    this.activeIndex = activeIndex\n    this.$emit('tabChange', activeIndex)\n  }\n}\n</script>\n<style lang=\"scss\">\n.tab-change {\n  display: flex;\n  border-radius: 4px;\n  margin-bottom: 20px;\n\n  .tab-item {\n    width: 120px;\n    height: 40px;\n    text-align: center;\n    line-height: 40px;\n    color: #333;\n    border: 1px solid #e5e4e4;\n    background-color: white;\n    border-left: none;\n    cursor: pointer;\n    .special-item {\n      .el-badge__content {\n        width: 20px;\n        padding: 0 5px;\n      }\n    }\n    .item {\n      .el-badge__content {\n        background-color: #fd3333 !important;\n        line-height: 18px;\n        height: auto;\n        min-width: 18px;\n        min-height: 18px;\n        // border-radius: 50%;\n      }\n      .el-badge__content.is-fixed {\n        top: 14px;\n        right: 2px;\n      }\n    }\n  }\n  .active {\n    background-color: #ffc200;\n    font-weight: bold;\n  }\n  .tab-item:first-child {\n    border-left: 1px solid #e5e4e4;\n  }\n}\n</style>\n"]}]}