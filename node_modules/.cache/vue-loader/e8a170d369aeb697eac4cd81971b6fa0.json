{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/setMealStatistics.vue?vue&type=template&id=711376c4", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/setMealStatistics.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"container\">\n  <h2 class=\"homeTitle\">\n    套餐总览<span><router-link to=\"setmeal\">套餐管理</router-link></span>\n  </h2>\n  <div class=\"orderviewBox\">\n    <ul>\n      <li>\n        <span class=\"status\"><i class=\"iconfont icon-open\"></i>已启售</span>\n        <span class=\"num\">{{ setMealData.sold }}</span>\n      </li>\n      <li>\n        <span class=\"status\"><i class=\"iconfont icon-stop\"></i>已停售</span>\n        <span class=\"num\">{{ setMealData.discontinued }}</span>\n      </li>\n      <li class=\"add\">\n        <router-link to=\"setmeal/add\">\n          <i></i>\n          <p>新增套餐</p>\n        </router-link>\n      </li>\n    </ul>\n  </div>\n</div>\n", null]}