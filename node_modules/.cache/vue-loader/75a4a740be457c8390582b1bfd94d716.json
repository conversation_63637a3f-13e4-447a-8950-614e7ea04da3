{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Navbar/index.vue?vue&type=style&index=0&id=4ace4340&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Navbar/index.vue", "mtime": 1689143899000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n.navbar {\n  height: 60px;\n  // overflow: hidden;\n  position: relative;\n  background: #ffc100;\n\n  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\n  .statusBox {\n    float: left;\n    height: 100%;\n    align-items: center;\n    display: flex;\n  }\n  .hamburger-container {\n    // line-height: 54px;\n\n    padding: 0 12px 0 20px;\n    cursor: pointer;\n    transition: background 0.3s;\n    -webkit-tap-highlight-color: transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, 0.025);\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n  .right-menu {\n    float: right;\n\n    margin-right: 20px;\n\n    color: #333333;\n    font-size: 14px;\n\n    span {\n      padding: 0 10px;\n      width: 130px;\n      display: inline-block;\n      cursor: pointer;\n      &:hover {\n        background: rgba(255, 255, 255, 0.52);\n      }\n    }\n    .amendPwdIcon {\n      i {\n        width: 18px;\n        height: 18px;\n        background: url(./../../../assets/icons/<EMAIL>) no-repeat;\n        background-size: contain;\n        margin-top: 8px;\n      }\n    }\n    .outLogin {\n      i {\n        width: 18px;\n        height: 18px;\n        background: url(./../../../assets/icons/<EMAIL>) no-repeat 100%\n          100%;\n        background-size: contain;\n        margin-top: 8px;\n      }\n    }\n    .outLogin {\n      cursor: pointer;\n    }\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background 0.3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, 0.025);\n        }\n      }\n    }\n\n    // .avatar-container {\n    // margin-right: 30px;\n\n    // }\n  }\n  .rightStatus {\n    height: 100%;\n    line-height: 60px;\n    display: flex;\n    align-items: center;\n    float: left;\n  }\n  .avatar-wrapper {\n    margin-top: 14px;\n    margin-left: 18px;\n    position: relative;\n    // vertical-align: middle;\n    float: right;\n    width: 120px;\n    text-align: left;\n    .user-avatar {\n      cursor: pointer;\n      width: 40px;\n      height: 40px;\n      border-radius: 10px;\n    }\n\n    .el-icon-caret-bottom {\n      cursor: pointer;\n      position: absolute;\n      right: -20px;\n      top: 25px;\n      font-size: 12px;\n    }\n\n    .el-button--primary {\n      // height: 32px;\n      background: rgba(255, 255, 255, 0.52);\n      border-radius: 4px;\n      padding-top: 0px;\n      padding-bottom: 0px;\n      position: relative;\n      // top: -15px;\n      width: 120px;\n      // padding: 11px 12px 10px;\n      padding-left: 12px;\n      text-align: left;\n      border: 0 none;\n      height: 32px;\n      line-height: 32px;\n      &.active {\n        background: rgba(250, 250, 250, 0);\n        border: 0 none;\n        .el-icon-arrow-down {\n          transform: rotate(-180deg);\n        }\n      }\n    }\n  }\n  .businessBtn {\n    height: 22px;\n    line-height: 20px;\n    background: #fd3333;\n    border: 1px solid #ffffff;\n    border-radius: 4px;\n    display: inline-block;\n    padding: 0 6px;\n    color: #fff;\n  }\n  .closing {\n    background: #6a6a6a;\n  }\n  .navicon {\n    i {\n      display: inline-block;\n      width: 18px;\n      height: 18px;\n      vertical-align: sub;\n      margin: 0 4px 0 0;\n    }\n  }\n  .operatingState {\n    i {\n      background: url('./../../../assets/icons/time.png') no-repeat;\n      background-size: contain;\n    }\n  }\n  .mesCenter {\n    i {\n      background: url('./../../../assets/icons/msg.png') no-repeat;\n      background-size: contain;\n    }\n  }\n  // .el-badge__content.is-fixed {\n  //   top: 20px;\n  //   right: 6px;\n  // }\n}\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkTA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Navbar", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <div class=\"statusBox\">\n      <hamburger id=\"hamburger-container\"\n                 :is-active=\"sidebar.opened\"\n                 class=\"hamburger-container\"\n                 @toggleClick=\"toggleSideBar\" />\n      <span v-if=\"status===1\"\n            class=\"businessBtn\">营业中</span>\n      <span v-else\n            class=\"businessBtn closing\">打烊中</span>\n    </div>\n\n    <div :key=\"restKey\"\n         class=\"right-menu\">\n      <div class=\"rightStatus\">\n        <audio ref=\"audioVo\"\n               hidden>\n          <source src=\"./../../../assets/preview.mp3\" type=\"audio/mp3\" />\n        </audio>\n        <audio ref=\"audioVo2\"\n               hidden>\n          <source src=\"./../../../assets/reminder.mp3\" type=\"audio/mp3\" />\n        </audio>\n        <span class=\"navicon operatingState\" @click=\"handleStatus\"><i />营业状态设置</span>\n      </div>\n      <div class=\"avatar-wrapper\">\n        <div :class=\"shopShow?'userInfo':''\"\n             @mouseenter=\"toggleShow\"\n             @mouseleave=\"mouseLeaves\">\n          <el-button type=\"primary\"\n                     :class=\"shopShow?'active':''\">\n            {{ name }}<i class=\"el-icon-arrow-down\" />\n          </el-button>\n          <div v-if=\"shopShow\"\n               class=\"userList\">\n            <p class=\"amendPwdIcon\"\n               @click=\"handlePwd\">\n              修改密码<i />\n            </p>\n            <p class=\"outLogin\"\n               @click=\"logout\">\n              退出登录<i />\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 营业状态弹层 -->\n    <el-dialog title=\"营业状态设置\"\n               :visible.sync=\"dialogVisible\"\n               width=\"25%\"\n               :show-close=\"false\">\n      <el-radio-group v-model=\"setStatus\">\n        <el-radio :label=\"1\">\n          营业中\n          <span>当前餐厅处于营业状态，自动接收任何订单，可点击打烊进入店铺打烊状态。</span>\n        </el-radio>\n        <el-radio :label=\"0\">\n          打烊中\n          <span>当前餐厅处于打烊状态，仅接受营业时间内的预定订单，可点击营业中手动恢复营业状态。</span>\n        </el-radio>\n      </el-radio-group>\n      <span slot=\"footer\"\n            class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\"\n                   @click=\"handleSave\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <!-- end -->\n    <!-- 修改密码 -->\n    <Password :dialog-form-visible=\"dialogFormVisible\"\n              @handleclose=\"handlePwdClose\" />\n    <!-- end -->\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Watch } from 'vue-property-decorator'\nimport { AppModule } from '@/store/modules/app'\nimport { UserModule } from '@/store/modules/user'\nimport Breadcrumb from '@/components/Breadcrumb/index.vue'\nimport Hamburger from '@/components/Hamburger/index.vue'\nimport { getStatus, setStatus } from '@/api/users'\nimport Cookies from 'js-cookie'\nimport { debounce, throttle } from '@/utils/common'\nimport { setNewData, getNewData } from '@/utils/cookies'\n\n// 接口\nimport { getCountUnread } from '@/api/inform'\n// 修改密码弹层\nimport Password from '../components/password.vue'\n\n@Component({\n  name: 'Navbar',\n  components: {\n    Breadcrumb,\n    Hamburger,\n    Password,\n  },\n})\nexport default class extends Vue {\n  private storeId = this.getStoreId\n  private restKey: number = 0\n  private websocket = null\n  private newOrder = ''\n  private message = ''\n  private audioIsPlaying = false\n  private audioPaused = false\n  private statusValue = true\n  private audioUrl: './../../../assets/preview.mp3'\n  private shopShow = false\n  private dialogVisible = false\n  private status = 1\n  private setStatus = 1\n  private dialogFormVisible = false\n  private ountUnread = 0\n  // get ountUnread() {\n  //   return Number(getNewData())\n  // }\n  get sidebar() {\n    return AppModule.sidebar\n  }\n\n  get device() {\n    return AppModule.device.toString()\n  }\n\n  getuserInfo() {\n    return UserModule.userInfo\n  }\n\n  get name() {\n    return (UserModule.userInfo as any).name\n      ? (UserModule.userInfo as any).name\n      : JSON.parse(Cookies.get('user_info') as any).name\n  }\n\n  get getStoreId() {\n    let storeId = ''\n    if (UserModule.storeId) {\n      storeId = UserModule.storeId\n    } else if ((UserModule.userInfo as any).stores != null) {\n      storeId = (UserModule.userInfo as any).stores[0].storeId\n    }\n    return storeId\n  }\n  mounted() {\n    document.addEventListener('click', this.handleClose)\n    //console.log(this.$store.state.app.statusNumber)\n    // const msg = {\n    //   data: {\n    //     type: 2,\n    //     content: '订单1653904906519客户催单，已下单23分钟，仍未接单。',\n    //     details: '434'\n    //   }\n    // }\n    this.getStatus()\n  }\n  created() {\n    this.webSocket()\n  }\n  onload() {\n  }\n  destroyed() {\n    this.websocket.close() //离开路由之后断开websocket连接\n  }\n\n  // 添加新订单提示弹窗\n  webSocket() {\n    const that = this as any\n    let clientId = Math.random().toString(36).substr(2)\n    let socketUrl = process.env.VUE_APP_SOCKET_URL + clientId\n    console.log(socketUrl, 'socketUrl')\n    if (typeof WebSocket == 'undefined') {\n      that.$notify({\n        title: '提示',\n        message: '当前浏览器无法接收实时报警信息，请使用谷歌浏览器！',\n        type: 'warning',\n        duration: 0,\n      })\n    } else {\n      this.websocket = new WebSocket(socketUrl)\n      // 监听socket打开\n      this.websocket.onopen = function () {\n        console.log('浏览器WebSocket已打开')\n      }\n      // 监听socket消息接收\n      this.websocket.onmessage = function (msg) {\n        // 转换为json对象\n        that.$refs.audioVo.currentTime = 0\n        that.$refs.audioVo2.currentTime = 0\n\n        console.log(msg, JSON.parse(msg.data), 'msg')\n        // const h = this.$createElement\n        const jsonMsg = JSON.parse(msg.data)\n        if (jsonMsg.type === 1) {\n          that.$refs.audioVo.play()\n        } else if (jsonMsg.type === 2) {\n          that.$refs.audioVo2.play()\n        }\n        that.$notify({\n          title: jsonMsg.type === 1 ? '待接单' : '催单',\n          duration: 0,\n          dangerouslyUseHTMLString: true,\n          onClick: () => {\n            that.$router\n              .push(`/order?orderId=${jsonMsg.orderId}`)\n              .catch((err) => {\n                console.log(err)\n              })\n            setTimeout(() => {\n              location.reload()\n            }, 100)\n          },\n          // 这里也可以把返回信息加入到message中显示\n          message: `${\n            jsonMsg.type === 1\n              ? `<span>您有1个<span style=color:#419EFF>订单待处理</span>,${jsonMsg.content},请及时接单</span>`\n              : `${jsonMsg.content}<span style='color:#419EFF;cursor: pointer'>去处理</span>`\n          }`,\n        })\n      }\n      // 监听socket错误\n      this.websocket.onerror = function () {\n        that.$notify({\n          title: '错误',\n          message: '服务器错误，无法接收实时报警信息',\n          type: 'error',\n          duration: 0,\n        })\n      }\n      // 监听socket关闭\n      this.websocket.onclose = function () {\n        console.log('WebSocket已关闭')\n      }\n    }\n  }\n\n  private toggleSideBar() {\n    AppModule.ToggleSideBar(false)\n  }\n  // 退出\n  private async logout() {\n    this.$store.dispatch('LogOut').then(() => {\n      // location.href = '/'\n      this.$router.replace({ path: '/login' })\n    })\n    // this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n  }\n  // 获取未读消息\n  async getCountUnread() {\n    const { data } = await getCountUnread()\n    if (data.code === 1) {\n      // this.ountUnread = data.data\n      AppModule.StatusNumber(data.data)\n      // setNewData(data.data)\n      // this.$message.success('操作成功！')\n    } else {\n      this.$message.error(data.msg)\n    }\n  }\n  // 营业状态\n  async getStatus() {\n    const { data } = await getStatus()\n    this.status = data.data\n    this.setStatus = this.status\n  }\n  // 下拉菜单显示\n  toggleShow() {\n    this.shopShow = true\n  }\n  // 下拉菜单隐藏\n  mouseLeaves() {\n    this.shopShow = false\n  }\n  // 触发空白处下来菜单关闭\n  handleClose() {\n    // clearTimeout(this.leave)\n    // this.shopShow = false\n  }\n  // 设置营业状态\n  handleStatus() {\n    this.dialogVisible = true\n  }\n  // 营业状态设置\n  async handleSave() {\n    const { data } = await setStatus(this.setStatus)\n    if (data.code === 1) {\n      this.dialogVisible = false\n      this.getStatus()\n    }\n  }\n  // 修改密码\n  handlePwd() {\n    this.dialogFormVisible = true\n  }\n  // 关闭密码编辑弹层\n  handlePwdClose() {\n    this.dialogFormVisible = false\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 60px;\n  // overflow: hidden;\n  position: relative;\n  background: #ffc100;\n\n  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\n  .statusBox {\n    float: left;\n    height: 100%;\n    align-items: center;\n    display: flex;\n  }\n  .hamburger-container {\n    // line-height: 54px;\n\n    padding: 0 12px 0 20px;\n    cursor: pointer;\n    transition: background 0.3s;\n    -webkit-tap-highlight-color: transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, 0.025);\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n  .right-menu {\n    float: right;\n\n    margin-right: 20px;\n\n    color: #333333;\n    font-size: 14px;\n\n    span {\n      padding: 0 10px;\n      width: 130px;\n      display: inline-block;\n      cursor: pointer;\n      &:hover {\n        background: rgba(255, 255, 255, 0.52);\n      }\n    }\n    .amendPwdIcon {\n      i {\n        width: 18px;\n        height: 18px;\n        background: url(./../../../assets/icons/<EMAIL>) no-repeat;\n        background-size: contain;\n        margin-top: 8px;\n      }\n    }\n    .outLogin {\n      i {\n        width: 18px;\n        height: 18px;\n        background: url(./../../../assets/icons/<EMAIL>) no-repeat 100%\n          100%;\n        background-size: contain;\n        margin-top: 8px;\n      }\n    }\n    .outLogin {\n      cursor: pointer;\n    }\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background 0.3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, 0.025);\n        }\n      }\n    }\n\n    // .avatar-container {\n    // margin-right: 30px;\n\n    // }\n  }\n  .rightStatus {\n    height: 100%;\n    line-height: 60px;\n    display: flex;\n    align-items: center;\n    float: left;\n  }\n  .avatar-wrapper {\n    margin-top: 14px;\n    margin-left: 18px;\n    position: relative;\n    // vertical-align: middle;\n    float: right;\n    width: 120px;\n    text-align: left;\n    .user-avatar {\n      cursor: pointer;\n      width: 40px;\n      height: 40px;\n      border-radius: 10px;\n    }\n\n    .el-icon-caret-bottom {\n      cursor: pointer;\n      position: absolute;\n      right: -20px;\n      top: 25px;\n      font-size: 12px;\n    }\n\n    .el-button--primary {\n      // height: 32px;\n      background: rgba(255, 255, 255, 0.52);\n      border-radius: 4px;\n      padding-top: 0px;\n      padding-bottom: 0px;\n      position: relative;\n      // top: -15px;\n      width: 120px;\n      // padding: 11px 12px 10px;\n      padding-left: 12px;\n      text-align: left;\n      border: 0 none;\n      height: 32px;\n      line-height: 32px;\n      &.active {\n        background: rgba(250, 250, 250, 0);\n        border: 0 none;\n        .el-icon-arrow-down {\n          transform: rotate(-180deg);\n        }\n      }\n    }\n  }\n  .businessBtn {\n    height: 22px;\n    line-height: 20px;\n    background: #fd3333;\n    border: 1px solid #ffffff;\n    border-radius: 4px;\n    display: inline-block;\n    padding: 0 6px;\n    color: #fff;\n  }\n  .closing {\n    background: #6a6a6a;\n  }\n  .navicon {\n    i {\n      display: inline-block;\n      width: 18px;\n      height: 18px;\n      vertical-align: sub;\n      margin: 0 4px 0 0;\n    }\n  }\n  .operatingState {\n    i {\n      background: url('./../../../assets/icons/time.png') no-repeat;\n      background-size: contain;\n    }\n  }\n  .mesCenter {\n    i {\n      background: url('./../../../assets/icons/msg.png') no-repeat;\n      background-size: contain;\n    }\n  }\n  // .el-badge__content.is-fixed {\n  //   top: 20px;\n  //   right: 6px;\n  // }\n}\n</style>\n<style lang=\"scss\">\n.el-notification {\n  // background: rgba(255, 255, 255, 0.71);\n  width: 419px !important;\n  .el-notification__title {\n    margin-bottom: 14px;\n    color: #333;\n    .el-notification__content {\n      color: #333;\n    }\n  }\n}\n.navbar {\n  .el-dialog {\n    min-width: auto !important;\n  }\n  .el-dialog__header {\n    height: 61px;\n    line-height: 60px;\n    background: #fbfbfa;\n    padding: 0 30px;\n    font-size: 16px;\n    color: #333;\n    border: 0 none;\n  }\n  .el-dialog__body {\n    padding: 10px 30px 30px;\n    .el-radio,\n    .el-radio__input {\n      white-space: normal;\n    }\n    .el-radio__label {\n      padding-left: 5px;\n      color: #333;\n      font-weight: 700;\n      span {\n        display: block;\n        line-height: 20px;\n        padding-top: 12px;\n        color: #666;\n        font-weight: normal;\n      }\n    }\n    .el-radio__input.is-checked .el-radio__inner {\n      &::after {\n        background: #333;\n      }\n    }\n    .el-radio-group {\n      & > .is-checked {\n        border: 1px solid #ffc200;\n      }\n    }\n    .el-radio {\n      width: 100%;\n      background: #fbfbfa;\n      border: 1px solid #e5e4e4;\n      border-radius: 4px;\n      padding: 14px 22px;\n      margin-top: 20px;\n    }\n    .el-radio__input.is-checked + .el-radio__label {\n      span {\n      }\n    }\n  }\n  .el-badge__content.is-fixed {\n    top: 24px;\n    right: 2px;\n    width: 18px;\n    height: 18px;\n    font-size: 10px;\n    line-height: 16px;\n    font-size: 10px;\n    border-radius: 50%;\n    padding: 0;\n  }\n  .badgeW {\n    .el-badge__content.is-fixed {\n      width: 30px;\n      border-radius: 20px;\n    }\n  }\n}\n.el-icon-arrow-down {\n  background: url('./../../../assets/icons/up.png') no-repeat 50% 50%;\n  background-size: contain;\n  width: 8px;\n  height: 8px;\n  transform: rotate(0eg);\n  margin-left: 16px;\n  position: absolute;\n  right: 16px;\n  top: 12px;\n  &:before {\n    content: '';\n  }\n}\n\n.userInfo {\n  background: #fff;\n  position: absolute;\n  top: 0px;\n  left: 0;\n  z-index: 99;\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.14);\n  width: 100%;\n  border-radius: 4px;\n  line-height: 32px;\n  padding: 0 0 5px;\n  height: 105px;\n  // .active {\n  //   top: 0;\n  //   left: 0;\n  // }\n  .userList {\n    width: 95%;\n    // // margin-top: -5px;\n    // position: absolute;\n    // top: 35px;\n    padding-left: 5px;\n  }\n  p {\n    cursor: pointer;\n    height: 32px;\n    line-height: 32px;\n    padding: 0 5px 0 7px;\n    i {\n      margin-left: 10px;\n\n      vertical-align: middle;\n      margin-top: 4px;\n      float: right;\n    }\n    &:hover {\n      background: #f6f1e1;\n    }\n  }\n}\n.msgTip {\n  color: #419eff;\n  padding: 0 5px;\n}\n// .el-dropdown{\n//   .el-button--primary{\n//     height: 32px;\n//     background: rgba(255,255,255,0.52);\n//     border-radius: 4px;\n//     padding-top: 0px;\n//     padding-bottom: 0px;\n//   }\n//   margin-top: 2px;\n// }\n// .el-popper{\n//   top: 45px !important;\n//   padding-top: 50px !important;\n//   border-radius: 0 0 4px 4px;\n// }\n// .el-popper[x-placement^=bottom] .popper__arrow::after,.popper__arrow{\n//   display: none !important;\n// }\n</style>\n"]}]}