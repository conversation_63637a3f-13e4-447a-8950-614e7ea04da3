{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue?vue&type=template&id=37dfd6fc", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/login/index.vue", "mtime": 1691720868000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"login\">\n  <div class=\"login-box\">\n    <img src=\"@/assets/login/login-l.png\" alt=\"\" />\n    <div class=\"login-form\">\n      <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\">\n        <div class=\"login-form-title\">\n          <img\n            src=\"@/assets/login/icon_logo.png\"\n            style=\"width: 149px; height: 38px\"\n            alt=\"\"\n          />\n          <!-- <span class=\"title-label\">苍穹外卖</span> -->\n        </div>\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            type=\"text\"\n            auto-complete=\"off\"\n            placeholder=\"账号\"\n            prefix-icon=\"iconfont icon-user\"\n          />\n        </el-form-item>\n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"密码\"\n            prefix-icon=\"iconfont icon-lock\"\n            @keyup.enter.native=\"handleLogin\"\n          />\n        </el-form-item>\n        <el-form-item style=\"width: 100%\">\n          <el-button\n            :loading=\"loading\"\n            class=\"login-btn\"\n            size=\"medium\"\n            type=\"primary\"\n            style=\"width: 100%\"\n            @click.native.prevent=\"handleLogin\"\n          >\n            <span v-if=\"!loading\">登录</span>\n            <span v-else>登录中...</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n  </div>\n</div>\n", null]}