{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/index.vue", "mtime": 1655803193000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue } from 'vue-property-decorator'\nimport {\n  getBusinessData,\n  getDataOverView, //营业数据\n  getOrderData, //订单管理今日订单\n  getOverviewDishes, //菜品总览\n  getSetMealStatistics, //套餐总览\n} from '@/api/index'\nimport { getOrderListBy } from '@/api/order'\n// 组件\n// 营业数据\nimport Overview from './components/overview.vue'\n// 订单管理\nimport Orderview from './components/orderview.vue'\n// 菜品总览\nimport CuisineStatistics from './components/cuisineStatistics.vue'\n// 套餐总览\nimport SetMealStatistics from './components/setMealStatistics.vue'\n// 订单列表\nimport OrderList from './components/orderList.vue'\n@Component({\n  name: 'Dashboard',\n  components: {\n    Overview,\n    Orderview,\n    CuisineStatistics,\n    SetMealStatistics,\n    OrderList,\n  },\n})\nexport default class extends Vue {\n  private todayData = {} as any\n  private overviewData = {}\n  private orderviewData = {} as any\n  private flag = 2\n  private tateData = []\n  private dishesData = {} as any\n  private setMealData = {}\n  private orderListData = []\n  private counts = 0\n  private page: number = 1\n  private pageSize: number = 10\n  private status = 2\n  private orderStatics = {} as any\n  created() {\n    this.init()\n  }\n  init() {\n    this.$nextTick(() => {\n      this.getBusinessData()\n      this.getOrderStatisticsData()\n      this.getOverStatisticsData()\n      this.getSetMealStatisticsData()\n    })\n  }\n  // 获取营业数据\n  async getBusinessData() {\n    const data = await getBusinessData()\n    this.overviewData = data.data.data\n  }\n  // 获取今日订单\n  async getOrderStatisticsData() {\n    const data = await getOrderData()\n    this.orderviewData = data.data.data\n  }\n  // 获取菜品总览数据\n  async getOverStatisticsData() {\n    const data = await getOverviewDishes()\n    this.dishesData = data.data.data\n  }\n  // 获取套餐总览数据\n  async getSetMealStatisticsData() {\n    const data = await getSetMealStatistics()\n    this.setMealData = data.data.data\n  }\n  //获取待处理，待派送，派送中数量\n  getOrderListBy3Status() {\n    getOrderListBy({})\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.orderStatics = res.data.data\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n}\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div class=\"dashboard-container home\">\n    <!-- 营业数据 -->\n    <Overview :overviewData=\"overviewData\" />\n    <!-- end -->\n    <!-- 订单管理 -->\n    <Orderview :orderviewData=\"orderviewData\" />\n    <!-- end -->\n    <div class=\"homeMain\">\n      <!-- 菜品总览 -->\n      <CuisineStatistics :dishesData=\"dishesData\" />\n      <!-- end -->\n      <!-- 套餐总览 -->\n      <SetMealStatistics :setMealData=\"setMealData\" />\n      <!-- end -->\n    </div>\n    <!-- 订单信息 -->\n    <OrderList\n      :order-statics=\"orderStatics\"\n      @getOrderListBy3Status=\"getOrderListBy3Status\"\n    />\n    <!-- end -->\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue } from 'vue-property-decorator'\nimport {\n  getBusinessData,\n  getDataOverView, //营业数据\n  getOrderData, //订单管理今日订单\n  getOverviewDishes, //菜品总览\n  getSetMealStatistics, //套餐总览\n} from '@/api/index'\nimport { getOrderListBy } from '@/api/order'\n// 组件\n// 营业数据\nimport Overview from './components/overview.vue'\n// 订单管理\nimport Orderview from './components/orderview.vue'\n// 菜品总览\nimport CuisineStatistics from './components/cuisineStatistics.vue'\n// 套餐总览\nimport SetMealStatistics from './components/setMealStatistics.vue'\n// 订单列表\nimport OrderList from './components/orderList.vue'\n@Component({\n  name: 'Dashboard',\n  components: {\n    Overview,\n    Orderview,\n    CuisineStatistics,\n    SetMealStatistics,\n    OrderList,\n  },\n})\nexport default class extends Vue {\n  private todayData = {} as any\n  private overviewData = {}\n  private orderviewData = {} as any\n  private flag = 2\n  private tateData = []\n  private dishesData = {} as any\n  private setMealData = {}\n  private orderListData = []\n  private counts = 0\n  private page: number = 1\n  private pageSize: number = 10\n  private status = 2\n  private orderStatics = {} as any\n  created() {\n    this.init()\n  }\n  init() {\n    this.$nextTick(() => {\n      this.getBusinessData()\n      this.getOrderStatisticsData()\n      this.getOverStatisticsData()\n      this.getSetMealStatisticsData()\n    })\n  }\n  // 获取营业数据\n  async getBusinessData() {\n    const data = await getBusinessData()\n    this.overviewData = data.data.data\n  }\n  // 获取今日订单\n  async getOrderStatisticsData() {\n    const data = await getOrderData()\n    this.orderviewData = data.data.data\n  }\n  // 获取菜品总览数据\n  async getOverStatisticsData() {\n    const data = await getOverviewDishes()\n    this.dishesData = data.data.data\n  }\n  // 获取套餐总览数据\n  async getSetMealStatisticsData() {\n    const data = await getSetMealStatistics()\n    this.setMealData = data.data.data\n  }\n  //获取待处理，待派送，派送中数量\n  getOrderListBy3Status() {\n    getOrderListBy({})\n      .then((res) => {\n        if (res.data.code === 1) {\n          this.orderStatics = res.data.data\n        } else {\n          this.$message.error(res.data.msg)\n        }\n      })\n      .catch((err) => {\n        this.$message.error('请求出错了：' + err.message)\n      })\n  }\n}\n</script>\n\n<style lang=\"scss\">\n</style>\n"]}]}