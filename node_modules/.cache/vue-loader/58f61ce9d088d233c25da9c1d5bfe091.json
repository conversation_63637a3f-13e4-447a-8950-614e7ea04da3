{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue?vue&type=template&id=33ec43fc&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div>\n  <div class=\"logo\">\n    <!-- <img\n      src=\"./../../../assets/logo.png\"\n      width=\"122.5\"\n      alt=\"\"\n    > -->\n    <!-- <img\n      src=\"@/assets/login/login-logo.png\"\n      alt=\"\"\n      style=\"width: 120px; height: 31px\"\n    /> -->\n    <div v-if=\"!isCollapse\"\n         class=\"sidebar-logo\">\n      <img src=\"@/assets/login/logo.png\"\n           style=\"width: 120px; height: 31px\">\n    </div>\n    <div v-else\n         class=\"sidebar-logo-mini\">\n      <img src=\"@/assets/login/mini-logo.png\">\n    </div>\n  </div>\n  <el-scrollbar wrap-class=\"scrollbar-wrapper\">\n    <el-menu :default-openeds=\"defOpen\"\n             :default-active=\"defAct\"\n             :collapse=\"isCollapse\"\n             :background-color=\"variables.menuBg\"\n             :text-color=\"variables.menuText\"\n             :active-text-color=\"variables.menuActiveText\"\n             :unique-opened=\"false\"\n             :collapse-transition=\"false\"\n             mode=\"vertical\">\n      <sidebar-item v-for=\"route in routes\"\n                    :key=\"route.path\"\n                    :item=\"route\"\n                    :base-path=\"route.path\"\n                    :is-collapse=\"isCollapse\" />\n      <!-- <div class=\"sub-menu\">\n        <div class=\"avatarName\">\n          {{ name }}\n        </div>\n        <div class=\"img\">\n          <img\n            src=\"./../../../assets/icons/<EMAIL>\"\n            class=\"outLogin\"\n            alt=\"退出\"\n            @click=\"logout\"\n          />\n        </div>\n      </div> -->\n    </el-menu>\n  </el-scrollbar>\n</div>\n", null]}