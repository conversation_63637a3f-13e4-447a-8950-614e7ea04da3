{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/setMealStatistics.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/setMealStatistics.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Vue, Prop } from 'vue-property-decorator'\r\n@Component({\r\n  name: 'SetMeal',\r\n})\r\nexport default class extends Vue {\r\n  @Prop() private setMealData!: any\r\n}\r\n", {"version": 3, "sources": ["setMealStatistics.vue"], "names": [], "mappings": ";AA2BA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "setMealStatistics.vue", "sourceRoot": "src/views/dashboard/components", "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <h2 class=\"homeTitle\">\r\n      套餐总览<span><router-link to=\"setmeal\">套餐管理</router-link></span>\r\n    </h2>\r\n    <div class=\"orderviewBox\">\r\n      <ul>\r\n        <li>\r\n          <span class=\"status\"><i class=\"iconfont icon-open\"></i>已启售</span>\r\n          <span class=\"num\">{{ setMealData.sold }}</span>\r\n        </li>\r\n        <li>\r\n          <span class=\"status\"><i class=\"iconfont icon-stop\"></i>已停售</span>\r\n          <span class=\"num\">{{ setMealData.discontinued }}</span>\r\n        </li>\r\n        <li class=\"add\">\r\n          <router-link to=\"setmeal/add\">\r\n            <i></i>\r\n            <p>新增套餐</p>\r\n          </router-link>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue, Prop } from 'vue-property-decorator'\r\n@Component({\r\n  name: 'SetMeal',\r\n})\r\nexport default class extends Vue {\r\n  @Prop() private setMealData!: any\r\n}\r\n</script>\r\n"]}]}