{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/components/SelectInput.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/components/SelectInput.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\n\r\n@Component({\r\n  name: 'selectInput',\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: [] }) private selectFlavorsData!: []\r\n  @Prop({ default: [] }) private dishFlavorsData!: []\r\n  @Prop({ default: '' }) private value!: number\r\n  @Prop({ default: 0 }) private index!: number\r\n  private keyValue = NaN\r\n\r\n  private mak: boolean = false\r\n\r\n  private selectFlavor(st: boolean) {\r\n    this.mak = st\r\n  }\r\n\r\n  private outSelect(st: boolean) {\r\n    const _this = this\r\n    setTimeout(function () {\r\n      _this.mak = st\r\n    }, 200)\r\n  }\r\n\r\n  private inputHandle(val: any) {\r\n    this.selectFlavor(false)\r\n  }\r\n\r\n  checkOption(val: any, ind: any) {\r\n    this.$emit('select', val.name, this.index, ind)\r\n    this.keyValue = val.name\r\n  }\r\n}\r\n", {"version": 3, "sources": ["SelectInput.vue"], "names": [], "mappings": ";AA0BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "SelectInput.vue", "sourceRoot": "src/views/dish/components", "sourcesContent": ["<template>\r\n  <div class=\"selectInput\">\r\n    <el-input\r\n      v-model=\"value\"\r\n      type=\"text\"\r\n      style=\"width: 100%\"\r\n      placeholder=\"请选择口味\"\r\n      clearable\r\n      readonly\r\n      @focus=\"selectFlavor(true)\"\r\n      @blur=\"outSelect(false)\"\r\n    />\r\n    <div v-if=\"mak && dishFlavorsData.length\" class=\"flavorSelect\">\r\n      <span\r\n        v-for=\"(it, ind) in dishFlavorsData\"\r\n        :key=\"ind\"\r\n        class=\"items\"\r\n        @click=\"checkOption(it, ind)\"\r\n        >{{ it.name }}</span\r\n      >\r\n      <span v-if=\"dishFlavorsData == []\" class=\"none\">无数据</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\n\r\n@Component({\r\n  name: 'selectInput',\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: [] }) private selectFlavorsData!: []\r\n  @Prop({ default: [] }) private dishFlavorsData!: []\r\n  @Prop({ default: '' }) private value!: number\r\n  @Prop({ default: 0 }) private index!: number\r\n  private keyValue = NaN\r\n\r\n  private mak: boolean = false\r\n\r\n  private selectFlavor(st: boolean) {\r\n    this.mak = st\r\n  }\r\n\r\n  private outSelect(st: boolean) {\r\n    const _this = this\r\n    setTimeout(function () {\r\n      _this.mak = st\r\n    }, 200)\r\n  }\r\n\r\n  private inputHandle(val: any) {\r\n    this.selectFlavor(false)\r\n  }\r\n\r\n  checkOption(val: any, ind: any) {\r\n    this.$emit('select', val.name, this.index, ind)\r\n    this.keyValue = val.name\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.selectInput {\r\n  position: relative;\r\n  width: 100%;\r\n  min-width: 100px;\r\n  .flavorSelect {\r\n    position: absolute;\r\n    width: 100%;\r\n    // padding: 0 10px;\r\n    border-radius: 3px;\r\n    border: solid 1px #e4e7ed;\r\n    line-height: 30px;\r\n    text-align: center;\r\n    background: #fff;\r\n    top: 50px;\r\n    z-index: 99;\r\n    .items {\r\n      cursor: pointer;\r\n      display: inline-block;\r\n      width: 100%;\r\n      line-height: 35px;\r\n      border-bottom: solid 1px #f4f4f4;\r\n      color: #666;\r\n      margin: 0 !important;\r\n      &:hover {\r\n        background-color: #fffbf0;\r\n      }\r\n      &:active {\r\n        background-color: #fffbf0;\r\n        color: #ffc200;\r\n      }\r\n    }\r\n    .none {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}