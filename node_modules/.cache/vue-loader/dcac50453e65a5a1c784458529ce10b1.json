{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dashboard/components/overview.vue", "mtime": 1655864776000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue, Prop } from 'vue-property-decorator'\nimport { getday } from '@/utils/formValidate'\n@Component({\n  name: 'Overview',\n})\nexport default class extends Vue {\n  @Prop() private overviewData!: any\n  get days() {\n    return getday()\n  }\n}\n", {"version": 3, "sources": ["overview.vue"], "names": [], "mappings": ";AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "overview.vue", "sourceRoot": "src/views/dashboard/components", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"homeTitle\">\n      今日数据<i>{{ days[1] }}</i\n      ><span><router-link to=\"statistics\">详细数据</router-link></span>\n    </h2>\n    <div class=\"overviewBox\">\n      <ul>\n        <li>\n          <p class=\"tit\">营业额</p>\n          <p class=\"num\">¥ {{ overviewData.turnover }}</p>\n        </li>\n        <li>\n          <p class=\"tit\">有效订单</p>\n          <p class=\"num\">{{ overviewData.validOrderCount }}</p>\n        </li>\n        <li>\n          <p class=\"tit\">订单完成率</p>\n          <p class=\"num\">\n            {{ (overviewData.orderCompletionRate * 100).toFixed(0) }}%\n          </p>\n        </li>\n        <li>\n          <p class=\"tit\">平均客单价</p>\n          <p class=\"num\">¥ {{ overviewData.unitPrice }}</p>\n        </li>\n\n        <li>\n          <p class=\"tit\">新增用户</p>\n          <p class=\"num\">{{ overviewData.newUsers }}</p>\n        </li>\n      </ul>\n    </div>\n  </div>\n</template>\n<script lang=\"ts\">\nimport { Component, Vue, Prop } from 'vue-property-decorator'\nimport { getday } from '@/utils/formValidate'\n@Component({\n  name: 'Overview',\n})\nexport default class extends Vue {\n  @Prop() private overviewData!: any\n  get days() {\n    return getday()\n  }\n}\n</script>\n"]}]}