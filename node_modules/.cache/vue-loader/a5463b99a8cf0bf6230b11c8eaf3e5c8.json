{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/components/AddDish.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/components/AddDish.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Prop, Vue, Watch } from 'vue-property-decorator'\r\n// import {getDishTypeList, getDishListType} from '@/api/dish';\r\nimport { getCategoryList, queryDishList } from '@/api/dish'\r\nimport Empty from '@/components/Empty/index.vue'\r\n\r\n@Component({\r\n  name: 'selectInput',\r\n  components: {\r\n    Empty\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: '' }) private value!: number\r\n  @Prop({ default: [] }) private checkList!: any[]\r\n  @Prop({ default: '' }) private seachKey!: string\r\n  private dishType: [] = []\r\n  private dishList: [] = []\r\n  private allDishList: any[] = []\r\n  private dishListCache: any[] = []\r\n  private keyInd = 0\r\n  private searchValue: string = ''\r\n  public checkedList: any[] = []\r\n  private checkedListAll: any[] = []\r\n  private ids: any = new Set()\r\n  created() {\r\n    this.init()\r\n  }\r\n\r\n  @Watch('seachKey')\r\n  private seachKeyChange(value: any) {\r\n    if (value.trim()) {\r\n      this.getDishForName(this.seachKey)\r\n    }\r\n  }\r\n\r\n  public init() {\r\n    // 菜单列表数据获取\r\n    this.getDishType()\r\n    // 初始化选项\r\n    this.checkedList = this.checkList.map((it: any) => it.name)\r\n    // 已选项的菜品-详细信息\r\n    this.checkedListAll = this.checkList.reverse()\r\n  }\r\n  // 获取套餐分类\r\n  public getDishType() {\r\n    getCategoryList({ type: 1 }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.dishType = res.data.data\r\n        this.getDishList(res.data.data[0].id)\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n      // if (res.data.code == 200) {\r\n      //   const { data } = res.data\r\n      //   this.   = data\r\n      //   this.getDishList(data[0].category_id)\r\n      // } else {\r\n      //   this.$message.error(res.data.desc)\r\n      // }\r\n    })\r\n  }\r\n\r\n  // 通过套餐ID获取菜品列表分类\r\n  private getDishList(id: number) {\r\n    queryDishList({ categoryId: id }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        if (res.data.data.length == 0) {\r\n          this.dishList = []\r\n          return\r\n        }\r\n        let newArr = res.data.data\r\n        newArr.forEach((n: any) => {\r\n          n.dishId = n.id\r\n          n.copies = 1\r\n          // n.dishCopies = 1\r\n          n.dishName = n.name\r\n        })\r\n        this.dishList = newArr\r\n        if (!this.ids.has(id)) {\r\n          this.allDishList = [...this.allDishList, ...newArr]\r\n        }\r\n        this.ids.add(id)\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n\r\n  // 关键词收搜菜品列表分类\r\n  private getDishForName(name: any) {\r\n    queryDishList({ name }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        let newArr = res.data.data\r\n        newArr.forEach((n: any) => {\r\n          n.dishId = n.id\r\n          n.dishName = n.name\r\n        })\r\n        this.dishList = newArr\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n  // 点击分类\r\n  private checkTypeHandle(ind: number, id: any) {\r\n    this.keyInd = ind\r\n    this.getDishList(id)\r\n  }\r\n  // 添加菜品\r\n  private checkedListHandle(value: [string]) {\r\n    // TODO 实现倒序 由于value是组件内封装无法从前面添加 所有取巧处理倒序添加\r\n    // 倒序展示 - 数据处理前反正 为正序\r\n    this.checkedListAll.reverse()\r\n    // value 是一个只包含菜品名的数组 需要从 dishList中筛选出 对应的详情\r\n    // 操作添加菜品\r\n    const list = this.allDishList.filter((item: any) => {\r\n      let data\r\n      value.forEach((it: any) => {\r\n        if (item.name == it) {\r\n          data = item\r\n        }\r\n      })\r\n      return data\r\n    })\r\n    // 编辑的时候需要与已有菜品合并\r\n    // 与当前请求下的选择性 然后去重就是当前的列表\r\n    const dishListCat = [...this.checkedListAll, ...list]\r\n    let arrData: any[] = []\r\n    this.checkedListAll = dishListCat.filter((item: any) => {\r\n      let allArrDate\r\n      if (arrData.length == 0) {\r\n        arrData.push(item.name)\r\n        allArrDate = item\r\n      } else {\r\n        const st = arrData.some(it => item.name == it)\r\n        if (!st) {\r\n          arrData.push(item.name)\r\n          allArrDate = item\r\n        }\r\n      }\r\n      return allArrDate\r\n    })\r\n    // 如果是减菜 走这里\r\n    if (value.length < arrData.length) {\r\n      this.checkedListAll = this.checkedListAll.filter((item: any) => {\r\n        if (value.some(it => it == item.name)) {\r\n          return item\r\n        }\r\n      })\r\n    }\r\n    this.$emit('checkList', this.checkedListAll)\r\n    // 数据处理完反转为倒序\r\n    this.checkedListAll.reverse()\r\n  }\r\n\r\n  open(done: any) {\r\n    this.dishListCache = JSON.parse(JSON.stringify(this.checkList))\r\n  }\r\n\r\n  close(done: any) {\r\n    this.checkList = this.dishListCache\r\n  }\r\n\r\n  // 删除\r\n  private delCheck(name: any) {\r\n    const index = this.checkedList.findIndex(it => it === name)\r\n    const indexAll = this.checkedListAll.findIndex(\r\n      (it: any) => it.name === name\r\n    )\r\n\r\n    this.checkedList.splice(index, 1)\r\n    this.checkedListAll.splice(indexAll, 1)\r\n    this.$emit('checkList', this.checkedListAll)\r\n  }\r\n}\r\n", {"version": 3, "sources": ["AddDish.vue"], "names": [], "mappings": ";AA4DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AddDish.vue", "sourceRoot": "src/views/setmeal/components", "sourcesContent": ["<template>\r\n  <div class=\"addDish\">\r\n    <div class=\"leftCont\">\r\n      <div v-show=\"seachKey.trim() == ''\"\r\n           class=\"tabBut\">\r\n        <span v-for=\"(item, index) in dishType\"\r\n              :key=\"index\"\r\n              :class=\"{ act: index == keyInd }\"\r\n              @click=\"checkTypeHandle(index, item.id)\">{{ item.name }}</span>\r\n      </div>\r\n      <div class=\"tabList\">\r\n        <div class=\"table\"\r\n             :class=\"{ borderNone: !dishList.length }\">\r\n          <div v-if=\"dishList.length == 0\"\r\n               style=\"padding-left: 10px\">\r\n            <Empty />\r\n          </div>\r\n          <el-checkbox-group v-if=\"dishList.length > 0\"\r\n                             v-model=\"checkedList\"\r\n                             @change=\"checkedListHandle\">\r\n            <div v-for=\"(item, index) in dishList\"\r\n                 :key=\"item.name + item.id\"\r\n                 class=\"items\">\r\n              <el-checkbox :key=\"index\"\r\n                           :label=\"item.name\">\r\n                <div class=\"item\">\r\n                  <span style=\"flex: 3; text-align: left\">{{\r\n                    item.dishName\r\n                  }}</span>\r\n                  <span>{{ item.status == 0 ? '停售' : '在售' }}</span>\r\n                  <span>{{ (Number(item.price) ).toFixed(2)*100/100 }}</span>\r\n                </div>\r\n              </el-checkbox>\r\n            </div>\r\n          </el-checkbox-group>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"ritCont\">\r\n      <div class=\"tit\">\r\n        已选菜品({{ checkedListAll.length }})\r\n      </div>\r\n      <div class=\"items\">\r\n        <div v-for=\"(item, ind) in checkedListAll\"\r\n             :key=\"ind\"\r\n             class=\"item\">\r\n          <span>{{ item.dishName || item.name }}</span>\r\n          <span class=\"price\">￥ {{ (Number(item.price) ).toFixed(2)*100/100 }} </span>\r\n          <span class=\"del\"\r\n                @click=\"delCheck(item.name)\">\r\n            <img src=\"./../../../assets/icons/<EMAIL>\"\r\n                 alt=\"\">\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Prop, Vue, Watch } from 'vue-property-decorator'\r\n// import {getDishTypeList, getDishListType} from '@/api/dish';\r\nimport { getCategoryList, queryDishList } from '@/api/dish'\r\nimport Empty from '@/components/Empty/index.vue'\r\n\r\n@Component({\r\n  name: 'selectInput',\r\n  components: {\r\n    Empty\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: '' }) private value!: number\r\n  @Prop({ default: [] }) private checkList!: any[]\r\n  @Prop({ default: '' }) private seachKey!: string\r\n  private dishType: [] = []\r\n  private dishList: [] = []\r\n  private allDishList: any[] = []\r\n  private dishListCache: any[] = []\r\n  private keyInd = 0\r\n  private searchValue: string = ''\r\n  public checkedList: any[] = []\r\n  private checkedListAll: any[] = []\r\n  private ids: any = new Set()\r\n  created() {\r\n    this.init()\r\n  }\r\n\r\n  @Watch('seachKey')\r\n  private seachKeyChange(value: any) {\r\n    if (value.trim()) {\r\n      this.getDishForName(this.seachKey)\r\n    }\r\n  }\r\n\r\n  public init() {\r\n    // 菜单列表数据获取\r\n    this.getDishType()\r\n    // 初始化选项\r\n    this.checkedList = this.checkList.map((it: any) => it.name)\r\n    // 已选项的菜品-详细信息\r\n    this.checkedListAll = this.checkList.reverse()\r\n  }\r\n  // 获取套餐分类\r\n  public getDishType() {\r\n    getCategoryList({ type: 1 }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        this.dishType = res.data.data\r\n        this.getDishList(res.data.data[0].id)\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n      // if (res.data.code == 200) {\r\n      //   const { data } = res.data\r\n      //   this.   = data\r\n      //   this.getDishList(data[0].category_id)\r\n      // } else {\r\n      //   this.$message.error(res.data.desc)\r\n      // }\r\n    })\r\n  }\r\n\r\n  // 通过套餐ID获取菜品列表分类\r\n  private getDishList(id: number) {\r\n    queryDishList({ categoryId: id }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        if (res.data.data.length == 0) {\r\n          this.dishList = []\r\n          return\r\n        }\r\n        let newArr = res.data.data\r\n        newArr.forEach((n: any) => {\r\n          n.dishId = n.id\r\n          n.copies = 1\r\n          // n.dishCopies = 1\r\n          n.dishName = n.name\r\n        })\r\n        this.dishList = newArr\r\n        if (!this.ids.has(id)) {\r\n          this.allDishList = [...this.allDishList, ...newArr]\r\n        }\r\n        this.ids.add(id)\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n\r\n  // 关键词收搜菜品列表分类\r\n  private getDishForName(name: any) {\r\n    queryDishList({ name }).then(res => {\r\n      if (res && res.data && res.data.code === 1) {\r\n        let newArr = res.data.data\r\n        newArr.forEach((n: any) => {\r\n          n.dishId = n.id\r\n          n.dishName = n.name\r\n        })\r\n        this.dishList = newArr\r\n      } else {\r\n        this.$message.error(res.data.msg)\r\n      }\r\n    })\r\n  }\r\n  // 点击分类\r\n  private checkTypeHandle(ind: number, id: any) {\r\n    this.keyInd = ind\r\n    this.getDishList(id)\r\n  }\r\n  // 添加菜品\r\n  private checkedListHandle(value: [string]) {\r\n    // TODO 实现倒序 由于value是组件内封装无法从前面添加 所有取巧处理倒序添加\r\n    // 倒序展示 - 数据处理前反正 为正序\r\n    this.checkedListAll.reverse()\r\n    // value 是一个只包含菜品名的数组 需要从 dishList中筛选出 对应的详情\r\n    // 操作添加菜品\r\n    const list = this.allDishList.filter((item: any) => {\r\n      let data\r\n      value.forEach((it: any) => {\r\n        if (item.name == it) {\r\n          data = item\r\n        }\r\n      })\r\n      return data\r\n    })\r\n    // 编辑的时候需要与已有菜品合并\r\n    // 与当前请求下的选择性 然后去重就是当前的列表\r\n    const dishListCat = [...this.checkedListAll, ...list]\r\n    let arrData: any[] = []\r\n    this.checkedListAll = dishListCat.filter((item: any) => {\r\n      let allArrDate\r\n      if (arrData.length == 0) {\r\n        arrData.push(item.name)\r\n        allArrDate = item\r\n      } else {\r\n        const st = arrData.some(it => item.name == it)\r\n        if (!st) {\r\n          arrData.push(item.name)\r\n          allArrDate = item\r\n        }\r\n      }\r\n      return allArrDate\r\n    })\r\n    // 如果是减菜 走这里\r\n    if (value.length < arrData.length) {\r\n      this.checkedListAll = this.checkedListAll.filter((item: any) => {\r\n        if (value.some(it => it == item.name)) {\r\n          return item\r\n        }\r\n      })\r\n    }\r\n    this.$emit('checkList', this.checkedListAll)\r\n    // 数据处理完反转为倒序\r\n    this.checkedListAll.reverse()\r\n  }\r\n\r\n  open(done: any) {\r\n    this.dishListCache = JSON.parse(JSON.stringify(this.checkList))\r\n  }\r\n\r\n  close(done: any) {\r\n    this.checkList = this.dishListCache\r\n  }\r\n\r\n  // 删除\r\n  private delCheck(name: any) {\r\n    const index = this.checkedList.findIndex(it => it === name)\r\n    const indexAll = this.checkedListAll.findIndex(\r\n      (it: any) => it.name === name\r\n    )\r\n\r\n    this.checkedList.splice(index, 1)\r\n    this.checkedListAll.splice(indexAll, 1)\r\n    this.$emit('checkList', this.checkedListAll)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.addDish {\r\n  .el-checkbox__label {\r\n    width: 100%;\r\n  }\r\n  .empty-box {\r\n    margin-top: 50px;\r\n    margin-bottom: 0px;\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.addDish {\r\n  padding: 0 20px;\r\n  display: flex;\r\n  line-height: 40px;\r\n  .empty-box {\r\n    img {\r\n      width: 190px;\r\n      height: 147px;\r\n    }\r\n  }\r\n\r\n  .borderNone {\r\n    border: none !important;\r\n  }\r\n  span,\r\n  .tit {\r\n    color: #333;\r\n  }\r\n  .leftCont {\r\n    display: flex;\r\n    border-right: solid 1px #efefef;\r\n    width: 60%;\r\n    padding: 15px;\r\n    .tabBut {\r\n      width: 110px;\r\n      font-weight: bold;\r\n      border-right: solid 2px #f4f4f4;\r\n      span {\r\n        display: block;\r\n        text-align: center;\r\n        // border-right: solid 2px #f4f4f4;\r\n        cursor: pointer;\r\n        position: relative;\r\n      }\r\n    }\r\n    .act {\r\n      border-color: $mine !important;\r\n      color: $mine !important;\r\n    }\r\n    .act::after {\r\n      content: ' ';\r\n      display: inline-block;\r\n      background-color: $mine;\r\n      width: 2px;\r\n      height: 40px;\r\n      position: absolute;\r\n      right: -2px;\r\n    }\r\n    .tabList {\r\n      flex: 1;\r\n      padding: 15px;\r\n      height: 400px;\r\n      overflow-y: scroll;\r\n      .table {\r\n        border: solid 1px #f4f4f4;\r\n        border-bottom: solid 1px #f4f4f4;\r\n        .items {\r\n          border-bottom: solid 1px #f4f4f4;\r\n          padding: 0 10px;\r\n          display: flex;\r\n          .el-checkbox,\r\n          .el-checkbox__label {\r\n            width: 100%;\r\n          }\r\n          .item {\r\n            display: flex;\r\n            padding-right: 20px;\r\n            span {\r\n              display: inline-block;\r\n              text-align: center;\r\n              flex: 1;\r\n              font-weight: normal;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ritCont {\r\n    width: 40%;\r\n    .tit {\r\n      margin: 0 15px;\r\n      font-weight: bold;\r\n    }\r\n    .items {\r\n      height: 338px;\r\n      padding: 4px 15px;\r\n      overflow: scroll;\r\n    }\r\n    .item {\r\n      box-shadow: 0px 1px 4px 3px rgba(0, 0, 0, 0.03);\r\n      display: flex;\r\n      text-align: center;\r\n      padding: 0 10px;\r\n      margin-bottom: 20px;\r\n      border-radius: 6px;\r\n      color: #818693;\r\n      span:first-child {\r\n        text-align: left;\r\n        color: #20232a;\r\n        flex: 70%;\r\n      }\r\n      .price {\r\n        display: inline-block;\r\n        flex: 70%;\r\n        text-align: left;\r\n      }\r\n      .del {\r\n        cursor: pointer;\r\n        img {\r\n          position: relative;\r\n          top: 5px;\r\n          width: 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}