{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/turnoverStatistics.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/turnoverStatistics.vue", "mtime": 1656313985000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'TurnoverStatistics',\n})\nexport default class extends Vue {\n  @Prop() private turnoverdata!: any\n  @Watch('turnoverdata')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('main') as any\n    const myChart = echarts.init(chartDom)\n\n    var option: any\n    option = {\n      // title: {\n      //   text: '营业额(元)',\n      //   top: 'bottom',\n      //   left: 'center',\n      //   textAlign: 'center',\n      //   textStyle: {\n      //     fontSize: 12,\n      //     fontWeight: 'normal',\n      //   },\n      // },\n      tooltip: {\n        trigger: 'axis',\n      },\n      grid: {\n        top: '5%',\n        left: '10',\n        right: '50',\n        bottom: '12%',\n        containLabel: true,\n      },\n      xAxis: {\n        type: 'category',\n        boundaryGap: false,\n        axisLabel: {\n          //X轴字体颜色\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n        },\n        axisLine: {\n          //X轴线颜色\n          lineStyle: {\n            color: '#E5E4E4',\n            width: 1, //x轴线的宽度\n          },\n        },\n        data: this.turnoverdata.dateList, //后端传来的动态数据\n      },\n      yAxis: [\n        {\n          type: 'value',\n          min: 0,\n          //max: 50000,\n          //interval: 1000,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px',\n            }\n            // formatter: \"{value} ml\",//单位\n          }\n        }\n      ],\n      series: [\n        {\n          name: '营业额',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10,\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#F29C1B',\n              lineStyle: {\n                color: '#FFD000',\n              },\n            },\n            emphasis: {\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FFC100',\n            },\n          },\n\n          data: this.turnoverdata.turnoverList,\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n", {"version": 3, "sources": ["turnoverStatistics.vue"], "names": [], "mappings": ";AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "turnoverStatistics.vue", "sourceRoot": "src/views/statistics/components", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <h2 class=\"homeTitle\">营业额统计</h2>\n    <div class=\"charBox\">\n      <div id=\"main\" style=\"width: 100%; height: 320px\"></div>\n      <ul class=\"orderListLine turnover\">\n        <li>营业额(元)</li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'TurnoverStatistics',\n})\nexport default class extends Vue {\n  @Prop() private turnoverdata!: any\n  @Watch('turnoverdata')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('main') as any\n    const myChart = echarts.init(chartDom)\n\n    var option: any\n    option = {\n      // title: {\n      //   text: '营业额(元)',\n      //   top: 'bottom',\n      //   left: 'center',\n      //   textAlign: 'center',\n      //   textStyle: {\n      //     fontSize: 12,\n      //     fontWeight: 'normal',\n      //   },\n      // },\n      tooltip: {\n        trigger: 'axis',\n      },\n      grid: {\n        top: '5%',\n        left: '10',\n        right: '50',\n        bottom: '12%',\n        containLabel: true,\n      },\n      xAxis: {\n        type: 'category',\n        boundaryGap: false,\n        axisLabel: {\n          //X轴字体颜色\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n        },\n        axisLine: {\n          //X轴线颜色\n          lineStyle: {\n            color: '#E5E4E4',\n            width: 1, //x轴线的宽度\n          },\n        },\n        data: this.turnoverdata.dateList, //后端传来的动态数据\n      },\n      yAxis: [\n        {\n          type: 'value',\n          min: 0,\n          //max: 50000,\n          //interval: 1000,\n          axisLabel: {\n            textStyle: {\n              color: '#666',\n              fontSize: '12px',\n            }\n            // formatter: \"{value} ml\",//单位\n          }\n        }\n      ],\n      series: [\n        {\n          name: '营业额',\n          type: 'line',\n          // stack: 'Total',\n          smooth: false, //否平滑曲线\n          showSymbol: false, //未显示鼠标上移的圆点\n          symbolSize: 10,\n          // symbol:\"circle\", //设置折线点定位实心点\n          itemStyle: {\n            normal: {\n              color: '#F29C1B',\n              lineStyle: {\n                color: '#FFD000',\n              },\n            },\n            emphasis: {\n              color: '#fff',\n              borderWidth: 5,\n              borderColor: '#FFC100',\n            },\n          },\n\n          data: this.turnoverdata.turnoverList,\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n</script>\n"]}]}