{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/index.vue", "mtime": 1656054703000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue } from 'vue-property-decorator'\nimport {\n  get1stAndToday,\n  past7Day,\n  past30Day,\n  pastWeek,\n  pastMonth,\n} from '@/utils/formValidate'\nimport {\n  getDataOverView, //数据概览\n  getTurnoverStatistics,\n  getUserStatistics,\n  getOrderStatistics,\n  getTop,\n} from '@/api/index'\n// 组件\n// 标题\nimport TitleIndex from './components/titleIndex.vue'\n// 营业额统计\nimport TurnoverStatistics from './components/turnoverStatistics.vue'\n// 用户统计\nimport UserStatistics from './components/userStatistics.vue'\n// 订单统计\nimport OrderStatistics from './components/orderStatistics.vue'\n// 排名\nimport Top from './components/top10.vue'\n@Component({\n  name: 'Dashboard',\n  components: {\n    TitleIndex,\n    TurnoverStatistics,\n    UserStatistics,\n    OrderStatistics,\n    Top,\n  },\n})\nexport default class extends Vue {\n  private overviewData = {} as any\n  private flag = 2\n  private tateData = []\n  private turnoverData = {} as any\n  private userData = {}\n  private orderData = {\n    data: {},\n  } as any\n  private top10Data = {}\n  created() {\n    //this.init(this.flag)\n    this.getTitleNum(2);\n  }\n  // 获取基本数据\n  init(begin: any,end:any) {\n    this.$nextTick(() => {\n      this.getTurnoverStatisticsData(begin,end)\n      this.getUserStatisticsData(begin,end)\n      this.getOrderStatisticsData(begin,end)\n      this.getTopData(begin,end)\n    })\n  }\n\n  // 获取营业额统计数据\n  async getTurnoverStatisticsData(begin: any ,end:any) {\n    const data = await getTurnoverStatistics({ begin: begin,end:end })\n    const turnoverData = data.data.data\n    this.turnoverData = {\n      dateList: turnoverData.dateList.split(','),\n      turnoverList: turnoverData.turnoverList.split(',')\n    }\n    // this.tateData = this.turnoverData.date\n    // const arr = []\n    // this.tateData.forEach((val) => {\n    //   let date = new Date()\n    //   let year = date.getFullYear()\n    //   arr.push(year + '-' + val)\n    // })\n    // this.tateData = arr\n  }\n  // 获取用户统计数据\n  async getUserStatisticsData(begin: any ,end:any) {\n    const data = await getUserStatistics({ begin: begin,end:end })\n    const userData = data.data.data\n    this.userData = {\n      dateList: userData.dateList.split(','),\n      totalUserList: userData.totalUserList.split(','),\n      newUserList: userData.newUserList.split(','),\n    }\n  }\n  // 获取订单统计数据\n  async getOrderStatisticsData(begin: any ,end:any) {\n    const data = await getOrderStatistics({begin: begin,end:end })\n    const orderData = data.data.data\n    this.orderData = {\n      data: {\n        dateList: orderData.dateList.split(','),\n        orderCountList: orderData.orderCountList.split(','),\n        validOrderCountList: orderData.validOrderCountList.split(','),\n        //orderCompletionRateList: orderData.orderCompletionRateList.split(','),\n      },\n      totalOrderCount: orderData.totalOrderCount,\n      validOrderCount: orderData.validOrderCount,\n      orderCompletionRate: orderData.orderCompletionRate\n    }\n  }\n  // 获取排行数据\n  async getTopData(begin: any ,end:any) {\n    const data = await getTop({begin: begin,end:end })\n    const top10Data = data.data.data\n    this.top10Data = {\n      nameList: top10Data.nameList.split(',').reverse(),\n      numberList: top10Data.numberList.split(',').reverse(),\n    }\n    console.log(this.top10Data)\n  }\n  // 获取当前选中的tab时间\n  getTitleNum(data) {\n    switch (data) {\n      case 1:\n        this.tateData = get1stAndToday()\n        break\n      case 2:\n        this.tateData = past7Day()\n        break\n      case 3:\n        this.tateData = past30Day()\n        break\n      case 4:\n        this.tateData = pastWeek()\n        break\n      case 5:\n        this.tateData = pastMonth()\n        break\n    }\n    this.init(this.tateData[0],this.tateData[1])\n  }\n}\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/statistics", "sourcesContent": ["<template>\n  <div class=\"dashboard-container home\">\n    <!-- 标题 -->\n    <TitleIndex @sendTitleInd=\"getTitleNum\" :flag=\"flag\" :tateData=\"tateData\" />\n    <!-- end -->\n    <div class=\"homeMain\">\n      <!-- 营业额统计 -->\n      <TurnoverStatistics :turnoverdata=\"turnoverData\" />\n      <!-- end -->\n      <!-- 用户统计 -->\n      <UserStatistics :userdata=\"userData\" />\n      <!-- end -->\n    </div>\n    <div class=\"homeMain homecon\">\n      <!-- 订单统计 -->\n      <OrderStatistics :orderdata=\"orderData\" :overviewData=\"overviewData\" />\n      <!-- end -->\n      <!-- 销量排名TOP10 -->\n      <Top :top10data=\"top10Data\" />\n      <!-- end -->\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue } from 'vue-property-decorator'\nimport {\n  get1stAndToday,\n  past7Day,\n  past30Day,\n  pastWeek,\n  pastMonth,\n} from '@/utils/formValidate'\nimport {\n  getDataOverView, //数据概览\n  getTurnoverStatistics,\n  getUserStatistics,\n  getOrderStatistics,\n  getTop,\n} from '@/api/index'\n// 组件\n// 标题\nimport TitleIndex from './components/titleIndex.vue'\n// 营业额统计\nimport TurnoverStatistics from './components/turnoverStatistics.vue'\n// 用户统计\nimport UserStatistics from './components/userStatistics.vue'\n// 订单统计\nimport OrderStatistics from './components/orderStatistics.vue'\n// 排名\nimport Top from './components/top10.vue'\n@Component({\n  name: 'Dashboard',\n  components: {\n    TitleIndex,\n    TurnoverStatistics,\n    UserStatistics,\n    OrderStatistics,\n    Top,\n  },\n})\nexport default class extends Vue {\n  private overviewData = {} as any\n  private flag = 2\n  private tateData = []\n  private turnoverData = {} as any\n  private userData = {}\n  private orderData = {\n    data: {},\n  } as any\n  private top10Data = {}\n  created() {\n    //this.init(this.flag)\n    this.getTitleNum(2);\n  }\n  // 获取基本数据\n  init(begin: any,end:any) {\n    this.$nextTick(() => {\n      this.getTurnoverStatisticsData(begin,end)\n      this.getUserStatisticsData(begin,end)\n      this.getOrderStatisticsData(begin,end)\n      this.getTopData(begin,end)\n    })\n  }\n\n  // 获取营业额统计数据\n  async getTurnoverStatisticsData(begin: any ,end:any) {\n    const data = await getTurnoverStatistics({ begin: begin,end:end })\n    const turnoverData = data.data.data\n    this.turnoverData = {\n      dateList: turnoverData.dateList.split(','),\n      turnoverList: turnoverData.turnoverList.split(',')\n    }\n    // this.tateData = this.turnoverData.date\n    // const arr = []\n    // this.tateData.forEach((val) => {\n    //   let date = new Date()\n    //   let year = date.getFullYear()\n    //   arr.push(year + '-' + val)\n    // })\n    // this.tateData = arr\n  }\n  // 获取用户统计数据\n  async getUserStatisticsData(begin: any ,end:any) {\n    const data = await getUserStatistics({ begin: begin,end:end })\n    const userData = data.data.data\n    this.userData = {\n      dateList: userData.dateList.split(','),\n      totalUserList: userData.totalUserList.split(','),\n      newUserList: userData.newUserList.split(','),\n    }\n  }\n  // 获取订单统计数据\n  async getOrderStatisticsData(begin: any ,end:any) {\n    const data = await getOrderStatistics({begin: begin,end:end })\n    const orderData = data.data.data\n    this.orderData = {\n      data: {\n        dateList: orderData.dateList.split(','),\n        orderCountList: orderData.orderCountList.split(','),\n        validOrderCountList: orderData.validOrderCountList.split(','),\n        //orderCompletionRateList: orderData.orderCompletionRateList.split(','),\n      },\n      totalOrderCount: orderData.totalOrderCount,\n      validOrderCount: orderData.validOrderCount,\n      orderCompletionRate: orderData.orderCompletionRate\n    }\n  }\n  // 获取排行数据\n  async getTopData(begin: any ,end:any) {\n    const data = await getTop({begin: begin,end:end })\n    const top10Data = data.data.data\n    this.top10Data = {\n      nameList: top10Data.nameList.split(',').reverse(),\n      numberList: top10Data.numberList.split(',').reverse(),\n    }\n    console.log(this.top10Data)\n  }\n  // 获取当前选中的tab时间\n  getTitleNum(data) {\n    switch (data) {\n      case 1:\n        this.tateData = get1stAndToday()\n        break\n      case 2:\n        this.tateData = past7Day()\n        break\n      case 3:\n        this.tateData = past30Day()\n        break\n      case 4:\n        this.tateData = pastWeek()\n        break\n      case 5:\n        this.tateData = pastMonth()\n        break\n    }\n    this.init(this.tateData[0],this.tateData[1])\n  }\n}\n</script>\n\n<style lang=\"scss\">\n</style>\n"]}]}