{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/Breadcrumb/index.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<el-breadcrumb\n  class=\"app-breadcrumb\"\n  separator=\"/\"\n>\n  <transition-group name=\"breadcrumb\">\n    <el-breadcrumb-item\n      v-for=\"(item, index) in breadcrumbs\"\n      :key=\"item.path\"\n    >\n      <span\n        v-if=\"item.redirect === 'noredirect' || index === breadcrumbs.length-1\"\n        class=\"no-redirect\"\n      >{{ item.meta.title }}</span>\n      <a\n        v-else\n        @click.prevent=\"handleLink(item)\"\n      >{{ item.meta.title }}</a>\n    </el-breadcrumb-item>\n  </transition-group>\n</el-breadcrumb>\n", null]}