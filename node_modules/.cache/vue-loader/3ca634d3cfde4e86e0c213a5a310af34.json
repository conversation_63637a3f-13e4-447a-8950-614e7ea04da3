{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/components/password.vue?vue&type=template&id=2ff4cc77", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/components/password.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<el-dialog\n  title=\"修改密码\"\n  :visible.sync=\"dialogFormVisible\"\n  width=\"568px\"\n  class=\"pwdCon\"\n  @close=\"handlePwdClose()\"\n>\n  <el-form :model=\"form\" label-width=\"85px\" :rules=\"rules\" ref=\"form\">\n    <el-form-item label=\"原始密码：\" prop=\"oldPassword\">\n      <el-input\n        v-model=\"form.oldPassword\"\n        type=\"password\"\n        placeholder=\"请输入\"\n      ></el-input>\n    </el-form-item>\n    <el-form-item label=\"新密码：\" prop=\"newPassword\">\n      <el-input\n        v-model=\"form.newPassword\"\n        type=\"password\"\n        placeholder=\"6 - 20位密码，数字或字母，区分大小写\"\n      ></el-input>\n    </el-form-item>\n    <el-form-item label=\"确认密码：\" prop=\"affirmPassword\">\n      <el-input\n        v-model=\"form.affirmPassword\"\n        type=\"password\"\n        placeholder=\"请输入\"\n      ></el-input>\n    </el-form-item>\n  </el-form>\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"handlePwdClose()\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"handleSave()\">保 存</el-button>\n  </div>\n</el-dialog>\n", null]}