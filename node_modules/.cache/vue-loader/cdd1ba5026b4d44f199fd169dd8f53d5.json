{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport path from 'path'\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport { Route, RouteConfig } from 'vue-router'\r\nimport { isExternal } from '@/utils/validate'\r\nimport SidebarItemLink from './SidebarItemLink.vue'\r\n\r\n@Component({\r\n  name: 'SidebarItem',\r\n  components: {\r\n    SidebarItemLink,\r\n  },\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ required: true }) private item!: RouteConfig\r\n  @Prop({ default: false }) private isCollapse!: boolean\r\n  @Prop({ default: true }) private isFirstLevel!: boolean\r\n  @Prop({ default: '' }) private basePath!: string\r\n\r\n  get showingChildNumber() {\r\n    if (this.item.children) {\r\n      const showingChildren = this.item.children.filter((item) => {\r\n        if (item.meta && item.meta.hidden) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n      return showingChildren.length\r\n    }\r\n    return 0\r\n  }\r\n\r\n  get roles() {\r\n    return UserModule.roles\r\n  }\r\n\r\n  get theOnlyOneChild() {\r\n    if (this.showingChildNumber > 0) {\r\n      return null\r\n    }\r\n    if (this.item.children) {\r\n      for (let child of this.item.children) {\r\n        if (!child.meta || !child.meta.hidden) {\r\n          return child\r\n        }\r\n      }\r\n    }\r\n    // If there is no children, return itself with path removed,\r\n    // because this.basePath already conatins item's path information\r\n    return { ...this.item, path: '' }\r\n  }\r\n\r\n  private resolvePath(routePath: string) {\r\n    if (isExternal(routePath)) {\r\n      return routePath\r\n    }\r\n    if (isExternal(this.basePath)) {\r\n      return this.basePath\r\n    }\r\n    return path.resolve(this.basePath, routePath)\r\n  }\r\n}\r\n", {"version": 3, "sources": ["SidebarItem.vue"], "names": [], "mappings": ";AA4DA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SidebarItem.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- <div\r\n      v-if=\" !item.meta || !item.meta.hidden \"\r\n      :class=\"['menu-wrapper', isCollapse ? 'simple-mode' : 'full-mode', {'first-level': isFirstLevel}]\"\r\n    > -->\r\n    <div\r\n      v-if=\"!item.meta || !item.meta.hidden\"\r\n      :class=\"['menu-wrapper', 'full-mode', { 'first-level': isFirstLevel }]\"\r\n    >\r\n      <template v-if=\"theOnlyOneChild && !theOnlyOneChild.children\">\r\n        <sidebar-item-link\r\n          v-if=\"theOnlyOneChild.meta\"\r\n          :to=\"resolvePath(theOnlyOneChild.path)\"\r\n        >\r\n          <el-menu-item\r\n            :index=\"resolvePath(theOnlyOneChild.path)\"\r\n            :class=\"{ 'submenu-title-noDropdown': isFirstLevel }\"\r\n          >\r\n            <!-- <i v-if=\"theOnlyOneChild.meta.title==='工作台'\" class=\"iconfont icon img-icon-sel\" /> -->\r\n            <!-- <svg-icon v-if=\"theOnlyOneChild.meta.title==='工作台'\" name=\"dashboard\" width=\"20\" height=\"20\"></svg-icon> -->\r\n            <i\r\n              v-if=\"theOnlyOneChild.meta.icon\"\r\n              class=\"iconfont\"\r\n              :class=\"theOnlyOneChild.meta.icon\"\r\n            />\r\n            <span v-if=\"theOnlyOneChild.meta.title\" slot=\"title\">{{\r\n              theOnlyOneChild.meta.title\r\n            }}</span>\r\n          </el-menu-item>\r\n        </sidebar-item-link>\r\n      </template>\r\n      <el-submenu v-else :index=\"resolvePath(item.path)\" popper-append-to-body>\r\n        <template slot=\"title\">\r\n          <i\r\n            v-if=\"item.meta && item.meta.icon\"\r\n            class=\"iconfont\"\r\n            :class=\"item.meta.icon\"\r\n          />\r\n          <span v-if=\"item.meta && item.meta.title\" slot=\"title\">{{\r\n            item.meta.title\r\n          }}</span>\r\n        </template>\r\n        <template v-if=\"item.children\">\r\n          <sidebar-item\r\n            v-for=\"child in item.children\"\r\n            :key=\"child.path\"\r\n            :item=\"child\"\r\n            :is-collapse=\"isCollapse\"\r\n            :is-first-level=\"false\"\r\n            :base-path=\"resolvePath(child.path)\"\r\n            class=\"nest-menu\"\r\n          />\r\n        </template>\r\n      </el-submenu>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport path from 'path'\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport { Route, RouteConfig } from 'vue-router'\r\nimport { isExternal } from '@/utils/validate'\r\nimport SidebarItemLink from './SidebarItemLink.vue'\r\n\r\n@Component({\r\n  name: 'SidebarItem',\r\n  components: {\r\n    SidebarItemLink,\r\n  },\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ required: true }) private item!: RouteConfig\r\n  @Prop({ default: false }) private isCollapse!: boolean\r\n  @Prop({ default: true }) private isFirstLevel!: boolean\r\n  @Prop({ default: '' }) private basePath!: string\r\n\r\n  get showingChildNumber() {\r\n    if (this.item.children) {\r\n      const showingChildren = this.item.children.filter((item) => {\r\n        if (item.meta && item.meta.hidden) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n      return showingChildren.length\r\n    }\r\n    return 0\r\n  }\r\n\r\n  get roles() {\r\n    return UserModule.roles\r\n  }\r\n\r\n  get theOnlyOneChild() {\r\n    if (this.showingChildNumber > 0) {\r\n      return null\r\n    }\r\n    if (this.item.children) {\r\n      for (let child of this.item.children) {\r\n        if (!child.meta || !child.meta.hidden) {\r\n          return child\r\n        }\r\n      }\r\n    }\r\n    // If there is no children, return itself with path removed,\r\n    // because this.basePath already conatins item's path information\r\n    return { ...this.item, path: '' }\r\n  }\r\n\r\n  private resolvePath(routePath: string) {\r\n    if (isExternal(routePath)) {\r\n      return routePath\r\n    }\r\n    if (isExternal(this.basePath)) {\r\n      return this.basePath\r\n    }\r\n    return path.resolve(this.basePath, routePath)\r\n  }\r\n}\r\n</script>\r\n"]}]}