{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue?vue&type=style&index=0&id=33ec43fc&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/index.vue", "mtime": 1654503351000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.logo {\r\n  text-align: center;\r\n  background-color: #ffc100;\r\n  padding: 15px 0 0;\r\n  height: 60px;\r\n  img {\r\n    display: inline-block;\r\n  }\r\n}\r\n.sidebar-logo-mini {\r\n  img {\r\n    width: 30px;\r\n    height: 30px;\r\n  }\r\n}\r\n.el-scrollbar {\r\n  height: 100%;\r\n  background-color: rgb(52, 55, 68);\r\n}\r\n\r\n.el-menu {\r\n  border: none;\r\n  height: calc(95vh - 23px);\r\n  width: 100% !important;\r\n  padding: 47px 15px 0;\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"logo\">\r\n      <!-- <img\r\n        src=\"./../../../assets/logo.png\"\r\n        width=\"122.5\"\r\n        alt=\"\"\r\n      > -->\r\n      <!-- <img\r\n        src=\"@/assets/login/login-logo.png\"\r\n        alt=\"\"\r\n        style=\"width: 120px; height: 31px\"\r\n      /> -->\r\n      <div v-if=\"!isCollapse\"\r\n           class=\"sidebar-logo\">\r\n        <img src=\"@/assets/login/logo.png\"\r\n             style=\"width: 120px; height: 31px\">\r\n      </div>\r\n      <div v-else\r\n           class=\"sidebar-logo-mini\">\r\n        <img src=\"@/assets/login/mini-logo.png\">\r\n      </div>\r\n    </div>\r\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\r\n      <el-menu :default-openeds=\"defOpen\"\r\n               :default-active=\"defAct\"\r\n               :collapse=\"isCollapse\"\r\n               :background-color=\"variables.menuBg\"\r\n               :text-color=\"variables.menuText\"\r\n               :active-text-color=\"variables.menuActiveText\"\r\n               :unique-opened=\"false\"\r\n               :collapse-transition=\"false\"\r\n               mode=\"vertical\">\r\n        <sidebar-item v-for=\"route in routes\"\r\n                      :key=\"route.path\"\r\n                      :item=\"route\"\r\n                      :base-path=\"route.path\"\r\n                      :is-collapse=\"isCollapse\" />\r\n        <!-- <div class=\"sub-menu\">\r\n          <div class=\"avatarName\">\r\n            {{ name }}\r\n          </div>\r\n          <div class=\"img\">\r\n            <img\r\n              src=\"./../../../assets/icons/<EMAIL>\"\r\n              class=\"outLogin\"\r\n              alt=\"退出\"\r\n              @click=\"logout\"\r\n            />\r\n          </div>\r\n        </div> -->\r\n      </el-menu>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Prop, Vue } from 'vue-property-decorator'\r\nimport { AppModule } from '@/store/modules/app'\r\nimport { UserModule } from '@/store/modules/user'\r\nimport SidebarItem from './SidebarItem.vue'\r\nimport variables from '@/styles/_variables.scss'\r\nimport { getSidebarStatus, setSidebarStatus } from '@/utils/cookies'\r\nimport Cookies from 'js-cookie'\r\n@Component({\r\n  name: 'SideBar',\r\n  components: {\r\n    SidebarItem\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private restKey: number = 0\r\n  get name() {\r\n    return (UserModule.userInfo as any).name\r\n      ? (UserModule.userInfo as any).name\r\n      : JSON.parse(Cookies.get('user_info') as any).name\r\n  }\r\n  get defOpen() {\r\n    // const urlArr = this.$route.path.split('/')\r\n    // const openStr = urlArr.length > 2 ? `/${urlArr[1]}` : '/'\r\n    let path = ['/']\r\n    this.routes.forEach((n: any, i: number) => {\r\n      if (n.meta.roles && n.meta.roles[0] === this.roles[0]) {\r\n        path.splice(0, 1, n.path)\r\n      }\r\n    })\r\n    return path\r\n  }\r\n\r\n  get defAct() {\r\n    let path = this.$route.path\r\n    return path\r\n  }\r\n\r\n  get sidebar() {\r\n    return AppModule.sidebar\r\n  }\r\n\r\n  get roles() {\r\n    return UserModule.roles\r\n  }\r\n\r\n  get routes() {\r\n    let routes = JSON.parse(\r\n      JSON.stringify([...(this.$router as any).options.routes])\r\n    )\r\n    console.log('-=-=routes=-=-=', routes)\r\n    console.log('-=-=routes=-=-=', this.roles[0])\r\n    let menuList = []\r\n    let menu = routes.find(item => item.path === '/')\r\n    if (menu) {\r\n      menuList = menu.children\r\n    }\r\n    console.log('-=-=routes=-wwww=-=', routes)\r\n    return menuList\r\n  }\r\n\r\n  get variables() {\r\n    return variables\r\n  }\r\n\r\n  get isCollapse() {\r\n    return !this.sidebar.opened\r\n  }\r\n  private async logout() {\r\n    this.$store.dispatch('LogOut').then(() => {\r\n      // location.href = '/'\r\n      this.$router.replace({ path: '/login' })\r\n    })\r\n    // this.$router.push(`/login?redirect=${this.$route.fullPath}`)\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.logo {\r\n  text-align: center;\r\n  background-color: #ffc100;\r\n  padding: 15px 0 0;\r\n  height: 60px;\r\n  img {\r\n    display: inline-block;\r\n  }\r\n}\r\n.sidebar-logo-mini {\r\n  img {\r\n    width: 30px;\r\n    height: 30px;\r\n  }\r\n}\r\n.el-scrollbar {\r\n  height: 100%;\r\n  background-color: rgb(52, 55, 68);\r\n}\r\n\r\n.el-menu {\r\n  border: none;\r\n  height: calc(95vh - 23px);\r\n  width: 100% !important;\r\n  padding: 47px 15px 0;\r\n}\r\n</style>\r\n"]}]}