{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/top10.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/statistics/components/top10.vue", "mtime": 1657266895000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'Top',\n})\nexport default class extends Vue {\n  @Prop() private top10data!: any\n  @Watch('top10data')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('top') as any\n    const myChart = echarts.init(chartDom)\n    var option: any\n    option = {\n      tooltip: {\n        trigger: 'axis',\n        backgroundColor: '#fff', //背景颜色（此时为默认色）\n        borderRadius: 2, //边框圆角\n        textStyle: {\n          color: '#333', //字体颜色\n          fontSize: 12, //字体大小\n          fontWeight: 300,\n        },\n      },\n      grid: {\n        top: '-10px',\n        left: '0',\n        right: '0',\n        bottom: '0',\n        containLabel: true,\n      },\n      xAxis: {\n        show: false,\n      },\n      yAxis: {\n        //   隐藏y轴坐标轴\n        axisLine: {\n          show: false,\n        },\n        // 隐藏y轴刻度线\n        axisTick: {\n          show: false,\n          alignWithLabel: true,\n        },\n        type: 'category',\n        // interval: 100,\n        axisLabel: {\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n          // formatter: \"{value} ml\",//单位\n        },\n        data: this.top10data.nameList,\n      },\n      series: [\n        {\n          data: this.top10data.numberList,\n          type: 'bar',\n          showBackground: true,\n          backgroundStyle: {\n            color: '#F3F4F7',\n          },\n          barWidth: 20,\n          barGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n          barCategoryGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n\n          itemStyle: {\n            emphasis: {\n              barBorderRadius: 30,\n            },\n            normal: {\n              barBorderRadius: [0, 10, 10, 0], // 圆角\n              color: new echarts.graphic.LinearGradient( // 渐变色\n                1,\n                0,\n                0,\n                0, // 渐变色的起止位置, 右/下/左/上\n                [\n                  // offset 位置\n                  { offset: 0, color: '#FFBD00' },\n                  { offset: 1, color: '#FFD000' },\n                ]\n              ),\n              label: {\n                //内容样式\n                show: true,\n                formatter: '{@score}',\n                color: '#333',\n                // position: \"insideLeft\", //内部左对齐\n                position: ['8', '5'], //自定义位置第一个参数为x轴方向，第二个参数为y轴方向，左上角为起点，向右向下为正数，向上向左为负数\n              },\n            },\n          },\n          // label: {\n          //   show: true,\n          //   position: \"left\",\n          //   valueAnimation: true,\n          // },\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n", {"version": 3, "sources": ["top10.vue"], "names": [], "mappings": ";AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "top10.vue", "sourceRoot": "src/views/statistics/components", "sourcesContent": ["<template>\n  <div class=\"container top10\">\n    <h2 class=\"homeTitle\">销量排名TOP10</h2>\n    <div class=\"charBox\">\n      <div id=\"top\" style=\"width: 100%; height: 380px\"></div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { Component, Vue, Prop, Watch } from 'vue-property-decorator'\nimport * as echarts from 'echarts'\n@Component({\n  name: 'Top',\n})\nexport default class extends Vue {\n  @Prop() private top10data!: any\n  @Watch('top10data')\n  getData() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  }\n  initChart() {\n    type EChartsOption = echarts.EChartsOption\n    const chartDom = document.getElementById('top') as any\n    const myChart = echarts.init(chartDom)\n    var option: any\n    option = {\n      tooltip: {\n        trigger: 'axis',\n        backgroundColor: '#fff', //背景颜色（此时为默认色）\n        borderRadius: 2, //边框圆角\n        textStyle: {\n          color: '#333', //字体颜色\n          fontSize: 12, //字体大小\n          fontWeight: 300,\n        },\n      },\n      grid: {\n        top: '-10px',\n        left: '0',\n        right: '0',\n        bottom: '0',\n        containLabel: true,\n      },\n      xAxis: {\n        show: false,\n      },\n      yAxis: {\n        //   隐藏y轴坐标轴\n        axisLine: {\n          show: false,\n        },\n        // 隐藏y轴刻度线\n        axisTick: {\n          show: false,\n          alignWithLabel: true,\n        },\n        type: 'category',\n        // interval: 100,\n        axisLabel: {\n          textStyle: {\n            color: '#666',\n            fontSize: '12px',\n          },\n          // formatter: \"{value} ml\",//单位\n        },\n        data: this.top10data.nameList,\n      },\n      series: [\n        {\n          data: this.top10data.numberList,\n          type: 'bar',\n          showBackground: true,\n          backgroundStyle: {\n            color: '#F3F4F7',\n          },\n          barWidth: 20,\n          barGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n          barCategoryGap: '80%' /*多个并排柱子设置柱子之间的间距*/,\n\n          itemStyle: {\n            emphasis: {\n              barBorderRadius: 30,\n            },\n            normal: {\n              barBorderRadius: [0, 10, 10, 0], // 圆角\n              color: new echarts.graphic.LinearGradient( // 渐变色\n                1,\n                0,\n                0,\n                0, // 渐变色的起止位置, 右/下/左/上\n                [\n                  // offset 位置\n                  { offset: 0, color: '#FFBD00' },\n                  { offset: 1, color: '#FFD000' },\n                ]\n              ),\n              label: {\n                //内容样式\n                show: true,\n                formatter: '{@score}',\n                color: '#333',\n                // position: \"insideLeft\", //内部左对齐\n                position: ['8', '5'], //自定义位置第一个参数为x轴方向，第二个参数为y轴方向，左上角为起点，向右向下为正数，向上向左为负数\n              },\n            },\n          },\n          // label: {\n          //   show: true,\n          //   position: \"left\",\n          //   valueAnimation: true,\n          // },\n        },\n      ],\n    }\n    option && myChart.setOption(option)\n  }\n}\n</script>\n"]}]}