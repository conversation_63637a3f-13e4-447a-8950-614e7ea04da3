{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/index.vue?vue&type=style&index=1&id=23b821be&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/setmeal/index.vue", "mtime": 1694682430000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.dashboard {\r\n  &-container {\r\n    margin: 30px;\r\n\r\n    .container {\r\n      background: #fff;\r\n      position: relative;\r\n      z-index: 1;\r\n      padding: 30px 28px;\r\n      border-radius: 4px;\r\n\r\n      .tableBar {\r\n        margin-bottom: 20px;\r\n        .tableLab {\r\n          float: right;\r\n          span {\r\n            cursor: pointer;\r\n            display: inline-block;\r\n            font-size: 14px;\r\n            padding: 0 20px;\r\n            color: $gray-2;\r\n          }\r\n        }\r\n      }\r\n\r\n      .tableBox {\r\n        width: 100%;\r\n        border: 1px solid $gray-5;\r\n        border-bottom: 0;\r\n      }\r\n\r\n      .pageList {\r\n        text-align: center;\r\n        margin-top: 30px;\r\n      }\r\n      //查询黑色按钮样式\r\n      .normal-btn {\r\n        background: #333333;\r\n        color: white;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/setmeal", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"container\">\r\n      <div class=\"tableBar\">\r\n\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nexport default {\r\n  \r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.el-table-column--selection .cell {\r\n  padding-left: 10px;\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.dashboard {\r\n  &-container {\r\n    margin: 30px;\r\n\r\n    .container {\r\n      background: #fff;\r\n      position: relative;\r\n      z-index: 1;\r\n      padding: 30px 28px;\r\n      border-radius: 4px;\r\n\r\n      .tableBar {\r\n        margin-bottom: 20px;\r\n        .tableLab {\r\n          float: right;\r\n          span {\r\n            cursor: pointer;\r\n            display: inline-block;\r\n            font-size: 14px;\r\n            padding: 0 20px;\r\n            color: $gray-2;\r\n          }\r\n        }\r\n      }\r\n\r\n      .tableBox {\r\n        width: 100%;\r\n        border: 1px solid $gray-5;\r\n        border-bottom: 0;\r\n      }\r\n\r\n      .pageList {\r\n        text-align: center;\r\n        margin-top: 30px;\r\n      }\r\n      //查询黑色按钮样式\r\n      .normal-btn {\r\n        background: #333333;\r\n        color: white;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}