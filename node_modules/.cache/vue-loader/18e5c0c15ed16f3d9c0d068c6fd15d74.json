{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue?vue&type=style&index=1&id=1ee0b527&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/components/ImgUpload/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.avatar-uploader .el-icon-plus:after {\r\n  position: absolute;\r\n  display: inline-block;\r\n  content: ' ' !important;\r\n  left: calc(50% - 20px);\r\n  top: calc(50% - 40px);\r\n  width: 40px;\r\n  height: 40px;\r\n  background: url('./../../assets/icons/<EMAIL>') center center\r\n    no-repeat;\r\n  background-size: 20px;\r\n}\r\n\r\n.el-upload-list__item-actions:hover .upload-icon {\r\n  display: inline-block;\r\n}\r\n.el-icon-zoom-in:before {\r\n  content: '\\E626';\r\n}\r\n.el-icon-delete:before {\r\n  content: '\\E612';\r\n}\r\n.el-upload-list__item-actions:hover {\r\n  opacity: 1;\r\n}\r\n.upload-item {\r\n  display: flex;\r\n  align-items: center;\r\n  .el-form-item__content {\r\n    width: 500px !important;\r\n  }\r\n}\r\n.upload-tips {\r\n  font-size: 12px;\r\n  color: #666666;\r\n  display: inline-block;\r\n  line-height: 17px;\r\n  margin-left: 36px;\r\n}\r\n.el-upload-list__item-actions {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  left: 0;\r\n  top: 0;\r\n  cursor: default;\r\n  text-align: center;\r\n  color: #fff;\r\n  opacity: 0;\r\n  font-size: 20px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  transition: opacity 0.3s;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-direction: column;\r\n}\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.avatar-uploader {\r\n  display: inline-block;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #ffc200;\r\n}\r\n.el-upload-span {\r\n  width: 100px;\r\n  height: 30px;\r\n  border: 1px solid #ffffff;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-upload-span:first-child {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 200px;\r\n  height: 160px;\r\n  line-height: 160px;\r\n  text-align: center;\r\n}\r\n\r\n.avatar {\r\n  width: 200px;\r\n  height: 160px;\r\n  display: block;\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImgUpload", "sourcesContent": ["<!--  -->\r\n<template>\r\n  <div class=\"upload-item\">\r\n    <el-upload ref=\"uploadfiles\"\r\n               :accept=\"type\"\r\n               :class=\"{ borderNone: imageUrl }\"\r\n               class=\"avatar-uploader\"\r\n               action=\"/api/common/upload\"\r\n               :show-file-list=\"false\"\r\n               :on-success=\"handleAvatarSuccess\"\r\n               :on-remove=\"handleRemove\"\r\n               :on-error=\"handleError\"\r\n               :before-upload=\"beforeAvatarUpload\"\r\n               :headers=\"headers\">\r\n      <img v-if=\"imageUrl\"\r\n           :src=\"imageUrl\"\r\n           class=\"avatar\">\r\n\r\n      <i v-else\r\n         class=\"el-icon-plus avatar-uploader-icon\" />\r\n      <span v-if=\"imageUrl\"\r\n            class=\"el-upload-list__item-actions\">\r\n        <span class=\"el-upload-span\"\r\n              @click.stop=\"oploadImgDel\">\r\n          删除图片\r\n        </span>\r\n        <span class=\"el-upload-span\"> 重新上传 </span>\r\n      </span>\r\n    </el-upload>\r\n    <p class=\"upload-tips\">\r\n      <slot />\r\n    </p>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Vue, Component, Prop, Watch } from 'vue-property-decorator'\r\nimport { baseUrl } from '@/config.json'\r\nimport { getToken } from '@/utils/cookies'\r\n@Component({\r\n  name: 'UploadImage'\r\n})\r\nexport default class extends Vue {\r\n  @Prop({ default: '.jpg,.jpeg,.png' }) type: string\r\n  @Prop({ default: 2 }) size: number\r\n  @Prop({ default: '' }) propImageUrl: string\r\n\r\n  private headers = {\r\n    token: getToken()\r\n  }\r\n  private imageUrl = ''\r\n  handleRemove() {}\r\n\r\n  @Watch('propImageUrl')\r\n  private onChange(val) {\r\n    this.imageUrl = val\r\n  }\r\n\r\n  handleError(err, file, fileList) {\r\n    console.log(err, file, fileList, 'handleError')\r\n    this.$message({\r\n      message: '图片上传失败',\r\n      type: 'error'\r\n    })\r\n  }\r\n\r\n  handleAvatarSuccess(response: any, file: any, fileList: any) {\r\n    // this.imageUrl = response.data\r\n    // this.imageUrl = `http://************:8080/common/download?name=${response.data}`\r\n    this.imageUrl = `${response.data}`\r\n    // this.imageUrl = `${baseUrl}/common/download?name=${response.data}`\r\n\r\n    this.$emit('imageChange', this.imageUrl)\r\n  }\r\n\r\n  oploadImgDel() {\r\n    this.imageUrl = ''\r\n    this.$emit('imageChange', this.imageUrl)\r\n  }\r\n  beforeAvatarUpload(file) {\r\n    const isLt2M = file.size / 1024 / 1024 < this.size\r\n    if (!isLt2M) {\r\n      this.$message({\r\n        message: `上传文件大小不能超过${this.size}M!`,\r\n        type: 'error'\r\n      })\r\n      return false\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang='scss'>\r\n.borderNone {\r\n  .el-upload {\r\n    border: 1px solid #d9d9d9 !important;\r\n  }\r\n}\r\n</style>\r\n<style scoped lang=\"scss\">\r\n.avatar-uploader .el-icon-plus:after {\r\n  position: absolute;\r\n  display: inline-block;\r\n  content: ' ' !important;\r\n  left: calc(50% - 20px);\r\n  top: calc(50% - 40px);\r\n  width: 40px;\r\n  height: 40px;\r\n  background: url('./../../assets/icons/<EMAIL>') center center\r\n    no-repeat;\r\n  background-size: 20px;\r\n}\r\n\r\n.el-upload-list__item-actions:hover .upload-icon {\r\n  display: inline-block;\r\n}\r\n.el-icon-zoom-in:before {\r\n  content: '\\E626';\r\n}\r\n.el-icon-delete:before {\r\n  content: '\\E612';\r\n}\r\n.el-upload-list__item-actions:hover {\r\n  opacity: 1;\r\n}\r\n.upload-item {\r\n  display: flex;\r\n  align-items: center;\r\n  .el-form-item__content {\r\n    width: 500px !important;\r\n  }\r\n}\r\n.upload-tips {\r\n  font-size: 12px;\r\n  color: #666666;\r\n  display: inline-block;\r\n  line-height: 17px;\r\n  margin-left: 36px;\r\n}\r\n.el-upload-list__item-actions {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  left: 0;\r\n  top: 0;\r\n  cursor: default;\r\n  text-align: center;\r\n  color: #fff;\r\n  opacity: 0;\r\n  font-size: 20px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  transition: opacity 0.3s;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-direction: column;\r\n}\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.avatar-uploader {\r\n  display: inline-block;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #ffc200;\r\n}\r\n.el-upload-span {\r\n  width: 100px;\r\n  height: 30px;\r\n  border: 1px solid #ffffff;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-upload-span:first-child {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 200px;\r\n  height: 160px;\r\n  line-height: 160px;\r\n  text-align: center;\r\n}\r\n\r\n.avatar {\r\n  width: 200px;\r\n  height: 160px;\r\n  display: block;\r\n}\r\n</style>\r\n"]}]}