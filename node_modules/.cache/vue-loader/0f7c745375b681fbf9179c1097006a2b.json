{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/index.vue?vue&type=style&index=0&id=27a2ef37&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/dish/index.vue", "mtime": 1654007213000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/css-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/postcss-loader/src/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/sass-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/style-resources-loader/lib/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n.el-table-column--selection .cell {\r\n  padding-left: 10px;\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0UA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dish", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"container\">\r\n      <div class=\"tableBar\">\r\n        <label style=\"margin-right: 10px\">菜品名称：</label>\r\n        <el-input v-model=\"input\"\r\n                  placeholder=\"请填写菜品名称\"\r\n                  style=\"width: 14%\"\r\n                  clearable\r\n                  @clear=\"init\"\r\n                  @keyup.enter.native=\"initFun\" />\r\n\r\n        <label style=\"margin-right: 10px; margin-left: 20px\">菜品分类：</label>\r\n        <el-select v-model=\"categoryId\"\r\n                   style=\"width: 14%\"\r\n                   placeholder=\"请选择\"\r\n                   clearable\r\n                   @clear=\"init\">\r\n          <el-option v-for=\"item in dishCategoryList\"\r\n                     :key=\"item.value\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.value\" />\r\n        </el-select>\r\n\r\n        <label style=\"margin-right: 10px; margin-left: 20px\">售卖状态：</label>\r\n        <el-select v-model=\"dishStatus\"\r\n                   style=\"width: 14%\"\r\n                   placeholder=\"请选择\"\r\n                   clearable\r\n                   @clear=\"init\">\r\n          <el-option v-for=\"item in saleStatus\"\r\n                     :key=\"item.value\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.value\" />\r\n        </el-select>\r\n        <el-button class=\"normal-btn continue\"\r\n                   @click=\"init(true)\">\r\n          查询\r\n        </el-button>\r\n\r\n        <div class=\"tableLab\">\r\n          <span class=\"delBut non\"\r\n                @click=\"deleteHandle('批量', null)\">批量删除</span>\r\n          <!-- <span class=\"blueBug non\" @click=\"statusHandle('1')\">批量启售</span>\r\n          <span\r\n            style=\"border: none\"\r\n            class=\"delBut non\"\r\n            @click=\"statusHandle('0')\"\r\n            >批量停售</span\r\n          > -->\r\n          <el-button type=\"primary\"\r\n                     style=\"margin-left: 15px\"\r\n                     @click=\"addDishtype('add')\">\r\n            + 新建菜品\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <el-table v-if=\"tableData.length\"\r\n                :data=\"tableData\"\r\n                stripe\r\n                class=\"tableBox\"\r\n                @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\"\r\n                         width=\"25\" />\r\n        <el-table-column prop=\"name\"\r\n                         label=\"菜品名称\" />\r\n        <el-table-column prop=\"image\"\r\n                         label=\"图片\">\r\n          <template slot-scope=\"{ row }\">\r\n            <el-image style=\"width: 80px; height: 40px; border: none; cursor: pointer\"\r\n                      :src=\"row.image\">\r\n              <div slot=\"error\"\r\n                   class=\"image-slot\">\r\n                <img src=\"./../../assets/noImg.png\"\r\n                     style=\"width: auto; height: 40px; border: none\">\r\n              </div>\r\n            </el-image>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"categoryName\"\r\n                         label=\"菜品分类\" />\r\n        <el-table-column label=\"售价\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"margin-right: 10px\">￥{{ (scope.row.price ).toFixed(2)*100/100 }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"售卖状态\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"tableColumn-status\"\r\n                 :class=\"{ 'stop-use': String(scope.row.status) === '0' }\">\r\n              {{ String(scope.row.status) === '0' ? '停售' : '启售' }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"updateTime\"\r\n                         label=\"最后操作时间\" />\r\n        <el-table-column label=\"操作\"\r\n                         width=\"250\"\r\n                         align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       size=\"small\"\r\n                       class=\"blueBug\"\r\n                       @click=\"addDishtype(scope.row.id)\">\r\n              修改\r\n            </el-button>\r\n            <el-button type=\"text\"\r\n                       size=\"small\"\r\n                       class=\"delBut\"\r\n                       @click=\"deleteHandle('单删', scope.row.id)\">\r\n              删除\r\n            </el-button>\r\n            <el-button type=\"text\"\r\n                       size=\"small\"\r\n                       class=\"non\"\r\n                       :class=\"{\r\n                         blueBug: scope.row.status == '0',\r\n                         delBut: scope.row.status != '0'\r\n                       }\"\r\n                       @click=\"statusHandle(scope.row)\">\r\n              {{ scope.row.status == '0' ? '启售' : '停售' }}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <Empty v-else\r\n             :is-search=\"isSearch\" />\r\n      <el-pagination v-if=\"counts > 10\"\r\n                     class=\"pageList\"\r\n                     :page-sizes=\"[10, 20, 30, 40]\"\r\n                     :page-size=\"pageSize\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     :total=\"counts\"\r\n                     @size-change=\"handleSizeChange\"\r\n                     @current-change=\"handleCurrentChange\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue } from 'vue-property-decorator'\r\nimport HeadLable from '@/components/HeadLable/index.vue'\r\nimport {\r\n  getDishPage,\r\n  editDish,\r\n  deleteDish,\r\n  dishStatusByStatus,\r\n  dishCategoryList\r\n} from '@/api/dish'\r\nimport InputAutoComplete from '@/components/InputAutoComplete/index.vue'\r\nimport Empty from '@/components/Empty/index.vue'\r\nimport { baseUrl } from '@/config.json'\r\n\r\n@Component({\r\n  name: 'DishType',\r\n  components: {\r\n    HeadLable,\r\n    InputAutoComplete,\r\n    Empty\r\n  }\r\n})\r\nexport default class extends Vue {\r\n  private input: any = ''\r\n  private counts: number = 0\r\n  private page: number = 1\r\n  private pageSize: number = 10\r\n  private checkList: string[] = []\r\n  private tableData: [] = []\r\n  private dishState = ''\r\n  private dishCategoryList = []\r\n  private categoryId = ''\r\n  private dishStatus = ''\r\n  private isSearch: boolean = false\r\n  private saleStatus: any = [\r\n    {\r\n      value: 0,\r\n      label: '停售'\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '启售'\r\n    }\r\n  ]\r\n\r\n  created() {\r\n    this.init()\r\n    this.getDishCategoryList()\r\n  }\r\n\r\n  initProp(val) {\r\n    this.input = val\r\n    this.initFun()\r\n  }\r\n\r\n  initFun() {\r\n    this.page = 1\r\n    this.init()\r\n  }\r\n\r\n  private async init(isSearch?) {\r\n    this.isSearch = isSearch\r\n    await getDishPage({\r\n      page: this.page,\r\n      pageSize: this.pageSize,\r\n      name: this.input || undefined,\r\n      categoryId: this.categoryId || undefined,\r\n      status: this.dishStatus\r\n    })\r\n      .then(res => {\r\n        if (res.data.code === 1) {\r\n          this.tableData = res.data && res.data.data && res.data.data.records\r\n          this.counts = Number(res.data.data.total)\r\n        }\r\n      })\r\n      .catch(err => {\r\n        this.$message.error('请求出错了：' + err.message)\r\n      })\r\n  }\r\n\r\n  // 添加\r\n  private addDishtype(st: string) {\r\n    if (st === 'add') {\r\n      this.$router.push({ path: '/dish/add' })\r\n    } else {\r\n      this.$router.push({ path: '/dish/add', query: { id: st } })\r\n    }\r\n  }\r\n\r\n  // 删除\r\n  private deleteHandle(type: string, id: any) {\r\n    if (type === '批量' && id === null) {\r\n      if (this.checkList.length === 0) {\r\n        return this.$message.error('请选择删除对象')\r\n      }\r\n    }\r\n    this.$confirm('确认删除该菜品, 是否继续?', '确定删除', {\r\n      confirmButtonText: '删除',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => {\r\n      deleteDish(type === '批量' ? this.checkList.join(',') : id)\r\n        .then(res => {\r\n          if (res && res.data && res.data.code === 1) {\r\n            this.$message.success('删除成功！')\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.data.msg)\r\n          }\r\n        })\r\n        .catch(err => {\r\n          this.$message.error('请求出错了：' + err.message)\r\n        })\r\n    })\r\n  }\r\n  //获取菜品分类下拉数据\r\n  private getDishCategoryList() {\r\n    dishCategoryList({\r\n      type: 1\r\n    })\r\n      .then(res => {\r\n        if (res && res.data && res.data.code === 1) {\r\n          this.dishCategoryList = (\r\n            res.data &&\r\n            res.data.data &&\r\n            res.data.data\r\n          ).map(item => {\r\n            return { value: item.id, label: item.name }\r\n          })\r\n        }\r\n      })\r\n      .catch(() => {})\r\n  }\r\n\r\n  //状态更改\r\n  private statusHandle(row: any) {\r\n    let params: any = {}\r\n    if (typeof row === 'string') {\r\n      if (this.checkList.length === 0) {\r\n        this.$message.error('批量操作，请先勾选操作菜品！')\r\n        return false\r\n      }\r\n      params.id = this.checkList.join(',')\r\n      params.status = row\r\n    } else {\r\n      params.id = row.id\r\n      params.status = row.status ? '0' : '1'\r\n    }\r\n    this.dishState = params\r\n    this.$confirm('确认更改该菜品状态?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => {\r\n      // 起售停售---批量起售停售接口\r\n      dishStatusByStatus(this.dishState)\r\n        .then(res => {\r\n          if (res && res.data && res.data.code === 1) {\r\n            this.$message.success('菜品状态已经更改成功！')\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.data.msg)\r\n          }\r\n        })\r\n        .catch(err => {\r\n          this.$message.error('请求出错了：' + err.message)\r\n        })\r\n    })\r\n  }\r\n\r\n  // 全部操作\r\n  private handleSelectionChange(val: any) {\r\n    let checkArr: any[] = []\r\n    val.forEach((n: any) => {\r\n      checkArr.push(n.id)\r\n    })\r\n    this.checkList = checkArr\r\n  }\r\n\r\n  private handleSizeChange(val: any) {\r\n    this.pageSize = val\r\n    this.init()\r\n  }\r\n\r\n  private handleCurrentChange(val: any) {\r\n    this.page = val\r\n    this.init()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.el-table-column--selection .cell {\r\n  padding-left: 10px;\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.dashboard {\r\n  &-container {\r\n    margin: 30px;\r\n    .container {\r\n      background: #fff;\r\n      position: relative;\r\n      z-index: 1;\r\n      padding: 30px 28px;\r\n      border-radius: 4px;\r\n      //查询黑色按钮样式\r\n      .normal-btn {\r\n        background: #333333;\r\n        color: white;\r\n        margin-left: 20px;\r\n      }\r\n      .tableBar {\r\n        margin-bottom: 20px;\r\n\r\n        .tableLab {\r\n          display: inline-block;\r\n          float: right;\r\n          span {\r\n            cursor: pointer;\r\n            display: inline-block;\r\n            font-size: 14px;\r\n            padding: 0 20px;\r\n            color: $gray-2;\r\n          }\r\n        }\r\n      }\r\n      .tableBox {\r\n        width: 100%;\r\n        border: 1px solid $gray-5;\r\n        border-bottom: 0;\r\n      }\r\n      .pageList {\r\n        text-align: center;\r\n        margin-top: 30px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}