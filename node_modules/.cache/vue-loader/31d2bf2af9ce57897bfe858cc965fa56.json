{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--12-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/babel.config.js", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staticRenderFns = exports.render = void 0;\nvar render = exports.render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", [!_vm.item.meta || !_vm.item.meta.hidden ? _c(\"div\", {\n    class: [\"menu-wrapper\", \"full-mode\", {\n      \"first-level\": _vm.isFirstLevel\n    }]\n  }, [_vm.theOnlyOneChild && !_vm.theOnlyOneChild.children ? [_vm.theOnlyOneChild.meta ? _c(\"sidebar-item-link\", {\n    attrs: {\n      to: _vm.resolvePath(_vm.theOnlyOneChild.path)\n    }\n  }, [_c(\"el-menu-item\", {\n    class: {\n      \"submenu-title-noDropdown\": _vm.isFirstLevel\n    },\n    attrs: {\n      index: _vm.resolvePath(_vm.theOnlyOneChild.path)\n    }\n  }, [_vm.theOnlyOneChild.meta.icon ? _c(\"i\", {\n    staticClass: \"iconfont\",\n    class: _vm.theOnlyOneChild.meta.icon\n  }) : _vm._e(), _vm.theOnlyOneChild.meta.title ? _c(\"span\", {\n    attrs: {\n      slot: \"title\"\n    },\n    slot: \"title\"\n  }, [_vm._v(_vm._s(_vm.theOnlyOneChild.meta.title))]) : _vm._e()])], 1) : _vm._e()] : _c(\"el-submenu\", {\n    attrs: {\n      index: _vm.resolvePath(_vm.item.path),\n      \"popper-append-to-body\": \"\"\n    }\n  }, [_c(\"template\", {\n    slot: \"title\"\n  }, [_vm.item.meta && _vm.item.meta.icon ? _c(\"i\", {\n    staticClass: \"iconfont\",\n    class: _vm.item.meta.icon\n  }) : _vm._e(), _vm.item.meta && _vm.item.meta.title ? _c(\"span\", {\n    attrs: {\n      slot: \"title\"\n    },\n    slot: \"title\"\n  }, [_vm._v(_vm._s(_vm.item.meta.title))]) : _vm._e()]), _vm.item.children ? _vm._l(_vm.item.children, function (child) {\n    return _c(\"sidebar-item\", {\n      key: child.path,\n      staticClass: \"nest-menu\",\n      attrs: {\n        item: child,\n        \"is-collapse\": _vm.isCollapse,\n        \"is-first-level\": false,\n        \"base-path\": _vm.resolvePath(child.path)\n      }\n    });\n  }) : _vm._e()], 2)], 2) : _vm._e()]);\n};\nvar staticRenderFns = exports.staticRenderFns = [];\nrender._withStripped = true;", {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "_setup", "_setupProxy", "item", "meta", "hidden", "class", "isFirstLevel", "theOnlyOneChild", "children", "attrs", "to", "<PERSON><PERSON><PERSON>", "path", "index", "icon", "staticClass", "_e", "title", "slot", "_v", "_s", "_l", "child", "key", "isCollapse", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\"div\", [\n    !_vm.item.meta || !_vm.item.meta.hidden\n      ? _c(\n          \"div\",\n          {\n            class: [\n              \"menu-wrapper\",\n              \"full-mode\",\n              { \"first-level\": _vm.isFirstLevel },\n            ],\n          },\n          [\n            _vm.theOnlyOneChild && !_vm.theOnlyOneChild.children\n              ? [\n                  _vm.theOnlyOneChild.meta\n                    ? _c(\n                        \"sidebar-item-link\",\n                        {\n                          attrs: {\n                            to: _vm.resolvePath(_vm.theOnlyOneChild.path),\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-menu-item\",\n                            {\n                              class: {\n                                \"submenu-title-noDropdown\": _vm.isFirstLevel,\n                              },\n                              attrs: {\n                                index: _vm.resolvePath(\n                                  _vm.theOnlyOneChild.path\n                                ),\n                              },\n                            },\n                            [\n                              _vm.theOnlyOneChild.meta.icon\n                                ? _c(\"i\", {\n                                    staticClass: \"iconfont\",\n                                    class: _vm.theOnlyOneChild.meta.icon,\n                                  })\n                                : _vm._e(),\n                              _vm.theOnlyOneChild.meta.title\n                                ? _c(\n                                    \"span\",\n                                    { attrs: { slot: \"title\" }, slot: \"title\" },\n                                    [\n                                      _vm._v(\n                                        _vm._s(_vm.theOnlyOneChild.meta.title)\n                                      ),\n                                    ]\n                                  )\n                                : _vm._e(),\n                            ]\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                ]\n              : _c(\n                  \"el-submenu\",\n                  {\n                    attrs: {\n                      index: _vm.resolvePath(_vm.item.path),\n                      \"popper-append-to-body\": \"\",\n                    },\n                  },\n                  [\n                    _c(\"template\", { slot: \"title\" }, [\n                      _vm.item.meta && _vm.item.meta.icon\n                        ? _c(\"i\", {\n                            staticClass: \"iconfont\",\n                            class: _vm.item.meta.icon,\n                          })\n                        : _vm._e(),\n                      _vm.item.meta && _vm.item.meta.title\n                        ? _c(\n                            \"span\",\n                            { attrs: { slot: \"title\" }, slot: \"title\" },\n                            [_vm._v(_vm._s(_vm.item.meta.title))]\n                          )\n                        : _vm._e(),\n                    ]),\n                    _vm.item.children\n                      ? _vm._l(_vm.item.children, function (child) {\n                          return _c(\"sidebar-item\", {\n                            key: child.path,\n                            staticClass: \"nest-menu\",\n                            attrs: {\n                              item: child,\n                              \"is-collapse\": _vm.isCollapse,\n                              \"is-first-level\": false,\n                              \"base-path\": _vm.resolvePath(child.path),\n                            },\n                          })\n                        })\n                      : _vm._e(),\n                  ],\n                  2\n                ),\n          ],\n          2\n        )\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CAAC,KAAK,EAAE,CACf,CAACD,GAAG,CAACK,IAAI,CAACC,IAAI,IAAI,CAACN,GAAG,CAACK,IAAI,CAACC,IAAI,CAACC,MAAM,GACnCN,EAAE,CACA,KAAK,EACL;IACEO,KAAK,EAAE,CACL,cAAc,EACd,WAAW,EACX;MAAE,aAAa,EAAER,GAAG,CAACS;IAAa,CAAC;EAEvC,CAAC,EACD,CACET,GAAG,CAACU,eAAe,IAAI,CAACV,GAAG,CAACU,eAAe,CAACC,QAAQ,GAChD,CACEX,GAAG,CAACU,eAAe,CAACJ,IAAI,GACpBL,EAAE,CACA,mBAAmB,EACnB;IACEW,KAAK,EAAE;MACLC,EAAE,EAAEb,GAAG,CAACc,WAAW,CAACd,GAAG,CAACU,eAAe,CAACK,IAAI;IAC9C;EACF,CAAC,EACD,CACEd,EAAE,CACA,cAAc,EACd;IACEO,KAAK,EAAE;MACL,0BAA0B,EAAER,GAAG,CAACS;IAClC,CAAC;IACDG,KAAK,EAAE;MACLI,KAAK,EAAEhB,GAAG,CAACc,WAAW,CACpBd,GAAG,CAACU,eAAe,CAACK,IACtB;IACF;EACF,CAAC,EACD,CACEf,GAAG,CAACU,eAAe,CAACJ,IAAI,CAACW,IAAI,GACzBhB,EAAE,CAAC,GAAG,EAAE;IACNiB,WAAW,EAAE,UAAU;IACvBV,KAAK,EAAER,GAAG,CAACU,eAAe,CAACJ,IAAI,CAACW;EAClC,CAAC,CAAC,GACFjB,GAAG,CAACmB,EAAE,CAAC,CAAC,EACZnB,GAAG,CAACU,eAAe,CAACJ,IAAI,CAACc,KAAK,GAC1BnB,EAAE,CACA,MAAM,EACN;IAAEW,KAAK,EAAE;MAAES,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EAC3C,CACErB,GAAG,CAACsB,EAAE,CACJtB,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACU,eAAe,CAACJ,IAAI,CAACc,KAAK,CACvC,CAAC,CAEL,CAAC,GACDpB,GAAG,CAACmB,EAAE,CAAC,CAAC,CAEhB,CAAC,CACF,EACD,CACF,CAAC,GACDnB,GAAG,CAACmB,EAAE,CAAC,CAAC,CACb,GACDlB,EAAE,CACA,YAAY,EACZ;IACEW,KAAK,EAAE;MACLI,KAAK,EAAEhB,GAAG,CAACc,WAAW,CAACd,GAAG,CAACK,IAAI,CAACU,IAAI,CAAC;MACrC,uBAAuB,EAAE;IAC3B;EACF,CAAC,EACD,CACEd,EAAE,CAAC,UAAU,EAAE;IAAEoB,IAAI,EAAE;EAAQ,CAAC,EAAE,CAChCrB,GAAG,CAACK,IAAI,CAACC,IAAI,IAAIN,GAAG,CAACK,IAAI,CAACC,IAAI,CAACW,IAAI,GAC/BhB,EAAE,CAAC,GAAG,EAAE;IACNiB,WAAW,EAAE,UAAU;IACvBV,KAAK,EAAER,GAAG,CAACK,IAAI,CAACC,IAAI,CAACW;EACvB,CAAC,CAAC,GACFjB,GAAG,CAACmB,EAAE,CAAC,CAAC,EACZnB,GAAG,CAACK,IAAI,CAACC,IAAI,IAAIN,GAAG,CAACK,IAAI,CAACC,IAAI,CAACc,KAAK,GAChCnB,EAAE,CACA,MAAM,EACN;IAAEW,KAAK,EAAE;MAAES,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EAC3C,CAACrB,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACK,IAAI,CAACC,IAAI,CAACc,KAAK,CAAC,CAAC,CACtC,CAAC,GACDpB,GAAG,CAACmB,EAAE,CAAC,CAAC,CACb,CAAC,EACFnB,GAAG,CAACK,IAAI,CAACM,QAAQ,GACbX,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACK,IAAI,CAACM,QAAQ,EAAE,UAAUc,KAAK,EAAE;IACzC,OAAOxB,EAAE,CAAC,cAAc,EAAE;MACxByB,GAAG,EAAED,KAAK,CAACV,IAAI;MACfG,WAAW,EAAE,WAAW;MACxBN,KAAK,EAAE;QACLP,IAAI,EAAEoB,KAAK;QACX,aAAa,EAAEzB,GAAG,CAAC2B,UAAU;QAC7B,gBAAgB,EAAE,KAAK;QACvB,WAAW,EAAE3B,GAAG,CAACc,WAAW,CAACW,KAAK,CAACV,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,GACFf,GAAG,CAACmB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACN,EACD,CACF,CAAC,GACDnB,GAAG,CAACmB,EAAE,CAAC,CAAC,CACb,CAAC;AACJ,CAAC;AACD,IAAIS,eAAe,GAAA7B,OAAA,CAAA6B,eAAA,GAAG,EAAE;AACxB9B,MAAM,CAAC+B,aAAa,GAAG,IAAI", "ignoreList": []}]}