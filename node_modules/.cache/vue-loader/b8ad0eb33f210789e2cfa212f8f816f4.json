{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/layout/components/Sidebar/SidebarItem.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div>\n  <!-- <div\n    v-if=\" !item.meta || !item.meta.hidden \"\n    :class=\"['menu-wrapper', isCollapse ? 'simple-mode' : 'full-mode', {'first-level': isFirstLevel}]\"\n  > -->\n  <div\n    v-if=\"!item.meta || !item.meta.hidden\"\n    :class=\"['menu-wrapper', 'full-mode', { 'first-level': isFirstLevel }]\"\n  >\n    <template v-if=\"theOnlyOneChild && !theOnlyOneChild.children\">\n      <sidebar-item-link\n        v-if=\"theOnlyOneChild.meta\"\n        :to=\"resolvePath(theOnlyOneChild.path)\"\n      >\n        <el-menu-item\n          :index=\"resolvePath(theOnlyOneChild.path)\"\n          :class=\"{ 'submenu-title-noDropdown': isFirstLevel }\"\n        >\n          <!-- <i v-if=\"theOnlyOneChild.meta.title==='工作台'\" class=\"iconfont icon img-icon-sel\" /> -->\n          <!-- <svg-icon v-if=\"theOnlyOneChild.meta.title==='工作台'\" name=\"dashboard\" width=\"20\" height=\"20\"></svg-icon> -->\n          <i\n            v-if=\"theOnlyOneChild.meta.icon\"\n            class=\"iconfont\"\n            :class=\"theOnlyOneChild.meta.icon\"\n          />\n          <span v-if=\"theOnlyOneChild.meta.title\" slot=\"title\">{{\n            theOnlyOneChild.meta.title\n          }}</span>\n        </el-menu-item>\n      </sidebar-item-link>\n    </template>\n    <el-submenu v-else :index=\"resolvePath(item.path)\" popper-append-to-body>\n      <template slot=\"title\">\n        <i\n          v-if=\"item.meta && item.meta.icon\"\n          class=\"iconfont\"\n          :class=\"item.meta.icon\"\n        />\n        <span v-if=\"item.meta && item.meta.title\" slot=\"title\">{{\n          item.meta.title\n        }}</span>\n      </template>\n      <template v-if=\"item.children\">\n        <sidebar-item\n          v-for=\"child in item.children\"\n          :key=\"child.path\"\n          :item=\"child\"\n          :is-collapse=\"isCollapse\"\n          :is-first-level=\"false\"\n          :base-path=\"resolvePath(child.path)\"\n          class=\"nest-menu\"\n        />\n      </template>\n    </el-submenu>\n  </div>\n</div>\n", null]}