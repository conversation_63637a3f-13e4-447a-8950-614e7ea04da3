{"remainingRequest": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/404.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/src/views/404.vue", "mtime": 1652756116000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-loader/lib/index.js", "mtime": 456789000000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/ts-loader/index.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cache-loader/dist/cjs.js", "mtime": 499162500000}, {"path": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/vue-loader/lib/index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\nimport { Component, Vue } from 'vue-property-decorator';\r\n\r\n@Component({\r\n    'name': 'Page404'\r\n})\r\nexport default class extends Vue {\r\n  private message = '404 Page Not Found'\r\n}\r\n", {"version": 3, "sources": ["404.vue"], "names": [], "mappings": ";AAqDA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "404.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"wscn-http404-container\">\r\n    <div class=\"wscn-http404\">\r\n      <div class=\"pic-404\">\r\n        <img\r\n          class=\"pic-404__parent\"\r\n          src=\"@/assets/404-images/404.png\"\r\n          alt=\"404\"\r\n        >\r\n        <img\r\n          class=\"pic-404__child left\"\r\n          src=\"@/assets/404-images/404-cloud.png\"\r\n          alt=\"404\"\r\n        >\r\n        <img\r\n          class=\"pic-404__child mid\"\r\n          src=\"@/assets/404-images/404-cloud.png\"\r\n          alt=\"404\"\r\n        >\r\n        <img\r\n          class=\"pic-404__child right\"\r\n          src=\"@/assets/404-images/404-cloud.png\"\r\n          alt=\"404\"\r\n        >\r\n      </div>\r\n      <div class=\"text-404\">\r\n        <div class=\"text-404__oops\">\r\n          OOPS!\r\n        </div>\r\n        <div class=\"text-404__info\">\r\n          All rights reserved\r\n          <a\r\n            style=\"color:#20a0ff\"\r\n            href=\"https://wallstreetcn.com\"\r\n            target=\"_blank\"\r\n          >wallstreetcn</a>\r\n        </div>\r\n        <div class=\"text-404__headline\">\r\n          {{ message }}\r\n        </div>\r\n        <div class=\"text-404__info\">\r\n          Please check that the URL you entered is correct, or click the button below to return to the homepage.\r\n        </div>\r\n        <a\r\n          href=\"\"\r\n          class=\"text-404__return-home\"\r\n        >Back to home</a>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { Component, Vue } from 'vue-property-decorator';\r\n\r\n@Component({\r\n    'name': 'Page404'\r\n})\r\nexport default class extends Vue {\r\n  private message = '404 Page Not Found'\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wscn-http404-container {\r\n  transform: translate(-50%,-50%);\r\n  position: absolute;\r\n  top: 40%;\r\n  left: 50%;\r\n}\r\n\r\n.wscn-http404 {\r\n  position: relative;\r\n  width: 1200px;\r\n  padding: 0 50px;\r\n  overflow: hidden;\r\n\r\n  .pic-404 {\r\n    position: relative;\r\n    float: left;\r\n    width: 600px;\r\n    overflow: hidden;\r\n\r\n    &__parent {\r\n      width: 100%;\r\n    }\r\n\r\n    &__child {\r\n      position: absolute;\r\n\r\n      &.left {\r\n        width: 80px;\r\n        top: 17px;\r\n        left: 220px;\r\n        opacity: 0;\r\n        animation-name: cloudLeft;\r\n        animation-duration: 2s;\r\n        animation-timing-function: linear;\r\n        animation-fill-mode: forwards;\r\n        animation-delay: 1s;\r\n      }\r\n\r\n      &.mid {\r\n        width: 46px;\r\n        top: 10px;\r\n        left: 420px;\r\n        opacity: 0;\r\n        animation-name: cloudMid;\r\n        animation-duration: 2s;\r\n        animation-timing-function: linear;\r\n        animation-fill-mode: forwards;\r\n        animation-delay: 1.2s;\r\n      }\r\n\r\n      &.right {\r\n        width: 62px;\r\n        top: 100px;\r\n        left: 500px;\r\n        opacity: 0;\r\n        animation-name: cloudRight;\r\n        animation-duration: 2s;\r\n        animation-timing-function: linear;\r\n        animation-fill-mode: forwards;\r\n        animation-delay: 1s;\r\n      }\r\n\r\n      @keyframes cloudLeft {\r\n        0% {\r\n          top: 17px;\r\n          left: 220px;\r\n          opacity: 0;\r\n        }\r\n\r\n        20% {\r\n          top: 33px;\r\n          left: 188px;\r\n          opacity: 1;\r\n        }\r\n\r\n        80% {\r\n          top: 81px;\r\n          left: 92px;\r\n          opacity: 1;\r\n        }\r\n\r\n        100% {\r\n          top: 97px;\r\n          left: 60px;\r\n          opacity: 0;\r\n        }\r\n      }\r\n\r\n      @keyframes cloudMid {\r\n        0% {\r\n          top: 10px;\r\n          left: 420px;\r\n          opacity: 0;\r\n        }\r\n\r\n        20% {\r\n          top: 40px;\r\n          left: 360px;\r\n          opacity: 1;\r\n        }\r\n\r\n        70% {\r\n          top: 130px;\r\n          left: 180px;\r\n          opacity: 1;\r\n        }\r\n\r\n        100% {\r\n          top: 160px;\r\n          left: 120px;\r\n          opacity: 0;\r\n        }\r\n      }\r\n\r\n      @keyframes cloudRight {\r\n        0% {\r\n          top: 100px;\r\n          left: 500px;\r\n          opacity: 0;\r\n        }\r\n\r\n        20% {\r\n          top: 120px;\r\n          left: 460px;\r\n          opacity: 1;\r\n        }\r\n\r\n        80% {\r\n          top: 180px;\r\n          left: 340px;\r\n          opacity: 1;\r\n        }\r\n\r\n        100% {\r\n          top: 200px;\r\n          left: 300px;\r\n          opacity: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .text-404 {\r\n    position: relative;\r\n    float: left;\r\n    width: 300px;\r\n    padding: 30px 0;\r\n    overflow: hidden;\r\n\r\n    &__oops {\r\n      font-size: 32px;\r\n      font-weight: bold;\r\n      line-height: 40px;\r\n      color: #1482f0;\r\n      opacity: 0;\r\n      margin-bottom: 20px;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n\r\n    &__headline {\r\n      font-size: 20px;\r\n      line-height: 24px;\r\n      color: #222;\r\n      font-weight: bold;\r\n      opacity: 0;\r\n      margin-bottom: 10px;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-delay: 0.1s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n\r\n    &__info {\r\n      font-size: 13px;\r\n      line-height: 21px;\r\n      color: grey;\r\n      opacity: 0;\r\n      margin-bottom: 30px;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-delay: 0.2s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n\r\n    &__return-home {\r\n      display: block;\r\n      float: left;\r\n      width: 110px;\r\n      height: 36px;\r\n      background: #1482f0;\r\n      border-radius: 100px;\r\n      text-align: center;\r\n      color: #ffffff;\r\n      opacity: 0;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-delay: 0.3s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n\r\n    @keyframes slideUp {\r\n      0% {\r\n        transform: translateY(60px);\r\n        opacity: 0;\r\n      }\r\n\r\n      100% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}