{"_from": "@cypress/listr-verbose-renderer@0.4.1", "_id": "@cypress/listr-verbose-renderer@0.4.1", "_inBundle": false, "_integrity": "sha512-EDiBsVPWC27DDLEJCo+dpl9ODHhdrwU57ccr9tspwCdG2ni0QVkf6LF0FGbhfujcjPxnXLIwsaks4sOrwrA4Qw==", "_location": "/@cypress/listr-verbose-renderer", "_phantomChildren": {"escape-string-regexp": "1.0.5", "exit-hook": "1.1.1", "has-ansi": "2.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "@cypress/listr-verbose-renderer@0.4.1", "name": "@cypress/listr-verbose-renderer", "escapedName": "@cypress%2flistr-verbose-renderer", "scope": "@cypress", "rawSpec": "0.4.1", "saveSpec": null, "fetchSpec": "0.4.1"}, "_requiredBy": ["/cypress"], "_resolved": "https://registry.npmmirror.com/@cypress/listr-verbose-renderer/-/listr-verbose-renderer-0.4.1.tgz", "_shasum": "a77492f4b11dcc7c446a34b3e28721afd33c642a", "_spec": "@cypress/listr-verbose-renderer@0.4.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cypress", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "bugs": {"url": "https://github.com/SamVerschueren/listr-verbose-renderer/issues"}, "bundleDependencies": false, "dependencies": {"chalk": "^1.1.3", "cli-cursor": "^1.0.2", "date-fns": "^1.27.2", "figures": "^1.7.0"}, "deprecated": false, "description": "Listr verbose renderer", "devDependencies": {"@cypress/releaser": "^0.2.1", "ava": "*", "hook-std": "^0.3.0", "listr": "*", "strip-ansi": "^3.0.1", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "lib"], "homepage": "https://github.com/SamVerschueren/listr-verbose-renderer#readme", "keywords": ["listr", "verbose", "renderer", "rendering", "custom", "format"], "license": "MIT", "name": "@cypress/listr-verbose-renderer", "repository": {"type": "git", "url": "git+https://github.com/SamVerschueren/listr-verbose-renderer.git"}, "scripts": {"release": "releaser", "test": "xo && ava"}, "version": "0.4.1", "xo": {"esnext": true}}