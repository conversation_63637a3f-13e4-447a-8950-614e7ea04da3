{"_from": "onetime@^1.0.0", "_id": "onetime@1.1.0", "_inBundle": false, "_integrity": "sha512-GZ+g4jayMqzCRMgB2sol7GiCLjKfS1PINkjmx8spcKce1LiVqcbQreXwqs2YAFXC6R03VIG28ZS31t8M866v6A==", "_location": "/@cypress/listr-verbose-renderer/onetime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "onetime@^1.0.0", "name": "onetime", "escapedName": "onetime", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/@cypress/listr-verbose-renderer/restore-cursor"], "_resolved": "https://registry.npmmirror.com/onetime/-/onetime-1.1.0.tgz", "_shasum": "a1f7838f8314c516f05ecefcbc4ccfe04b4ed789", "_spec": "onetime@^1.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@cypress/listr-verbose-renderer/node_modules/restore-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Only call a function once", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/onetime#readme", "keywords": ["once", "one", "single", "call", "function", "prevent"], "license": "MIT", "name": "onetime", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0"}