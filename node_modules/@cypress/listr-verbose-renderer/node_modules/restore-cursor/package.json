{"_from": "restore-cursor@^1.0.1", "_id": "restore-cursor@1.0.1", "_inBundle": false, "_integrity": "sha512-reSjH4HuiFlxlaBaFCiS6O76ZGG2ygKoSlCsipKdaZuKSPx/+bt9mULkn4l0asVzbEfQQmXRg6Wp6gv6m0wElw==", "_location": "/@cypress/listr-verbose-renderer/restore-cursor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "restore-cursor@^1.0.1", "name": "restore-cursor", "escapedName": "restore-cursor", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/@cypress/listr-verbose-renderer/cli-cursor"], "_resolved": "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-1.0.1.tgz", "_shasum": "34661f46886327fed2991479152252df92daa541", "_spec": "restore-cursor@^1.0.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@cypress/listr-verbose-renderer/node_modules/cli-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "bundleDependencies": false, "dependencies": {"exit-hook": "^1.0.0", "onetime": "^1.0.0"}, "deprecated": false, "description": "Gracefully restore the CLI cursor on exit", "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "name": "restore-cursor", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/restore-cursor.git"}, "version": "1.0.1"}