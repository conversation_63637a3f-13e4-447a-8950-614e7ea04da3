{"_from": "cli-cursor@^1.0.2", "_id": "cli-cursor@1.0.2", "_inBundle": false, "_integrity": "sha512-25tABq090YNKkF6JH7lcwO0zFJTRke4Jcq9iX2nr/Sz0Cjjv4gckmwlW6Ty/aoyFd6z3ysR2hMGC2GFugmBo6A==", "_location": "/@cypress/listr-verbose-renderer/cli-cursor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cli-cursor@^1.0.2", "name": "cli-cursor", "escapedName": "cli-cursor", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/@cypress/listr-verbose-renderer"], "_resolved": "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-1.0.2.tgz", "_shasum": "64da3f7d56a54412e59794bd62dc35295e8f2987", "_spec": "cli-cursor@^1.0.2", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@cypress/listr-verbose-renderer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "bundleDependencies": false, "dependencies": {"restore-cursor": "^1.0.1"}, "deprecated": false, "description": "Toggle the CLI cursor", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "name": "cli-cursor", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/cli-cursor.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}