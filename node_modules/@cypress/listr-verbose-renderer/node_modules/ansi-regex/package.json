{"_from": "ansi-regex@^2.0.0", "_id": "ansi-regex@2.1.1", "_inBundle": false, "_integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==", "_location": "/@cypress/listr-verbose-renderer/ansi-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-regex@^2.0.0", "name": "ansi-regex", "escapedName": "ansi-regex", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/@cypress/listr-verbose-renderer/strip-ansi"], "_resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-2.1.1.tgz", "_shasum": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "_spec": "ansi-regex@^2.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@cypress/listr-verbose-renderer/node_modules/strip-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching ANSI escape codes", "devDependencies": {"ava": "0.17.0", "xo": "0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/ansi-regex#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "name": "ansi-regex", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "scripts": {"test": "xo && ava --verbose", "view-supported": "node fixtures/view-codes.js"}, "version": "2.1.1", "xo": {"rules": {"guard-for-in": 0, "no-loop-func": 0}}}