{"_from": "strip-ansi@^3.0.0", "_id": "strip-ansi@3.0.1", "_inBundle": false, "_integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "_location": "/@cypress/listr-verbose-renderer/strip-ansi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-ansi@^3.0.0", "name": "strip-ansi", "escapedName": "strip-ansi", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/@cypress/listr-verbose-renderer/chalk"], "_resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-3.0.1.tgz", "_shasum": "6a385fb8853d952d5ff05d0e8aaf94278dc63dcf", "_spec": "strip-ansi@^3.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@cypress/listr-verbose-renderer/node_modules/chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-regex": "^2.0.0"}, "deprecated": false, "description": "Strip ANSI escape codes", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbna.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "name": "strip-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.1"}