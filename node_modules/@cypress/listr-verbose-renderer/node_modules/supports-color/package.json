{"_from": "supports-color@^2.0.0", "_id": "supports-color@2.0.0", "_inBundle": false, "_integrity": "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==", "_location": "/@cypress/listr-verbose-renderer/supports-color", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "supports-color@^2.0.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/@cypress/listr-verbose-renderer/chalk"], "_resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-2.0.0.tgz", "_shasum": "535d045ce6b6363fa40117084629995e9df324c7", "_spec": "supports-color@^2.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@cypress/listr-verbose-renderer/node_modules/chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Detect whether a terminal supports color", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}, "engines": {"node": ">=0.8.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}], "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "mocha"}, "version": "2.0.0"}