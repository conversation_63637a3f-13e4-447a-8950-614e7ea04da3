{"_from": "@cypress/xvfb@1.2.4", "_id": "@cypress/xvfb@1.2.4", "_inBundle": false, "_integrity": "sha512-skbBzPggOVYCbnGgV+0dmBdW/s77ZkAOXIC1knS8NagwDjBrNC1LuXtQJeiN6l+m7lzmHtaoUw/ctJKdqkG57Q==", "_location": "/@cypress/xvfb", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@cypress/xvfb@1.2.4", "name": "@cypress/xvfb", "escapedName": "@cypress%2fxvfb", "scope": "@cypress", "rawSpec": "1.2.4", "saveSpec": null, "fetchSpec": "1.2.4"}, "_requiredBy": ["/cypress"], "_resolved": "https://registry.npmmirror.com/@cypress/xvfb/-/xvfb-1.2.4.tgz", "_shasum": "2daf42e8275b39f4aa53c14214e557bd14e7748a", "_spec": "@cypress/xvfb@1.2.4", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/cypress", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl"}, "bugs": {"url": "https://github.com/cypress-io/xvfb/issues"}, "bundleDependencies": false, "contributors": [{"name": "ProxV, Inc.", "email": "<EMAIL>", "url": "http://proxv.com"}], "dependencies": {"debug": "^3.1.0", "lodash.once": "^4.1.1"}, "deprecated": false, "description": "Easily start and stop an X Virtual Frame Buffer from your node apps.", "devDependencies": {"bluebird": "^3.5.1", "chai": "^4.1.2", "eslint": "^4.13.1", "eslint-plugin-cypress-dev": "^1.1.2", "eslint-plugin-mocha": "^4.11.0", "husky": "1.0.1", "mocha": "^3.5.0", "npm-utils": "^2.0.0", "semantic-release": "15.9.16"}, "files": ["index.js"], "homepage": "https://github.com/cypress-io/xvfb#readme", "husky": {"hooks": {"pre-commit": "npm test", "pre-push": "npm run size"}}, "license": "MIT", "main": "index.js", "name": "@cypress/xvfb", "private": false, "publishConfig": {"registry": "http://registry.npmjs.org/", "access": "public"}, "release": {"analyzeCommits": {"preset": "angular", "releaseRules": [{"type": "break", "release": "major"}]}}, "repository": {"type": "git", "url": "git+https://github.com/cypress-io/xvfb.git"}, "scripts": {"commit": "commit-wizard", "demo": "node ./demo", "semantic-release": "semantic-release", "size": "t=\"$(npm pack .)\"; wc -c \"${t}\"; tar tvf \"${t}\"; rm \"${t}\";", "test": "eslint **/*.js && mocha", "test-watch": "mocha watch"}, "version": "1.2.4"}