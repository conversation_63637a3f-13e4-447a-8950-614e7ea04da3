{"_from": "@babel/plugin-transform-block-scoping@^7.3.4", "_id": "@babel/plugin-transform-block-scoping@7.28.0", "_inBundle": false, "_integrity": "sha512-gKKnwjpdx5sER/wl0WN0efUBFzF/56YZO0RJrSYP4CljXnP31ByY7fol89AzomdlLNzI36AvOTmYHsnZTCkq8Q==", "_location": "/@babel/plugin-transform-block-scoping", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-block-scoping@^7.3.4", "name": "@babel/plugin-transform-block-scoping", "escapedName": "@babel%2fplugin-transform-block-scoping", "scope": "@babel", "rawSpec": "^7.3.4", "saveSpec": null, "fetchSpec": "^7.3.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.28.0.tgz", "_shasum": "e7c50cbacc18034f210b93defa89638666099451", "_spec": "@babel/plugin-transform-block-scoping@^7.3.4", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Compile ES2015 block scoping (const and let) to ES5", "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-block-scoping", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-block-scoping"}, "type": "commonjs", "version": "7.28.0"}