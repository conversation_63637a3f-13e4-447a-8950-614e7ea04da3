{"_from": "@babel/plugin-transform-regenerator@^7.3.4", "_id": "@babel/plugin-transform-regenerator@7.28.3", "_inBundle": false, "_integrity": "sha512-K3/M/a4+ESb5LEldjQb+XSrpY0nF+ZBFlTCbSnKaYAMfD8v33O6PMs4uYnOk19HlcsI8WMu3McdFPTiQHF/1/A==", "_location": "/@babel/plugin-transform-regenerator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-regenerator@^7.3.4", "name": "@babel/plugin-transform-regenerator", "escapedName": "@babel%2fplugin-transform-regenerator", "scope": "@babel", "rawSpec": "^7.3.4", "saveSpec": null, "fetchSpec": "^7.3.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.28.3.tgz", "_shasum": "b8eee0f8aed37704bbcc932fd0b1a0a34d0b7344", "_spec": "@babel/plugin-transform-regenerator@^7.3.4", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Explode async and generator functions into a state machine.", "devDependencies": {"@babel/core": "^7.28.3", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.28.0", "@babel/plugin-transform-classes": "^7.28.3", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/plugin-transform-runtime": "^7.28.3", "babel-plugin-polyfill-regenerator": "^0.6.5", "mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-regenerator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regenerator"}, "type": "commonjs", "version": "7.28.3"}