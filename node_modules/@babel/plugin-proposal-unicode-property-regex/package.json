{"_from": "@babel/plugin-proposal-unicode-property-regex@^7.2.0", "_id": "@babel/plugin-proposal-unicode-property-regex@7.18.6", "_inBundle": false, "_integrity": "sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==", "_location": "/@babel/plugin-proposal-unicode-property-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-proposal-unicode-property-regex@^7.2.0", "name": "@babel/plugin-proposal-unicode-property-regex", "escapedName": "@babel%2fplugin-proposal-unicode-property-regex", "scope": "@babel", "rawSpec": "^7.2.0", "saveSpec": null, "fetchSpec": "^7.2.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz", "_shasum": "af613d2cd5e643643b65cded64207b15c85cb78e", "_spec": "@babel/plugin-proposal-unicode-property-regex@^7.2.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-unicode-property-regex instead.", "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-unicode-property-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-unicode-property-regex", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-unicode-property-regex"}, "type": "commonjs", "version": "7.18.6"}