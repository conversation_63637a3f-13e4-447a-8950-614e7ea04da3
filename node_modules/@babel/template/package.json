{"_from": "@babel/template@^7.27.2", "_id": "@babel/template@7.27.2", "_inBundle": false, "_integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "_location": "/@babel/template", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/template@^7.27.2", "name": "@babel/template", "escapedName": "@babel%2ftemplate", "scope": "@babel", "rawSpec": "^7.27.2", "saveSpec": null, "fetchSpec": "^7.27.2"}, "_requiredBy": ["/@babel/core", "/@babel/helper-wrap-function", "/@babel/helpers", "/@babel/plugin-transform-computed-properties", "/@babel/traverse", "/@jest/core/istanbul-lib-instrument", "/@jest/reporters/istanbul-lib-instrument", "/@jest/test-sequencer/istanbul-lib-instrument", "/@jest/transform/istanbul-lib-instrument", "/jest/istanbul-lib-instrument"], "_resolved": "https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz", "_shasum": "fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d", "_spec": "@babel/template@^7.27.2", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "deprecated": false, "description": "Generate an AST from a string template.", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-template", "license": "MIT", "main": "./lib/index.js", "name": "@babel/template", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-template"}, "type": "commonjs", "version": "7.27.2"}