{"_from": "@babel/helper-validator-option@^7.27.1", "_id": "@babel/helper-validator-option@7.27.1", "_inBundle": false, "_integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "_location": "/@babel/helper-validator-option", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-validator-option@^7.27.1", "name": "@babel/helper-validator-option", "escapedName": "@babel%2fhelper-validator-option", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "_shasum": "fa52f5b1e7db1ab049445b421c4471303897702f", "_spec": "@babel/helper-validator-option@^7.27.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/helper-compilation-targets", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Validate plugin/preset options", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-validator-option", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-option"}, "type": "commonjs", "version": "7.27.1"}