{"_from": "@babel/types@^7.28.2", "_id": "@babel/types@7.28.2", "_inBundle": false, "_integrity": "sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==", "_location": "/@babel/types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/types@^7.28.2", "name": "@babel/types", "escapedName": "@babel%2ftypes", "scope": "@babel", "rawSpec": "^7.28.2", "saveSpec": null, "fetchSpec": "^7.28.2"}, "_requiredBy": ["/@babel/core", "/@babel/generator", "/@babel/helper-annotate-as-pure", "/@babel/helper-environment-visitor", "/@babel/helper-member-expression-to-functions", "/@babel/helper-module-imports", "/@babel/helper-optimise-call-expression", "/@babel/helper-skip-transparent-expression-wrappers", "/@babel/helper-wrap-function", "/@babel/helpers", "/@babel/parser", "/@babel/template", "/@babel/traverse", "/@jest/core/istanbul-lib-instrument", "/@jest/core/jest-snapshot", "/@jest/reporters/istanbul-lib-instrument", "/@jest/reporters/jest-snapshot", "/@jest/test-sequencer/istanbul-lib-instrument", "/@jest/test-sequencer/jest-snapshot", "/@jest/transform/istanbul-lib-instrument", "/@types/babel__core", "/@types/babel__generator", "/@types/babel__template", "/@types/babel__traverse", "/babel-eslint", "/jest/istanbul-lib-instrument", "/jest/jest-snapshot"], "_resolved": "https://registry.npmmirror.com/@babel/types/-/types-7.28.2.tgz", "_shasum": "da9db0856a9a88e0a13b019881d7513588cf712b", "_spec": "@babel/types@^7.28.2", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/parser", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "deprecated": false, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "devDependencies": {"@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "glob": "^7.2.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-types", "license": "MIT", "main": "./lib/index.js", "name": "@babel/types", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "type": "commonjs", "types": "./lib/index-legacy.d.ts", "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "version": "7.28.2"}