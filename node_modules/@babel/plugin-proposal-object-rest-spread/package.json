{"_from": "@babel/plugin-proposal-object-rest-spread@^7.3.4", "_id": "@babel/plugin-proposal-object-rest-spread@7.20.7", "_inBundle": false, "_integrity": "sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg==", "_location": "/@babel/plugin-proposal-object-rest-spread", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-proposal-object-rest-spread@^7.3.4", "name": "@babel/plugin-proposal-object-rest-spread", "escapedName": "@babel%2fplugin-proposal-object-rest-spread", "scope": "@babel", "rawSpec": "^7.3.4", "saveSpec": null, "fetchSpec": "^7.3.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz", "_shasum": "aa662940ef425779c75534a5c41e9d936edc390a", "_spec": "@babel/plugin-proposal-object-rest-spread@^7.3.4", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/compat-data": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.20.7"}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.", "description": "Compile object rest and spread to ES5", "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/parser": "^7.20.7"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-object-rest-spread", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-object-rest-spread", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-object-rest-spread"}, "type": "commonjs", "version": "7.20.7"}