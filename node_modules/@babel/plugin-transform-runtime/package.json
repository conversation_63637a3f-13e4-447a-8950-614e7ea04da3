{"_from": "@babel/plugin-transform-runtime@^7.4.0", "_id": "@babel/plugin-transform-runtime@7.28.3", "_inBundle": false, "_integrity": "sha512-Y6ab1kGqZ0u42Zv/4a7l0l72n9DKP/MKoKWaUSBylrhNZO2prYuqFOLbn5aW5SIFXwSH93yfjbgllL8lxuGKLg==", "_location": "/@babel/plugin-transform-runtime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-runtime@^7.4.0", "name": "@babel/plugin-transform-runtime", "escapedName": "@babel%2fplugin-transform-runtime", "scope": "@babel", "rawSpec": "^7.4.0", "saveSpec": null, "fetchSpec": "^7.4.0"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.28.3.tgz", "_shasum": "f5990a1b2d2bde950ed493915e0719841c8d0eaa", "_spec": "@babel/plugin-transform-runtime@^7.4.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "browser": {"./lib/get-runtime-path/index.js": "./lib/get-runtime-path/browser.js", "./src/get-runtime-path/index.ts": "./src/get-runtime-path/browser.ts"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "semver": "^6.3.1"}, "deprecated": false, "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "devDependencies": {"@babel/core": "^7.28.3", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/helpers": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/runtime": "^7.28.3", "@babel/runtime-corejs3": "^7.28.3", "babel-plugin-polyfill-corejs3": "^0.13.0", "make-dir": "^2.1.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-runtime", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-runtime", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-runtime"}, "type": "commonjs", "version": "7.28.3"}