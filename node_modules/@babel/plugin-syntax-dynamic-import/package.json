{"_from": "@babel/plugin-syntax-dynamic-import@^7.0.0", "_id": "@babel/plugin-syntax-dynamic-import@7.8.3", "_inBundle": false, "_integrity": "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==", "_location": "/@babel/plugin-syntax-dynamic-import", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-dynamic-import@^7.0.0", "name": "@babel/plugin-syntax-dynamic-import", "escapedName": "@babel%2fplugin-syntax-dynamic-import", "scope": "@babel", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "_shasum": "62bf98b2da3cd21d626154fc96ee5b3cb68eacb3", "_spec": "@babel/plugin-syntax-dynamic-import@^7.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/babel-preset-app", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of import()", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-dynamic-import", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-dynamic-import"}, "version": "7.8.3"}