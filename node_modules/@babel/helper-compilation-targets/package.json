{"_from": "@babel/helper-compilation-targets@^7.27.2", "_id": "@babel/helper-compilation-targets@7.27.2", "_inBundle": false, "_integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "_location": "/@babel/helper-compilation-targets", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-compilation-targets@^7.27.2", "name": "@babel/helper-compilation-targets", "escapedName": "@babel%2fhelper-compilation-targets", "scope": "@babel", "rawSpec": "^7.27.2", "saveSpec": null, "fetchSpec": "^7.27.2"}, "_requiredBy": ["/@babel/core", "/@babel/helper-define-polyfill-provider", "/@babel/plugin-proposal-object-rest-spread", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-function-name"], "_resolved": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "_shasum": "46a0f6efab808d51d29ce96858dd10ce8732733d", "_spec": "@babel/helper-compilation-targets@^7.27.2", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "deprecated": false, "description": "Helper functions on Babel compilation targets", "devDependencies": {"@babel/helper-plugin-test-runner": "^7.27.1", "@types/lru-cache": "^5.1.1", "@types/semver": "^5.5.0"}, "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-compilation-targets", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-compilation-targets"}, "type": "commonjs", "version": "7.27.2"}