{"_from": "@babel/helper-define-polyfill-provider@^0.6.5", "_id": "@babel/helper-define-polyfill-provider@0.6.5", "_inBundle": false, "_integrity": "sha512-uJnGFcPsWQK8fvjgGP5LZUZZsYGIoPeRjSF5PGwrelYgq7Q15/Ft9NGFp1zglwgIv//W0uG4BevRuSJRyylZPg==", "_location": "/@babel/helper-define-polyfill-provider", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-define-polyfill-provider@^0.6.5", "name": "@babel/helper-define-polyfill-provider", "escapedName": "@babel%2fhelper-define-polyfill-provider", "scope": "@babel", "rawSpec": "^0.6.5", "saveSpec": null, "fetchSpec": "^0.6.5"}, "_requiredBy": ["/babel-plugin-polyfill-corejs2", "/babel-plugin-polyfill-corejs3", "/babel-plugin-polyfill-regenerator"], "_resolved": "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.5.tgz", "_shasum": "742ccf1cb003c07b48859fc9fa2c1bbe40e5f753", "_spec": "@babel/helper-define-polyfill-provider@^0.6.5", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/babel-plugin-polyfill-corejs2", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}, "deprecated": false, "description": "Babel helper to create your own polyfill provider", "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.7", "@babel/generator": "^7.27.5", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/traverse": "^7.27.7", "babel-loader": "^8.4.1", "rollup": "^2.79.2", "rollup-plugin-babel": "^4.4.0", "strip-ansi": "^6.0.1", "webpack": "^4.47.0", "webpack-cli": "^3.3.12"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/helper-define-polyfill-provider", "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-helper-define-polyfill-provider"}, "version": "0.6.5"}