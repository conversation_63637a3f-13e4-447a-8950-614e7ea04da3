{"_from": "@babel/core@^7.0.0", "_id": "@babel/core@7.28.3", "_inBundle": false, "_integrity": "sha512-yDBHV9kQNcr2/sUr9jghVyz9C3Y5G2zUM2H2lo+9mKv4sFgbA8s8Z9t8D1jiTkGoO/NoIfKMyKWr4s6CN23ZwQ==", "_location": "/@babel/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/core@^7.0.0", "name": "@babel/core", "escapedName": "@babel%2fcore", "scope": "@babel", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/@jest/core/jest-config", "/@jest/reporters/jest-config", "/@jest/test-sequencer/jest-config", "/@jest/transform", "/@vue/cli-plugin-babel", "/jest/jest-config"], "_resolved": "https://registry.npmmirror.com/@babel/core/-/core-7.28.3.tgz", "_shasum": "aceddde69c5d1def69b839d09efa3e3ff59c97cb", "_spec": "@babel/core@^7.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/cli-plugin-babel", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "browser": {"./lib/config/files/index.js": "./lib/config/files/index-browser.js", "./lib/config/resolve-targets.js": "./lib/config/resolve-targets-browser.js", "./lib/transform-file.js": "./lib/transform-file-browser.js", "./src/config/files/index.ts": "./src/config/files/index-browser.ts", "./src/config/resolve-targets.ts": "./src/config/resolve-targets-browser.ts", "./src/transform-file.ts": "./src/transform-file-browser.ts"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20core%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.28.3", "@babel/helpers": "^7.28.3", "@babel/parser": "^7.28.3", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.3", "@babel/types": "^7.28.2", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "deprecated": false, "description": "Babel compiler core.", "devDependencies": {"@babel/helper-transform-fixture-test-runner": "^7.28.3", "@babel/plugin-syntax-flow": "^7.27.1", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.28.3", "@babel/preset-typescript": "^7.27.1", "@jridgewell/trace-mapping": "^0.3.28", "@types/convert-source-map": "^2.0.0", "@types/debug": "^4.1.0", "@types/resolve": "^1.3.2", "@types/semver": "^5.4.0", "rimraf": "^3.0.0", "ts-node": "^11.0.0-beta.1", "tsx": "^4.20.3"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}, "homepage": "https://babel.dev/docs/en/next/babel-core", "keywords": ["6to5", "babel", "classes", "const", "es6", "harmony", "let", "modules", "transpile", "transpiler", "var", "babel-core", "compiler"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/core", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-core"}, "type": "commonjs", "version": "7.28.3"}