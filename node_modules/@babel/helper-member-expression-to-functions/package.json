{"_from": "@babel/helper-member-expression-to-functions@^7.27.1", "_id": "@babel/helper-member-expression-to-functions@7.27.1", "_inBundle": false, "_integrity": "sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==", "_location": "/@babel/helper-member-expression-to-functions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-member-expression-to-functions@^7.27.1", "name": "@babel/helper-member-expression-to-functions", "escapedName": "@babel%2fhelper-member-expression-to-functions", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-replace-supers"], "_resolved": "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz", "_shasum": "ea1211276be93e798ce19037da6f06fbb994fa44", "_spec": "@babel/helper-member-expression-to-functions@^7.27.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "deprecated": false, "description": "Helper function to replace certain member expressions with function calls", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-member-expression-to-functions", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-member-expression-to-functions", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-member-expression-to-functions"}, "type": "commonjs", "version": "7.27.1"}