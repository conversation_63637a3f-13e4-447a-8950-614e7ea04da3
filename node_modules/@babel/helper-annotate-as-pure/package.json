{"_from": "@babel/helper-annotate-as-pure@^7.27.3", "_id": "@babel/helper-annotate-as-pure@7.27.3", "_inBundle": false, "_integrity": "sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==", "_location": "/@babel/helper-annotate-as-pure", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-annotate-as-pure@^7.27.3", "name": "@babel/helper-annotate-as-pure", "escapedName": "@babel%2fhelper-annotate-as-pure", "scope": "@babel", "rawSpec": "^7.27.3", "saveSpec": null, "fetchSpec": "^7.27.3"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-create-regexp-features-plugin", "/@babel/helper-remap-async-to-generator", "/@babel/plugin-transform-classes"], "_resolved": "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz", "_shasum": "f31fd86b915fc4daf1f3ac6976c59be7084ed9c5", "_spec": "@babel/helper-annotate-as-pure@^7.27.3", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.27.3"}, "deprecated": false, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "devDependencies": {"@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-annotate-as-pure", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-annotate-as-pure"}, "type": "commonjs", "version": "7.27.3"}