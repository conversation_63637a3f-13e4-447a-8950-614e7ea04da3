{"_from": "@babel/plugin-transform-shorthand-properties@^7.2.0", "_id": "@babel/plugin-transform-shorthand-properties@7.27.1", "_inBundle": false, "_integrity": "sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==", "_location": "/@babel/plugin-transform-shorthand-properties", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-shorthand-properties@^7.2.0", "name": "@babel/plugin-transform-shorthand-properties", "escapedName": "@babel%2fplugin-transform-shorthand-properties", "scope": "@babel", "rawSpec": "^7.2.0", "saveSpec": null, "fetchSpec": "^7.2.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "_shasum": "532abdacdec87bfee1e0ef8e2fcdee543fe32b90", "_spec": "@babel/plugin-transform-shorthand-properties@^7.2.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Compile ES2015 shorthand properties to ES5", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-shorthand-properties", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "type": "commonjs", "version": "7.27.1"}