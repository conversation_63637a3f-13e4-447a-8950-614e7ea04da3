{"_from": "@babel/traverse@^7.28.3", "_id": "@babel/traverse@7.28.3", "_inBundle": false, "_integrity": "sha512-7w4kZYHneL3A6NP2nxzHvT3HCZ7puDZZjFMqDpBPECub79sTtSO5CGXDkKrTQq8ksAwfD/XI2MRFX23njdDaIQ==", "_location": "/@babel/traverse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/traverse@^7.28.3", "name": "@babel/traverse", "escapedName": "@babel%2ftraverse", "scope": "@babel", "rawSpec": "^7.28.3", "saveSpec": null, "fetchSpec": "^7.28.3"}, "_requiredBy": ["/@babel/core", "/@babel/helper-create-class-features-plugin", "/@babel/helper-member-expression-to-functions", "/@babel/helper-module-imports", "/@babel/helper-module-transforms", "/@babel/helper-remap-async-to-generator", "/@babel/helper-replace-supers", "/@babel/helper-skip-transparent-expression-wrappers", "/@babel/helper-wrap-function", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-destructuring", "/@babel/plugin-transform-function-name", "/@babel/plugin-transform-modules-systemjs", "/@jest/core/istanbul-lib-instrument", "/@jest/core/jest-jasmine2", "/@jest/reporters/istanbul-lib-instrument", "/@jest/reporters/jest-jasmine2", "/@jest/test-sequencer/istanbul-lib-instrument", "/@jest/test-sequencer/jest-jasmine2", "/@jest/transform/istanbul-lib-instrument", "/babel-eslint", "/jest/istanbul-lib-instrument", "/jest/jest-jasmine2"], "_resolved": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.28.3.tgz", "_shasum": "6911a10795d2cce43ec6a28cffc440cca2593434", "_spec": "@babel/traverse@^7.28.3", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20traverse%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.3", "@babel/template": "^7.27.2", "@babel/types": "^7.28.2", "debug": "^4.3.1"}, "deprecated": false, "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "devDependencies": {"@babel/core": "^7.28.3", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-traverse", "license": "MIT", "main": "./lib/index.js", "name": "@babel/traverse", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-traverse"}, "type": "commonjs", "version": "7.28.3"}