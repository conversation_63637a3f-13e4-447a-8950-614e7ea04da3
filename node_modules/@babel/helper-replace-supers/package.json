{"_from": "@babel/helper-replace-supers@^7.27.1", "_id": "@babel/helper-replace-supers@7.27.1", "_inBundle": false, "_integrity": "sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==", "_location": "/@babel/helper-replace-supers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-replace-supers@^7.27.1", "name": "@babel/helper-replace-supers", "escapedName": "@babel%2fhelper-replace-supers", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-object-super"], "_resolved": "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz", "_shasum": "b1ed2d634ce3bdb730e4b52de30f8cccfd692bc0", "_spec": "@babel/helper-replace-supers@^7.27.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "deprecated": false, "description": "Helper function to replace supers", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-replace-supers", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-replace-supers"}, "type": "commonjs", "version": "7.27.1"}