{"_from": "@babel/plugin-proposal-decorators@^7.1.0", "_id": "@babel/plugin-proposal-decorators@7.28.0", "_inBundle": false, "_integrity": "sha512-zOiZqvANjWDUaUS9xMxbMcK/Zccztbe/6ikvUXaG9nsPH3w6qh5UaPGAnirI/WhIbZ8m3OHU0ReyPrknG+ZKeg==", "_location": "/@babel/plugin-proposal-decorators", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-proposal-decorators@^7.1.0", "name": "@babel/plugin-proposal-decorators", "escapedName": "@babel%2fplugin-proposal-decorators", "scope": "@babel", "rawSpec": "^7.1.0", "saveSpec": null, "fetchSpec": "^7.1.0"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.28.0.tgz", "_shasum": "419c8acc31088e05a774344c021800f7ddc39bf0", "_spec": "@babel/plugin-proposal-decorators@^7.1.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "deprecated": false, "description": "Compile class and object decorators to ES5", "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/traverse": "^7.28.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.8", "object.getownpropertydescriptors": "^2.1.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "keywords": ["babel", "babel-plugin", "decorators"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-decorators", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-decorators"}, "type": "commonjs", "version": "7.28.0"}