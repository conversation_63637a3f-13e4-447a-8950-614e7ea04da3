{"_from": "@babel/helpers@^7.28.3", "_id": "@babel/helpers@7.28.3", "_inBundle": false, "_integrity": "sha512-PTNtvUQihsAsDHMOP5pfobP8C6CM4JWXmP8DrEIt46c3r2bf87Ua1zoqevsMo9g+tWDwgWrFP5EIxuBx5RudAw==", "_location": "/@babel/helpers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helpers@^7.28.3", "name": "@babel/helpers", "escapedName": "@babel%2fhelpers", "scope": "@babel", "rawSpec": "^7.28.3", "saveSpec": null, "fetchSpec": "^7.28.3"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.28.3.tgz", "_shasum": "b83156c0a2232c133d1b535dd5d3452119c7e441", "_spec": "@babel/helpers@^7.28.3", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.2"}, "deprecated": false, "description": "Collection of helper functions used by Babel transforms.", "devDependencies": {"@babel/generator": "^7.28.3", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/parser": "^7.28.3", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helpers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "type": "commonjs", "version": "7.28.3"}