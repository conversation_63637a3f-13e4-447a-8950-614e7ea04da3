{"_from": "@babel/helper-plugin-utils@^7.18.6", "_id": "@babel/helper-plugin-utils@7.27.1", "_inBundle": false, "_integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "_location": "/@babel/helper-plugin-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-plugin-utils@^7.18.6", "name": "@babel/helper-plugin-utils", "escapedName": "@babel%2fhelper-plugin-utils", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/helper-define-polyfill-provider", "/@babel/plugin-proposal-async-generator-functions", "/@babel/plugin-proposal-class-properties", "/@babel/plugin-proposal-decorators", "/@babel/plugin-proposal-json-strings", "/@babel/plugin-proposal-object-rest-spread", "/@babel/plugin-proposal-optional-catch-binding", "/@babel/plugin-proposal-unicode-property-regex", "/@babel/plugin-syntax-async-generators", "/@babel/plugin-syntax-decorators", "/@babel/plugin-syntax-dynamic-import", "/@babel/plugin-syntax-json-strings", "/@babel/plugin-syntax-jsx", "/@babel/plugin-syntax-object-rest-spread", "/@babel/plugin-syntax-optional-catch-binding", "/@babel/plugin-transform-arrow-functions", "/@babel/plugin-transform-async-to-generator", "/@babel/plugin-transform-block-scoped-functions", "/@babel/plugin-transform-block-scoping", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-computed-properties", "/@babel/plugin-transform-destructuring", "/@babel/plugin-transform-dotall-regex", "/@babel/plugin-transform-duplicate-keys", "/@babel/plugin-transform-exponentiation-operator", "/@babel/plugin-transform-for-of", "/@babel/plugin-transform-function-name", "/@babel/plugin-transform-literals", "/@babel/plugin-transform-modules-amd", "/@babel/plugin-transform-modules-commonjs", "/@babel/plugin-transform-modules-systemjs", "/@babel/plugin-transform-modules-umd", "/@babel/plugin-transform-named-capturing-groups-regex", "/@babel/plugin-transform-new-target", "/@babel/plugin-transform-object-super", "/@babel/plugin-transform-parameters", "/@babel/plugin-transform-regenerator", "/@babel/plugin-transform-runtime", "/@babel/plugin-transform-shorthand-properties", "/@babel/plugin-transform-spread", "/@babel/plugin-transform-sticky-regex", "/@babel/plugin-transform-template-literals", "/@babel/plugin-transform-typeof-symbol", "/@babel/plugin-transform-unicode-regex", "/@babel/preset-env", "/@jest/core/babel-plugin-istanbul", "/@jest/reporters/babel-plugin-istanbul", "/@jest/test-sequencer/babel-plugin-istanbul", "/@jest/transform/babel-plugin-istanbul", "/jest/babel-plugin-istanbul"], "_resolved": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "_shasum": "ddb2f876534ff8013e6c2b299bf4d39b3c51d44c", "_spec": "@babel/helper-plugin-utils@^7.18.6", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/plugin-proposal-class-properties", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "General utilities for plugins to use", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-plugin-utils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-plugin-utils"}, "type": "commonjs", "version": "7.27.1"}