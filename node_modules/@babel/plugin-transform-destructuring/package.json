{"_from": "@babel/plugin-transform-destructuring@^7.2.0", "_id": "@babel/plugin-transform-destructuring@7.28.0", "_inBundle": false, "_integrity": "sha512-v1nrSMBiKcodhsyJ4Gf+Z0U/yawmJDBOTpEB3mcQY52r9RIyPneGyAS/yM6seP/8I+mWI3elOMtT5dB8GJVs+A==", "_location": "/@babel/plugin-transform-destructuring", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-destructuring@^7.2.0", "name": "@babel/plugin-transform-destructuring", "escapedName": "@babel%2fplugin-transform-destructuring", "scope": "@babel", "rawSpec": "^7.2.0", "saveSpec": null, "fetchSpec": "^7.2.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.28.0.tgz", "_shasum": "0f156588f69c596089b7d5b06f5af83d9aa7f97a", "_spec": "@babel/plugin-transform-destructuring@^7.2.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.28.0"}, "deprecated": false, "description": "Compile ES2015 destructuring to ES5", "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-destructuring", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-destructuring", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-destructuring"}, "type": "commonjs", "version": "7.28.0"}