{"_from": "@babel/plugin-transform-new-target@^7.0.0", "_id": "@babel/plugin-transform-new-target@7.27.1", "_inBundle": false, "_integrity": "sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==", "_location": "/@babel/plugin-transform-new-target", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-new-target@^7.0.0", "name": "@babel/plugin-transform-new-target", "escapedName": "@babel%2fplugin-transform-new-target", "scope": "@babel", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz", "_shasum": "259c43939728cad1706ac17351b7e6a7bea1abeb", "_spec": "@babel/plugin-transform-new-target@^7.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Transforms new.target meta property", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-new-target", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-new-target", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-new-target"}, "type": "commonjs", "version": "7.27.1"}