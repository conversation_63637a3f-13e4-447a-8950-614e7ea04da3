{"_from": "@babel/plugin-proposal-async-generator-functions@^7.2.0", "_id": "@babel/plugin-proposal-async-generator-functions@7.20.7", "_inBundle": false, "_integrity": "sha512-xMbiLsn/8RK7Wq7VeVytytS2L6qE69bXPB10YCmMdDZbKF4okCqY74pI/jJQ/8U0b/F6NrT2+14b8/P9/3AMGA==", "_location": "/@babel/plugin-proposal-async-generator-functions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-proposal-async-generator-functions@^7.2.0", "name": "@babel/plugin-proposal-async-generator-functions", "escapedName": "@babel%2fplugin-proposal-async-generator-functions", "scope": "@babel", "rawSpec": "^7.2.0", "saveSpec": null, "fetchSpec": "^7.2.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.7.tgz", "_shasum": "bfb7276d2d573cb67ba379984a2334e262ba5326", "_spec": "@babel/plugin-proposal-async-generator-functions@^7.2.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-async-generator-functions instead.", "description": "Turn async generator functions into ES2015 generators", "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "babel-plugin-polyfill-corejs3": "^0.6.0", "core-js-pure": "^3.25.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-async-generator-functions", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-async-generator-functions", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-async-generator-functions"}, "type": "commonjs", "version": "7.20.7"}