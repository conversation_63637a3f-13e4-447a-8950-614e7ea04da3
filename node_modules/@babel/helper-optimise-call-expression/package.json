{"_from": "@babel/helper-optimise-call-expression@^7.27.1", "_id": "@babel/helper-optimise-call-expression@7.27.1", "_inBundle": false, "_integrity": "sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==", "_location": "/@babel/helper-optimise-call-expression", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-optimise-call-expression@^7.27.1", "name": "@babel/helper-optimise-call-expression", "escapedName": "@babel%2fhelper-optimise-call-expression", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-replace-supers"], "_resolved": "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz", "_shasum": "c65221b61a643f3e62705e5dd2b5f115e35f9200", "_spec": "@babel/helper-optimise-call-expression@^7.27.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.27.1"}, "deprecated": false, "description": "Helper function to optimise call expression", "devDependencies": {"@babel/generator": "^7.27.1", "@babel/parser": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-optimise-call-expression", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-optimise-call-expression"}, "type": "commonjs", "version": "7.27.1"}