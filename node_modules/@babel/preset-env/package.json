{"_from": "@babel/preset-env@^7.0.0 < 7.4.0", "_id": "@babel/preset-env@7.3.4", "_inBundle": false, "_integrity": "sha512-2mwqfYMK8weA0g0uBKOt4FE3iEodiHy9/CW0b+nWXcbL+pGzLx8ESYc+j9IIxr6LTDHWKgPm71i9smo02bw+gA==", "_location": "/@babel/preset-env", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/preset-env@^7.0.0 < 7.4.0", "name": "@babel/preset-env", "escapedName": "@babel%2fpreset-env", "scope": "@babel", "rawSpec": "^7.0.0 < 7.4.0", "saveSpec": null, "fetchSpec": "^7.0.0 < 7.4.0"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmmirror.com/@babel/preset-env/-/preset-env-7.3.4.tgz", "_shasum": "887cf38b6d23c82f19b5135298bdb160062e33e1", "_spec": "@babel/preset-env@^7.0.0 < 7.4.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/babel-preset-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-async-generator-functions": "^7.2.0", "@babel/plugin-proposal-json-strings": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.3.4", "@babel/plugin-proposal-optional-catch-binding": "^7.2.0", "@babel/plugin-proposal-unicode-property-regex": "^7.2.0", "@babel/plugin-syntax-async-generators": "^7.2.0", "@babel/plugin-syntax-json-strings": "^7.2.0", "@babel/plugin-syntax-object-rest-spread": "^7.2.0", "@babel/plugin-syntax-optional-catch-binding": "^7.2.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-async-to-generator": "^7.3.4", "@babel/plugin-transform-block-scoped-functions": "^7.2.0", "@babel/plugin-transform-block-scoping": "^7.3.4", "@babel/plugin-transform-classes": "^7.3.4", "@babel/plugin-transform-computed-properties": "^7.2.0", "@babel/plugin-transform-destructuring": "^7.2.0", "@babel/plugin-transform-dotall-regex": "^7.2.0", "@babel/plugin-transform-duplicate-keys": "^7.2.0", "@babel/plugin-transform-exponentiation-operator": "^7.2.0", "@babel/plugin-transform-for-of": "^7.2.0", "@babel/plugin-transform-function-name": "^7.2.0", "@babel/plugin-transform-literals": "^7.2.0", "@babel/plugin-transform-modules-amd": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/plugin-transform-modules-systemjs": "^7.3.4", "@babel/plugin-transform-modules-umd": "^7.2.0", "@babel/plugin-transform-named-capturing-groups-regex": "^7.3.0", "@babel/plugin-transform-new-target": "^7.0.0", "@babel/plugin-transform-object-super": "^7.2.0", "@babel/plugin-transform-parameters": "^7.2.0", "@babel/plugin-transform-regenerator": "^7.3.4", "@babel/plugin-transform-shorthand-properties": "^7.2.0", "@babel/plugin-transform-spread": "^7.2.0", "@babel/plugin-transform-sticky-regex": "^7.2.0", "@babel/plugin-transform-template-literals": "^7.2.0", "@babel/plugin-transform-typeof-symbol": "^7.2.0", "@babel/plugin-transform-unicode-regex": "^7.2.0", "browserslist": "^4.3.4", "invariant": "^2.2.2", "js-levenshtein": "^1.1.3", "semver": "^5.3.0"}, "deprecated": false, "description": "A Babel preset for each environment.", "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/helper-fixtures": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0", "caniuse-db": "1.0.30000851", "compat-table": "github:kangax/compat-table#1e7b377fbdda9243cf9602872fcb493cdbdd565f", "electron-to-chromium": "1.3.79"}, "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "@babel/preset-env", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-env"}, "scripts": {"build-data": "node ./scripts/build-data.js; node ./scripts/build-modules-support.js"}, "version": "7.3.4"}