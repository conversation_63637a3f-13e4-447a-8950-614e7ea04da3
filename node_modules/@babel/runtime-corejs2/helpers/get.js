var _Reflect$get = require("core-js/library/fn/reflect/get.js");
var _Object$getOwnPropertyDescriptor = require("core-js/library/fn/object/get-own-property-descriptor.js");
var superPropBase = require("./superPropBase.js");
function _get() {
  return module.exports = _get = "undefined" != typeof Reflect && _Reflect$get ? _Reflect$get.bind() : function (e, t, r) {
    var p = superPropBase(e, t);
    if (p) {
      var n = _Object$getOwnPropertyDescriptor(p, t);
      return n.get ? n.get.call(arguments.length < 3 ? e : r) : n.value;
    }
  }, module.exports.__esModule = true, module.exports["default"] = module.exports, _get.apply(null, arguments);
}
module.exports = _get, module.exports.__esModule = true, module.exports["default"] = module.exports;