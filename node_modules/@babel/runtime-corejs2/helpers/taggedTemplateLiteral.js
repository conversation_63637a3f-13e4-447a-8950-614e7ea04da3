var _Object$freeze = require("core-js/library/fn/object/freeze.js");
var _Object$defineProperties = require("core-js/library/fn/object/define-properties.js");
function _taggedTemplateLiteral(e, t) {
  return t || (t = e.slice(0)), _Object$freeze(_Object$defineProperties(e, {
    raw: {
      value: _Object$freeze(t)
    }
  }));
}
module.exports = _taggedTemplateLiteral, module.exports.__esModule = true, module.exports["default"] = module.exports;