import _Object$setPrototypeOf from "core-js/library/fn/object/set-prototype-of.js";
import _Object$getPrototypeOf from "core-js/library/fn/object/get-prototype-of.js";
function _getPrototypeOf(t) {
  return _getPrototypeOf = _Object$setPrototypeOf ? _Object$getPrototypeOf.bind() : function (t) {
    return t.__proto__ || _Object$getPrototypeOf(t);
  }, _getPrototypeOf(t);
}
export { _getPrototypeOf as default };