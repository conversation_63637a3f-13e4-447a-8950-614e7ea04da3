import _Object$keys from "core-js/library/fn/object/keys.js";
import _Object$getOwnPropertySymbols from "core-js/library/fn/object/get-own-property-symbols.js";
import _Object$getOwnPropertyDescriptor from "core-js/library/fn/object/get-own-property-descriptor.js";
import defineProperty from "./defineProperty.js";
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? Object(arguments[r]) : {},
      o = _Object$keys(t);
    "function" == typeof _Object$getOwnPropertySymbols && o.push.apply(o, _Object$getOwnPropertySymbols(t).filter(function (e) {
      return _Object$getOwnPropertyDescriptor(t, e).enumerable;
    })), o.forEach(function (r) {
      defineProperty(e, r, t[r]);
    });
  }
  return e;
}
export { _objectSpread as default };