{"_from": "@babel/plugin-transform-named-capturing-groups-regex@^7.3.0", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.27.1", "_inBundle": false, "_integrity": "sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==", "_location": "/@babel/plugin-transform-named-capturing-groups-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-named-capturing-groups-regex@^7.3.0", "name": "@babel/plugin-transform-named-capturing-groups-regex", "escapedName": "@babel%2fplugin-transform-named-capturing-groups-regex", "scope": "@babel", "rawSpec": "^7.3.0", "saveSpec": null, "fetchSpec": "^7.3.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "_shasum": "f32b8f7818d8fc0cc46ee20a8ef75f071af976e1", "_spec": "@babel/plugin-transform-named-capturing-groups-regex@^7.3.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Compile regular expressions using named groups to ES5.", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "core-js": "^3.30.2"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-named-capturing-groups-regex", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "type": "commonjs", "version": "7.27.1"}