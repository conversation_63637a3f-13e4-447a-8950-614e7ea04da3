{"_from": "@babel/helper-environment-visitor@^7.18.9", "_id": "@babel/helper-environment-visitor@7.24.7", "_inBundle": false, "_integrity": "sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ==", "_location": "/@babel/helper-environment-visitor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-environment-visitor@^7.18.9", "name": "@babel/helper-environment-visitor", "escapedName": "@babel%2fhelper-environment-visitor", "scope": "@babel", "rawSpec": "^7.18.9", "saveSpec": null, "fetchSpec": "^7.18.9"}, "_requiredBy": ["/@babel/plugin-proposal-async-generator-functions"], "_resolved": "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.24.7.tgz", "_shasum": "4b31ba9551d1f90781ba83491dd59cf9b269f7d9", "_spec": "@babel/helper-environment-visitor@^7.18.9", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/plugin-proposal-async-generator-functions", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.24.7"}, "deprecated": false, "description": "Helper visitor to only visit nodes in the current 'this' context", "devDependencies": {"@babel/traverse": "^7.24.7"}, "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-environment-visitor", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-environment-visitor", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-environment-visitor"}, "type": "commonjs", "version": "7.24.7"}