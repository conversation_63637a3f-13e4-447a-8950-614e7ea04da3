{"_from": "@babel/plugin-transform-classes@^7.3.4", "_id": "@babel/plugin-transform-classes@7.28.3", "_inBundle": false, "_integrity": "sha512-DoEWC5SuxuARF2KdKmGUq3ghfPMO6ZzR12Dnp5gubwbeWJo4dbNWXJPVlwvh4Zlq6Z7YVvL8VFxeSOJgjsx4Sg==", "_location": "/@babel/plugin-transform-classes", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-classes@^7.3.4", "name": "@babel/plugin-transform-classes", "escapedName": "@babel%2fplugin-transform-classes", "scope": "@babel", "rawSpec": "^7.3.4", "saveSpec": null, "fetchSpec": "^7.3.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.28.3.tgz", "_shasum": "598297260343d0edbd51cb5f5075e07dee91963a", "_spec": "@babel/plugin-transform-classes@^7.3.4", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-globals": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.28.3"}, "deprecated": false, "description": "Compile ES2015 classes to ES5", "devDependencies": {"@babel/core": "^7.28.3", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-classes", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-classes"}, "type": "commonjs", "version": "7.28.3"}