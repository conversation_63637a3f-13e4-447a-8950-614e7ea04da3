{"_from": "@babel/plugin-transform-async-to-generator@^7.3.4", "_id": "@babel/plugin-transform-async-to-generator@7.27.1", "_inBundle": false, "_integrity": "sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==", "_location": "/@babel/plugin-transform-async-to-generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-async-to-generator@^7.3.4", "name": "@babel/plugin-transform-async-to-generator", "escapedName": "@babel%2fplugin-transform-async-to-generator", "scope": "@babel", "rawSpec": "^7.3.4", "saveSpec": null, "fetchSpec": "^7.3.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz", "_shasum": "9a93893b9379b39466c74474f55af03de78c66e7", "_spec": "@babel/plugin-transform-async-to-generator@^7.3.4", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "deprecated": false, "description": "Turn async functions into ES2015 generators", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-async-to-generator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "type": "commonjs", "version": "7.27.1"}