{"_from": "@babel/helper-create-regexp-features-plugin@^7.18.6", "_id": "@babel/helper-create-regexp-features-plugin@7.27.1", "_inBundle": false, "_integrity": "sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==", "_location": "/@babel/helper-create-regexp-features-plugin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-create-regexp-features-plugin@^7.18.6", "name": "@babel/helper-create-regexp-features-plugin", "escapedName": "@babel%2fhelper-create-regexp-features-plugin", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/plugin-proposal-unicode-property-regex", "/@babel/plugin-transform-dotall-regex", "/@babel/plugin-transform-named-capturing-groups-regex", "/@babel/plugin-transform-unicode-regex"], "_resolved": "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "_shasum": "05b0882d97ba1d4d03519e4bce615d70afa18c53", "_spec": "@babel/helper-create-regexp-features-plugin@^7.18.6", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/plugin-proposal-unicode-property-regex", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "deprecated": false, "description": "Compile ESNext Regular Expressions to ES5", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-create-regexp-features-plugin", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "type": "commonjs", "version": "7.27.1"}