{"_from": "@babel/plugin-transform-parameters@^7.2.0", "_id": "@babel/plugin-transform-parameters@7.27.7", "_inBundle": false, "_integrity": "sha512-qBkYTYCb76RRxUM6CcZA5KRu8K4SM8ajzVeUgVdMVO9NN9uI/GaVmBg/WKJJGnNokV9SY8FxNOVWGXzqzUidBg==", "_location": "/@babel/plugin-transform-parameters", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-parameters@^7.2.0", "name": "@babel/plugin-transform-parameters", "escapedName": "@babel%2fplugin-transform-parameters", "scope": "@babel", "rawSpec": "^7.2.0", "saveSpec": null, "fetchSpec": "^7.2.0"}, "_requiredBy": ["/@babel/plugin-proposal-object-rest-spread", "/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.7.tgz", "_shasum": "1fd2febb7c74e7d21cf3b05f7aebc907940af53a", "_spec": "@babel/plugin-transform-parameters@^7.2.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Compile ES2015 default and rest parameters to ES5", "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-parameters", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-parameters"}, "type": "commonjs", "version": "7.27.7"}