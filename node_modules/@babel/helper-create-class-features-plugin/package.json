{"_from": "@babel/helper-create-class-features-plugin@^7.18.6", "_id": "@babel/helper-create-class-features-plugin@7.28.3", "_inBundle": false, "_integrity": "sha512-V9f6ZFIYSLNEbuGA/92uOvYsGCJNsuA8ESZ4ldc09bWk/j8H8TKiPw8Mk1eG6olpnO0ALHJmYfZvF4MEE4gajg==", "_location": "/@babel/helper-create-class-features-plugin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-create-class-features-plugin@^7.18.6", "name": "@babel/helper-create-class-features-plugin", "escapedName": "@babel%2fhelper-create-class-features-plugin", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/plugin-proposal-class-properties", "/@babel/plugin-proposal-decorators"], "_resolved": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.28.3.tgz", "_shasum": "3e747434ea007910c320c4d39a6b46f20f371d46", "_spec": "@babel/helper-create-class-features-plugin@^7.18.6", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/plugin-proposal-class-properties", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.28.3", "semver": "^6.3.1"}, "deprecated": false, "description": "Compile class public and private fields, private methods and decorators to ES6", "devDependencies": {"@babel/core": "^7.28.3", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/preset-env": "^7.28.3", "@types/charcodes": "^0.2.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-create-class-features-plugin", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-class-features-plugin"}, "type": "commonjs", "version": "7.28.3"}