{"# dependencies": "This package doesn't actually have runtime dependencies. @babel/types is only needed for type definitions.", "_from": "@babel/parser@^7.23.5", "_id": "@babel/parser@7.28.3", "_inBundle": false, "_integrity": "sha512-7+Ey1mAgYqFAx2h0RuoxcQT5+MlG3GTV0TQrgr7/ZliKsm/MNDxVVutlWaziMq7wJNAz8MTqz55XLpWvva6StA==", "_location": "/@babel/parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/parser@^7.23.5", "name": "@babel/parser", "escapedName": "@babel%2fparser", "scope": "@babel", "rawSpec": "^7.23.5", "saveSpec": null, "fetchSpec": "^7.23.5"}, "_requiredBy": ["/@babel/core", "/@babel/generator", "/@babel/template", "/@babel/traverse", "/@jest/core/istanbul-lib-instrument", "/@jest/reporters/istanbul-lib-instrument", "/@jest/test-sequencer/istanbul-lib-instrument", "/@jest/transform/istanbul-lib-instrument", "/@types/babel__core", "/@types/babel__template", "/@vue/compiler-sfc", "/babel-eslint", "/jest/istanbul-lib-instrument"], "_resolved": "https://registry.npmmirror.com/@babel/parser/-/parser-7.28.3.tgz", "_shasum": "d2d25b814621bca5fe9d172bc93792547e7a2a71", "_spec": "@babel/parser@^7.23.5", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/compiler-sfc", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bin": {"parser": "bin/babel-parser.js"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A+parser+%28babylon%29%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.28.2"}, "deprecated": false, "description": "A JavaScript parser", "devDependencies": {"@babel/code-frame": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-fixtures": "^7.28.0", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.0.0"}, "files": ["bin", "lib", "typings/babel-parser.d.ts", "index.cjs"], "homepage": "https://babel.dev/docs/en/next/babel-parser", "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "type": "commonjs", "types": "./typings/babel-parser.d.ts", "version": "7.28.3"}