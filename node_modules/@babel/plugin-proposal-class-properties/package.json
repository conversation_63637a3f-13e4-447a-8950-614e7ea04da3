{"_from": "@babel/plugin-proposal-class-properties@^7.0.0", "_id": "@babel/plugin-proposal-class-properties@7.18.6", "_inBundle": false, "_integrity": "sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==", "_location": "/@babel/plugin-proposal-class-properties", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-proposal-class-properties@^7.0.0", "name": "@babel/plugin-proposal-class-properties", "escapedName": "@babel%2fplugin-proposal-class-properties", "scope": "@babel", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "_shasum": "b110f59741895f7ec21a6fff696ec46265c446a3", "_spec": "@babel/plugin-proposal-class-properties@^7.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-class-properties", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "type": "commonjs", "version": "7.18.6"}