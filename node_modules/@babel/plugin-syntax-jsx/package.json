{"_from": "@babel/plugin-syntax-jsx@^7.0.0", "_id": "@babel/plugin-syntax-jsx@7.27.1", "_inBundle": false, "_integrity": "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==", "_location": "/@babel/plugin-syntax-jsx", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-jsx@^7.0.0", "name": "@babel/plugin-syntax-jsx", "escapedName": "@babel%2fplugin-syntax-jsx", "scope": "@babel", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/@vue/babel-plugin-transform-vue-jsx", "/@vue/babel-preset-app", "/@vue/babel-sugar-composition-api-inject-h", "/@vue/babel-sugar-composition-api-render-instance", "/@vue/babel-sugar-functional-vue", "/@vue/babel-sugar-inject-h", "/@vue/babel-sugar-v-model", "/@vue/babel-sugar-v-on"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "_shasum": "2f9beb5eff30fa507c5532d107daac7b888fa34c", "_spec": "@babel/plugin-syntax-jsx@^7.0.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@vue/babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Allow parsing of jsx", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-jsx", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-jsx"}, "type": "commonjs", "version": "7.27.1"}