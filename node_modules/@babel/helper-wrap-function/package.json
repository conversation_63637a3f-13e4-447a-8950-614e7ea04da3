{"_from": "@babel/helper-wrap-function@^7.27.1", "_id": "@babel/helper-wrap-function@7.28.3", "_inBundle": false, "_integrity": "sha512-zdf983tNfLZFletc0RRXYrHrucBEg95NIFMkn6K9dbeMYnsgHaSBGcQqdsCSStG2PYwRre0Qc2NNSCXbG+xc6g==", "_location": "/@babel/helper-wrap-function", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-wrap-function@^7.27.1", "name": "@babel/helper-wrap-function", "escapedName": "@babel%2fhelper-wrap-function", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/helper-remap-async-to-generator"], "_resolved": "https://registry.npmmirror.com/@babel/helper-wrap-function/-/helper-wrap-function-7.28.3.tgz", "_shasum": "fe4872092bc1438ffd0ce579e6f699609f9d0a7a", "_spec": "@babel/helper-wrap-function@^7.27.1", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/helper-remap-async-to-generator", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.27.2", "@babel/traverse": "^7.28.3", "@babel/types": "^7.28.2"}, "deprecated": false, "description": "Helper to wrap functions inside a function call.", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-wrap-function", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "type": "commonjs", "version": "7.28.3"}