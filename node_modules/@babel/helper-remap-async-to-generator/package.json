{"_from": "@babel/helper-remap-async-to-generator@^7.18.9", "_id": "@babel/helper-remap-async-to-generator@7.27.1", "_inBundle": false, "_integrity": "sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==", "_location": "/@babel/helper-remap-async-to-generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-remap-async-to-generator@^7.18.9", "name": "@babel/helper-remap-async-to-generator", "escapedName": "@babel%2fhelper-remap-async-to-generator", "scope": "@babel", "rawSpec": "^7.18.9", "saveSpec": null, "fetchSpec": "^7.18.9"}, "_requiredBy": ["/@babel/plugin-proposal-async-generator-functions", "/@babel/plugin-transform-async-to-generator"], "_resolved": "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "_shasum": "4601d5c7ce2eb2aea58328d43725523fcd362ce6", "_spec": "@babel/helper-remap-async-to-generator@^7.18.9", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/plugin-proposal-async-generator-functions", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "deprecated": false, "description": "Helper function to remap async functions to generators", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-remap-async-to-generator", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-remap-async-to-generator"}, "type": "commonjs", "version": "7.27.1"}