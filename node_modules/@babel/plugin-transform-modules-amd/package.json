{"_from": "@babel/plugin-transform-modules-amd@^7.2.0", "_id": "@babel/plugin-transform-modules-amd@7.27.1", "_inBundle": false, "_integrity": "sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==", "_location": "/@babel/plugin-transform-modules-amd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-modules-amd@^7.2.0", "name": "@babel/plugin-transform-modules-amd", "escapedName": "@babel%2fplugin-transform-modules-amd", "scope": "@babel", "rawSpec": "^7.2.0", "saveSpec": null, "fetchSpec": "^7.2.0"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz", "_shasum": "a4145f9d87c2291fe2d05f994b65dba4e3e7196f", "_spec": "@babel/plugin-transform-modules-amd@^7.2.0", "_where": "/Users/<USER>/develop/vue/project-sky-admin-vue-ts/node_modules/@babel/preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "This plugin transforms ES2015 modules to AMD", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-external-helpers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-modules-amd", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-amd"}, "type": "commonjs", "version": "7.27.1"}